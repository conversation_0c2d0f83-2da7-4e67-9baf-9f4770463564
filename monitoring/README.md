# Monitoring Stack for IELTS Toolkit

This directory contains configuration files for the IELTS Toolkit monitoring stack, which includes:

- **Prometheus**: Time-series database for metrics collection
- **Grafana**: Visualization and dashboarding
- **Node Exporter**: System metrics exporter
- **cAdvisor**: Container metrics
- **Postgres Exporter**: PostgreSQL database metrics

## Directory Structure

```
monitoring/
├── prometheus/
│   └── prometheus.yml     # Prometheus configuration
├── grafana/
│   └── provisioning/      # Grafana provisioning
│       ├── datasources/   # Pre-configured data sources
│       └── dashboards/    # Pre-configured dashboards
│           ├── dashboard.yml
│           └── json/      # Dashboard definitions
│               ├── nodejs-dashboard.json
│               └── postgres-dashboard.json
```

## Accessing Monitoring Tools

| Service           | URL                                   | Default Credentials |
|-------------------|---------------------------------------|---------------------|
| Prometheus        | http://localhost:9090                 | N/A                 |
| Grafana           | http://localhost:3000 (production)    | admin/admin         |
|                   | http://localhost:3001 (development)   |                     |
| Node Exporter     | http://localhost:9100/metrics         | N/A                 |
| cAdvisor          | http://localhost:8080                 | N/A                 |
| Postgres Exporter | http://localhost:9187/metrics         | N/A                 |
| NestJS Metrics    | http://localhost:5000/api/metrics     | N/A                 |

## Default Dashboards

1. **IELTS Toolkit - Backend Monitoring**: Displays Node.js application metrics including:
   - API request rates and response times
   - Memory and CPU usage
   - Active connections

2. **IELTS Toolkit - PostgreSQL Monitoring**: Shows database metrics like:
   - Active connections
   - Transaction rates
   - Query performance
   - Cache hit ratio
   - Database size

## Adding Custom Metrics

### Backend Metrics

The NestJS application is configured with a MetricsService that can be injected into any service or controller:

```typescript
constructor(private readonly metricsService: MetricsService) {}

// Example usage:
someFunction() {
  // Count active users
  this.metricsService.setActiveUsers(activeCount);
  
  // Count test items
  this.metricsService.setTestCount(testCount);
}
```

## Alert Configuration

Alerting is not enabled by default. To configure alerts, update the Prometheus configuration with alerting rules and set up an Alertmanager instance.

## Troubleshooting

If metrics aren't appearing:

1. Check container logs: `docker-compose logs prometheus`
2. Verify target status in Prometheus UI: http://localhost:9090/targets
3. Confirm metrics endpoint accessibility: http://localhost:5000/api/metrics 