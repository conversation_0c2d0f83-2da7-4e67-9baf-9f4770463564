# IELTS Toolkit

A comprehensive web application for IELTS test preparation, allowing students to take practice tests and teachers/admins to create and manage tests.

## Repository Structure

- `client/`: Frontend React application
- `server-nest/`: Backend NestJS application (Git submodule)
- `monitoring/`: Monitoring components for local development
- `railway-setup.sh`: Railway deployment setup script
- `DEPLOYMENT.md`: Comprehensive deployment guide

## Getting Started

1. Clone the repository with submodules:
   ```bash
   git clone --recurse-submodules https://github.com/YOUR_USERNAME/ielts-toolkit.git
   ```

2. If you already cloned the repository without submodules, initialize and update them:
   ```bash
   git submodule init
   git submodule update --init --recursive
   ```

3. Run `docker-compose up -d` to start all services
4. Access the application at http://localhost:3000

## Testing the Application

### Available Users

The application is seeded with the following test users:

| Role | Email | Password |
|------|-------|----------|
| Admin | <EMAIL> | admin123 |
| Teacher | <EMAIL> | teacher123 |
| Student | <EMAIL> | student123 |

### Key Features

- **Student Features**:
  - View and take available tests
  - Track progress on tests
  - View test results
  - Flag questions for review

- **Teacher Features**:
  - Create and manage tests
  - Add questions to tests
  - Publish tests for students
  - View student results

- **Admin Features**:
  - All teacher features
  - User management
  - Category management

## Development

### Tech Stack

- **Frontend**: React with TypeScript, Vite, TailwindCSS
- **Backend**: NestJS, Prisma ORM
- **Database**: PostgreSQL
- **Monitoring**: Prometheus, Grafana

### Working with the server-nest Submodule

The backend code is maintained as a Git submodule. To make changes to the backend:

1. Navigate to the server-nest directory:
   ```bash
   cd server-nest
   ```

2. Make your changes to the code

3. Commit and push changes to the server-nest repository:
   ```bash
   git add .
   git commit -m "Your commit message"
   git push origin main
   ```

4. Update the submodule reference in the main repository:
   ```bash
   cd ..
   git add server-nest
   git commit -m "Update server-nest submodule"
   git push origin main
   ```

## Deployment

The application is configured for cost-effective deployment to Railway (~$5-15/month).

### Quick Deployment

1. **Automated Setup** (Recommended):
   ```bash
   ./railway-setup.sh
   ```

2. **Manual Setup**:
   - Create Railway account at https://railway.app
   - Follow the detailed guide in `DEPLOYMENT.md`

### Cost Breakdown
- PostgreSQL Database: $5/month
- Backend Service: $0-5/month (free tier available)
- Frontend Service: $0-5/month (free tier available)
- **Total: $5-15/month**

### Features
- ✅ Zero-config deployments
- ✅ Automatic HTTPS
- ✅ Built-in monitoring
- ✅ Database backups
- ✅ GitHub integration

### Troubleshooting

If you encounter connection issues between containers:
1. Restart the containers with `docker-compose restart`
2. Check logs with `docker-compose logs [service_name]`
3. Ensure database is properly seeded with `docker-compose exec server npx prisma db seed`

Access the database admin panel at http://localhost:5050 with credentials:
- Email: <EMAIL>
- Password: pgadmin

## License

This project is licensed under the MIT License - see the LICENSE file for details.