version: '3.8'

x-default-logging: &default-logging
  driver: json-file
  options:
    max-size: "10m"
    max-file: "3"

services:
  # DATABASE GROUP
  # PostgreSQL service
  postgres:
    image: postgres:16-alpine
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: ielts_toolkit
    networks:
      - ielts-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ielts_toolkit"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging: *default-logging
    labels:
      - "com.ielts-toolkit.group=database"
      - "com.ielts-toolkit.description=PostgreSQL Database"

  # Postgres Exporter - PostgreSQL metrics for Prometheus
  postgres-exporter:
    image: quay.io/prometheuscommunity/postgres-exporter:latest
    restart: unless-stopped
    environment:
      - DATA_SOURCE_NAME=postgresql://${POSTGRES_USER:-ielts_user}:${POSTGRES_PASSWORD:-password}@postgres:5432/${POSTGRES_DB:-ielts_suite}?sslmode=disable
    ports:
      - "${POSTGRES_EXPORTER_PORT:-9187}:9187"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - ielts-network
    logging: *default-logging
    labels:
      - "com.ielts-toolkit.group=monitoring"
      - "com.ielts-toolkit.description=PostgreSQL Metrics Exporter"

  # APPLICATION GROUP
  # Backend service
  server:
    build:
      context: ./server-nest
      dockerfile: Dockerfile.dev
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=********************************************/ielts_toolkit
      - JWT_SECRET=your-secret-key
      - NODE_ENV=development
    volumes:
      - ./server-nest:/app
      - /app/node_modules
    networks:
      - ielts-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s
    logging: *default-logging
    labels:
      - "com.ielts-toolkit.group=application"
      - "com.ielts-toolkit.description=NestJS Backend API"
    command: npm run start:dev

  # Frontend service
  client:
    build:
      context: ./client
      dockerfile: Dockerfile.dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    depends_on:
      server:
        condition: service_healthy
    environment:
      - NODE_ENV=development
      - HOST=0.0.0.0
      - PORT=3000
      - CONTAINER=true
    volumes:
      - ./client:/app
      - /app/node_modules
    networks:
      - ielts-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:3000"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    logging: *default-logging
    labels:
      - "com.ielts-toolkit.group=application"
      - "com.ielts-toolkit.description=React Frontend"
    command: npm run dev -- --host --port 3000

  # MONITORING GROUP
  # Prometheus - Metrics collection and storage
  prometheus:
    image: prom/prometheus:latest
    restart: unless-stopped
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - ielts-network
    logging: *default-logging
    labels:
      - "com.ielts-toolkit.group=monitoring"
      - "com.ielts-toolkit.description=Prometheus Metrics Collection"

  # Grafana - Visualization and dashboards
  grafana:
    image: grafana/grafana:latest
    restart: unless-stopped
    ports:
      - "${GRAFANA_PORT:-3001}:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    networks:
      - ielts-network
    logging: *default-logging
    labels:
      - "com.ielts-toolkit.group=monitoring"
      - "com.ielts-toolkit.description=Grafana Dashboards"

  # Node Exporter - System metrics
  node-exporter:
    image: prom/node-exporter:latest
    restart: unless-stopped
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "${NODE_EXPORTER_PORT:-9100}:9100"
    networks:
      - ielts-network
    logging: *default-logging
    labels:
      - "com.ielts-toolkit.group=monitoring"
      - "com.ielts-toolkit.description=Node Exporter System Metrics"

  # cAdvisor - Container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    restart: unless-stopped
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - "${CADVISOR_PORT:-8080}:8080"
    networks:
      - ielts-network
    logging: *default-logging
    labels:
      - "com.ielts-toolkit.group=monitoring"
      - "com.ielts-toolkit.description=cAdvisor Container Metrics"

  pgadmin:
    image: dpage/pgadmin4
    ports:
      - "5050:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - ielts-network
    logging: *default-logging
    labels:
      - "com.ielts-toolkit.group=database"
      - "com.ielts-toolkit.description=pgAdmin Database Management"

# Networks
networks:
  ielts-network:
    driver: bridge

# Volumes
volumes:
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  pgadmin_data:
    driver: local 