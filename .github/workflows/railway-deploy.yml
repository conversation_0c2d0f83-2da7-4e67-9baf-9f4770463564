name: Deploy to Railway

on:
  push:
    branches: [ main ]
    paths:
      - 'client/**'
      - 'server-nest/**'
      - '.github/workflows/railway-deploy.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'client/**'
      - 'server-nest/**'
      - '.github/workflows/railway-deploy.yml'
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy to Railway
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        submodules: recursive
        token: ${{ secrets.PAT_TOKEN }}

    - name: Initialize Git submodules
      run: |
        git submodule init
        git submodule update

    - name: Install Railway CLI
      run: |
        npm install -g @railway/cli

    - name: Deploy Backend to Railway
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
      run: |
        cd server-nest
        railway link ${{ secrets.RAILWAY_PROJECT_ID }}
        railway up

    - name: Deploy Frontend to Railway
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
      run: |
        cd client
        railway link ${{ secrets.RAILWAY_PROJECT_ID }}
        railway up

    - name: Run Database Migrations
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
      run: |
        cd server-nest
        railway link ${{ secrets.RAILWAY_PROJECT_ID }}
        railway run npm run prisma:deploy
