name: Deploy to Railway

on:
  push:
    branches: [ main ]
    paths:
      - 'client/**'
      - 'server-nest/**'
      - '.github/workflows/railway-deploy.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'client/**'
      - 'server-nest/**'
      - '.github/workflows/railway-deploy.yml'
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy to Railway
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        submodules: recursive
        token: ${{ secrets.PAT_TOKEN }}

    - name: Initialize Git submodules
      run: |
        git submodule init
        git submodule update

    - name: Install Railway CLI
      run: |
        npm install -g @railway/cli

    - name: Deploy Backend to Railway
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
        RAILWAY_PROJECT_ID: ${{ secrets.RAILWAY_PROJECT_ID }}
        RAILWAY_SERVICE_ID: ${{ secrets.RAILWAY_BACKEND_SERVICE_ID }}
      run: |
        cd server-nest
        railway up

    - name: Deploy Frontend to Railway
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
        RAILWAY_PROJECT_ID: ${{ secrets.RAILWAY_PROJECT_ID }}
        RAILWAY_SERVICE_ID: ${{ secrets.RAILWAY_FRONTEND_SERVICE_ID }}
      run: |
        cd client
        railway up

    - name: Run Database Migrations
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
        RAILWAY_PROJECT_ID: ${{ secrets.RAILWAY_PROJECT_ID }}
        RAILWAY_SERVICE_ID: ${{ secrets.RAILWAY_BACKEND_SERVICE_ID }}
      run: |
        cd server-nest
        railway run npm run prisma:deploy
