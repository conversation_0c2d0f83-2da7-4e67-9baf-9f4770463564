# IELTS Suite API

Backend API for IELTS management and practice system.

## Description

API serving the IELTS Suite application, supporting features for teachers, students, and administrators.

## Features

### Teachers
- Test management (create, read, update, delete)
- Category management (create, read, update, delete)
- Create different types of tests (reading, listening, speaking, writing, full tests)
- Publish or hide tests

### Students
- View shared tests
- Take tests
- View results

### Administrators
- User management
- Disable accounts
- View reports

## Installation

### Requirements
- Node.js (>= 14.0.0)
- PostgreSQL (>= 12.0)
- npm or yarn

### Setup

1. Install dependencies:
```bash
npm install
```

2. Create `.env` file from `.env.example`:
```bash
cp .env.example .env
```

3. Edit the `.env` file with PostgreSQL configuration (DATABASE_URL) and other parameters.

4. Create and apply migrations:
```bash
npm run prisma:migrate
```

5. Initialize sample data (if needed):
```bash
npm run prisma:seed
```

6. Run the application:
```bash
# Development mode
npm run start:dev

# Production mode
npm run start:prod
```

## Database Management

```bash
# View and edit data visually
npm run prisma:studio

# Create a new migration
npm run prisma:migrate -- --name migration_name

# Check migrations status
npx prisma migrate status
```

## Technology
- NestJS
- TypeScript
- PostgreSQL with Prisma ORM
- Passport.js and JWT
- Validation with class-validator

## API Endpoints

### Auth
- POST /api/auth/register - Register account
- POST /api/auth/login - Login
- GET /api/auth/me - Get current user information

### Users
- GET /api/users - Get list of users (Admin)
- GET /api/users/:id - Get user information
- PATCH /api/users/:id - Update user information
- DELETE /api/users/:id - Delete user (Admin)
- PATCH /api/users/:id/toggle-active - Toggle user status (Admin)

### Categories
- POST /api/categories - Create new category
- GET /api/categories - Get list of categories
- GET /api/categories/:id - Get category information
- PATCH /api/categories/:id - Update category
- DELETE /api/categories/:id - Delete category
- PATCH /api/categories/:id/toggle-active - Toggle category status

### Tests
- POST /api/tests - Create new test
- GET /api/tests - Get list of tests
- GET /api/tests/:id - Get test information
- PATCH /api/tests/:id - Update test
- DELETE /api/tests/:id - Delete test
- PATCH /api/tests/:id/toggle-active - Toggle test status
- PATCH /api/tests/:id/toggle-publish - Toggle publish status

## Contribution
Please create an issue before sending a pull request.

## License
MIT
