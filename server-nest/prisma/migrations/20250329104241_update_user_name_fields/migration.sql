/*
  Warnings:

  - You are about to drop the column `name` on the `users` table. All the data in the column will be lost.
  - Added the required column `firstName` to the `users` table without a default value. This is not possible if the table is not empty.
  - Added the required column `lastName` to the `users` table without a default value. This is not possible if the table is not empty.

*/
-- First add the new columns
ALTER TABLE "users" ADD COLUMN "firstName" TEXT;
ALTER TABLE "users" ADD COLUMN "lastName" TEXT;

-- Update existing data by splitting the name field
UPDATE "users" 
SET "firstName" = SPLIT_PART(name, ' ', 1),
    "lastName" = SPLIT_PART(name, ' ', 2)
WHERE "firstName" IS NULL;

-- Make the new columns required
ALTER TABLE "users" ALTER COLUMN "firstName" SET NOT NULL;
ALTER TABLE "users" ALTER COLUMN "lastName" SET NOT NULL;

-- Drop the old name column
ALTER TABLE "users" DROP COLUMN "name";
