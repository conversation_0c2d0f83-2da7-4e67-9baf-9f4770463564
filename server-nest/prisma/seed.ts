import { PrismaClient, Role } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  // Clean the database
  await prisma.test.deleteMany();
  await prisma.category.deleteMany();
  await prisma.user.deleteMany();

  console.log('Database cleaned');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 10);
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      password: adminPassword,
      role: Role.ADMIN,
      isActive: true,
    },
  });

  // Create teacher user
  const teacherPassword = await bcrypt.hash('teacher123', 10);
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      firstName: 'Teacher',
      lastName: 'User',
      password: teacherPassword,
      role: Role.TEACHER,
      isActive: true,
    },
  });

  // Create student user
  const studentPassword = await bcrypt.hash('student123', 10);
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      firstName: 'Student',
      lastName: 'User',
      password: studentPassword,
      role: Role.STUDENT,
      isActive: true,
    },
  });

  console.log('Users created:', { admin: '<EMAIL>', teacher: '<EMAIL>', student: '<EMAIL>' });

  // Find the admin user to set as creator for categories
  const adminUser = await prisma.user.findFirst({
    where: { role: Role.ADMIN }
  });

  if (!adminUser) {
    throw new Error('Admin user not found. Cannot create categories without a creator.');
  }

  // Create categories
  const readingCategory = await prisma.category.create({
    data: {
      name: 'Reading',
      description: 'Reading practice tests for IELTS',
      createdById: adminUser.id, // Set the creator ID
    },
  });

  const listeningCategory = await prisma.category.create({
    data: {
      name: 'Listening',
      description: 'Listening practice tests for IELTS',
      createdById: adminUser.id, // Set the creator ID
    },
  });

  const writingCategory = await prisma.category.create({
    data: {
      name: 'Writing',
      description: 'Writing practice tests for IELTS',
      createdById: adminUser.id, // Set the creator ID
    },
  });

  const speakingCategory = await prisma.category.create({
    data: {
      name: 'Speaking',
      description: 'Speaking practice tests for IELTS',
      createdById: adminUser.id, // Set the creator ID
    },
  });

  console.log('Categories created:', {
    readingCategory,
    listeningCategory,
    writingCategory,
    speakingCategory,
  });

  console.log('Seed completed successfully');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });