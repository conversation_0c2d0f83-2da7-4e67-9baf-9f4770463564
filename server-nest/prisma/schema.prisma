generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  password  String
  role      Role     @default(STUDENT)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  firstName String
  lastName  String

  // Group relations
  createdGroups Group[]        @relation("TeacherGroups")
  studentGroups StudentGroup[] @relation("StudentGroups")

  // Test relations
  createdTests     Test[]         // Tests created by this user
  testResults      TestResult[]   // Test results for this user
  
  // Category relations
  createdCategories Category[]     // Categories created by this user

  // Invite relations
  createdInvites GroupInvite[]

  @@map("users")
}

model Category {
  id          String   @id @default(uuid())
  name        String
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  isActive    Boolean  @default(true)
  createdById String?  @map("created_by_id")
  createdBy   User?    @relation(fields: [createdById], references: [id])
  tests       Test[]

  @@map("categories")
}

model Test {
  id                    String     @id @default(uuid())
  title                 String
  description           String?
  type                  TestType
  difficulty            Difficulty @default(MEDIUM)
  isPublished           Boolean    @default(false) @map("is_published")
  isActive              Boolean    @default(true)
  categoryId            String?    @map("category_id")
  createdById           String?    @map("created_by_id")
  createdAt             DateTime   @default(now()) @map("created_at")
  updatedAt             DateTime   @updatedAt @map("updated_at")
  instructions          String?
  maxScore              Int        @default(100) @map("max_score")
  multipleChoiceAnswers Json?      @map("multiple_choice_answers")
  textAnswers           Json?      @map("text_answers")
  timeLimit             Int        @default(60) @map("time_limit")
  content               String?
  category              Category?  @relation(fields: [categoryId], references: [id])
  createdBy             User?      @relation(fields: [createdById], references: [id])

  // Group relation
  groupAssignments      GroupAssignment[]

  // Test results
  testResults           TestResult[]

  @@map("tests")
}

enum Role {
  ADMIN
  TEACHER
  STUDENT
}

enum TestType {
  READING
  LISTENING
  WRITING
  SPEAKING
  FULL_TEST
}

enum Difficulty {
  EASY
  MEDIUM
  HARD
}

model Group {
  id          String   @id @default(uuid())
  name        String
  level       String   // e.g., "Beginner", "Intermediate", "Advanced"
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  isActive    Boolean  @default(true)
  createdBy   String   @map("created_by")

  // Relations
  teacher     User     @relation("TeacherGroups", fields: [createdBy], references: [id])
  students    StudentGroup[]
  assignments GroupAssignment[]
  invites     GroupInvite[]

  @@map("groups")
}

model StudentGroup {
  id        String   @id @default(uuid())
  groupId   String   @map("group_id")
  studentId String   @map("student_id")
  joinedAt  DateTime @default(now()) @map("joined_at")

  // Relations
  group     Group    @relation(fields: [groupId], references: [id], onDelete: Cascade)
  student   User     @relation("StudentGroups", fields: [studentId], references: [id])

  @@unique([groupId, studentId])
  @@map("student_groups")
}

model GroupAssignment {
  id         String    @id @default(uuid())
  groupId    String    @map("group_id")
  testId     String    @map("test_id")
  assignedAt DateTime  @default(now()) @map("assigned_at")
  dueDate    DateTime? @map("due_date")

  // Relations
  group      Group     @relation(fields: [groupId], references: [id], onDelete: Cascade)
  test       Test      @relation(fields: [testId], references: [id])

  @@unique([groupId, testId])
  @@map("group_assignments")
}

model GroupInvite {
  id          String    @id @default(uuid())
  groupId     String    @map("group_id")
  token       String    @unique
  createdById String    @map("created_by_id")
  createdAt   DateTime  @default(now()) @map("created_at")
  expiresAt   DateTime  @map("expires_at")
  maxUses     Int?      @map("max_uses") // null means unlimited
  useCount    Int       @default(0) @map("use_count")
  isActive    Boolean   @default(true)
  lastUsedAt  DateTime? @map("last_used_at")

  // Relations
  group       Group     @relation(fields: [groupId], references: [id], onDelete: Cascade)
  createdBy   User      @relation(fields: [createdById], references: [id])

  @@map("group_invites")
}

model TestResult {
  id              String   @id @default(uuid())
  testId          String   @map("test_id")
  userId          String   @map("user_id")
  score           Int
  maxScore        Int      @map("max_score")
  submittedAt     DateTime @default(now()) @map("submitted_at")
  timeSpent       Int      @map("time_spent") // in seconds
  totalQuestions  Int      @map("total_questions")
  correctAnswers  Int      @map("correct_answers")
  incorrectAnswers Int     @map("incorrect_answers")
  answers         Json     // Stored as JSON array of answer objects

  // Relations
  test            Test     @relation(fields: [testId], references: [id])
  user            User     @relation(fields: [userId], references: [id])

  @@map("test_results")
}
