import { Provider } from '@nestjs/common';
import { 
  makeCounterProvider, 
  makeGaugeProvider, 
  makeHistogramProvider 
} from '@willsoto/nestjs-prometheus';
import { Counter, Gauge, Histogram } from 'prom-client';

/**
 * Helper function to create a counter metric provider
 */
export function makeCounterMetric(options: {
  name: string;
  help: string;
  labelNames?: string[];
}): Provider {
  return makeCounterProvider(options);
}

/**
 * Helper function to create a gauge metric provider
 */
export function makeGaugeMetric(options: {
  name: string;
  help: string;
  labelNames?: string[];
}): Provider {
  return makeGaugeProvider(options);
}

/**
 * Helper function to create a histogram metric provider
 */
export function makeHistogramMetric(options: {
  name: string;
  help: string;
  labelNames?: string[];
  buckets?: number[];
}): Provider {
  return makeHistogramProvider(options);
} 