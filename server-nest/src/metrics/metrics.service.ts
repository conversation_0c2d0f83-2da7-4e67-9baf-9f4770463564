import { Injectable } from '@nestjs/common';
import { InjectMetric } from '@willsoto/nestjs-prometheus';
import { Counter, Gauge, Histogram } from 'prom-client';

@Injectable()
export class MetricsService {
  constructor(
    @InjectMetric('http_request_total')
    private readonly requestCounter: Counter<string>,
    
    @InjectMetric('http_request_duration_seconds')
    private readonly requestDuration: Histogram<string>,
    
    @InjectMetric('app_active_users')
    private readonly activeUsers: Gauge<string>,
    
    @InjectMetric('app_test_count')
    private readonly testCount: Gauge<string>,
  ) {}

  // HTTP request metrics
  recordHttpRequest(method: string, route: string, status: number) {
    this.requestCounter.inc({
      method,
      route,
      status: status.toString(),
    });
  }

  recordHttpRequestDuration(method: string, route: string, durationMs: number) {
    this.requestDuration.observe(
      {
        method,
        route,
      },
      durationMs / 1000, // Convert to seconds
    );
  }

  // Application-specific metrics
  setActiveUsers(count: number) {
    this.activeUsers.set(count);
  }

  setTestCount(count: number) {
    this.testCount.set(count);
  }
} 