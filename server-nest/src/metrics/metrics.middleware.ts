import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { MetricsService } from './metrics.service';

@Injectable()
export class MetricsMiddleware implements NestMiddleware {
  constructor(private readonly metricsService: MetricsService) {}

  use(req: Request, res: Response, next: NextFunction) {
    const start = Date.now();
    const { method, path } = req;

    // Add response hook to record metrics after request completion
    res.on('finish', () => {
      const responseTime = Date.now() - start;
      const { statusCode } = res;

      // Record metrics
      this.metricsService.recordHttpRequest(method, path, statusCode);
      this.metricsService.recordHttpRequestDuration(method, path, responseTime);
    });

    next();
  }
} 