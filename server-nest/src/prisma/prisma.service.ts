import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ConfigService } from '@nestjs/config';
import { ExtendedPrismaClient } from './prisma.types';

@Injectable()
export class PrismaService extends PrismaClient implements ExtendedPrismaClient, OnModuleInit, OnModuleDestroy {
  constructor(private configService: ConfigService) {
    super({
      datasources: {
        db: {
          url: configService.get('DATABASE_URL'),
        },
      },
    });
  }

  async onModuleInit() {
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }

  async cleanDatabase() {
    if (this.configService.get('NODE_ENV') === 'development') {
      // Execute in a transaction to maintain referential integrity
      return this.$transaction([
        this.test.deleteMany(),
        this.category.deleteMany(),
        this.user.deleteMany(),
      ]);
    }
  }
}