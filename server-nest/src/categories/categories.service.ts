import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { Role } from '../common/enums/role.enum';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { User } from '../users/user.entity';
import { Category } from './category.entity';

@Injectable()
export class CategoriesService {
  constructor(
    private readonly prisma: PrismaService,
  ) {}

  async create(createCategoryDto: CreateCategoryDto, user: User): Promise<Category> {
    try {
      // Now we can use createdById directly in the database
      // Use type assertion to work around TypeScript errors with Prisma schema changes
      const newCategory = await this.prisma.category.create({
        data: {
          name: createCategoryDto.name,
          description: createCategoryDto.description || null,
          createdById: user?.id  // This will work after migration
        },
        include: {
          createdBy: true  // Include the related user
        }
      } as any);

      // Use type assertion to work around TypeScript errors
      const typedCategory = newCategory as any;

      // Transform response to match our entity
      const response: Category = {
        id: typedCategory.id,
        name: typedCategory.name,
        description: typedCategory.description,
        isActive: typedCategory.isActive,
        createdById: typedCategory.createdById || null,
        createdAt: typedCategory.createdAt,
        updatedAt: typedCategory.updatedAt
      };

      // Add creator information if available
      if (typedCategory.createdBy) {
        response.createdBy = {
          _id: typedCategory.createdBy.id,
          firstName: typedCategory.createdBy.firstName,
          lastName: typedCategory.createdBy.lastName,
          email: typedCategory.createdBy.email
        };

        console.log(`Category created by user: ${typedCategory.createdBy.firstName} ${typedCategory.createdBy.lastName} (${typedCategory.createdById})`);
      } else {
        console.log('Category created without user association');
      }

      return response;
    } catch (error) {
      console.error('Error creating category:', error);
      throw new InternalServerErrorException('Failed to create category: ' + error.message);
    }
  }

  // Public endpoint - only show active categories
  async findAll(): Promise<Category[]> {
    try {
      // Get all active categories
      // Use type assertion to work around TypeScript errors with Prisma schema changes
      const categories = await this.prisma.category.findMany({
        where: {
          isActive: true
        },
        include: {
          createdBy: true // Include user information
        },
        orderBy: {
          createdAt: 'desc'
        }
      } as any);

      // Transform to match our Entity type
      return categories.map(category => {
        // Use type assertion to work around TypeScript errors
        const typedCategory = category as any;

        // Create a category object matching our entity format
        const formattedCategory: Category = {
          id: typedCategory.id,
          name: typedCategory.name,
          description: typedCategory.description,
          isActive: typedCategory.isActive,
          createdById: typedCategory.createdById || null,
          createdAt: typedCategory.createdAt,
          updatedAt: typedCategory.updatedAt
        };

        // Add creator information if available
        if (typedCategory.createdBy) {
          formattedCategory.createdBy = {
            _id: typedCategory.createdBy.id,
            firstName: typedCategory.createdBy.firstName,
            lastName: typedCategory.createdBy.lastName,
            email: typedCategory.createdBy.email
          };
        }

        return formattedCategory;
      });
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw new InternalServerErrorException('Failed to fetch categories');
    }
  }

  // Authenticated endpoint - filter by user role
  async findAllForUser(currentUser: User): Promise<Category[]> {
    try {
      // Build query based on user role
      const whereCondition: any = {};

      // If user is not admin, filter categories by creator
      if (currentUser.role !== Role.ADMIN) {
        whereCondition.createdById = currentUser.id;
      }

      // Get categories from database with filtering
      // Use type assertion to work around TypeScript errors with Prisma schema changes
      const categories = await this.prisma.category.findMany({
        where: whereCondition,
        include: {
          createdBy: true // Include user information
        },
        orderBy: {
          createdAt: 'desc'
        }
      } as any);

      // Log information about the filtering
      if (currentUser.role !== Role.ADMIN) {
        console.log(`Showing categories for teacher: ${currentUser.firstName} ${currentUser.lastName} (${currentUser.id})`);
        console.log(`Found ${categories.length} categories created by this teacher`);
      } else {
        console.log(`Showing all categories for admin: ${currentUser.firstName} ${currentUser.lastName}`);
        console.log(`Found ${categories.length} total categories`);
      }

      // Transform the category data to match our entity format
      return categories.map(category => {
        // Use type assertion to work around TypeScript errors
        const typedCategory = category as any;

        // Create a category object matching our entity format
        const formattedCategory: Category = {
          id: typedCategory.id,
          name: typedCategory.name,
          description: typedCategory.description,
          isActive: typedCategory.isActive,
          createdById: typedCategory.createdById,
          createdAt: typedCategory.createdAt,
          updatedAt: typedCategory.updatedAt
        };

        // Add creator information if available
        if (typedCategory.createdBy) {
          formattedCategory.createdBy = {
            _id: typedCategory.createdBy.id,
            firstName: typedCategory.createdBy.firstName,
            lastName: typedCategory.createdBy.lastName,
            email: typedCategory.createdBy.email
          };
        }

        return formattedCategory;
      });
    } catch (error) {
      console.error(`Failed to find categories: ${error.message}`);
      throw new InternalServerErrorException('Failed to retrieve categories');
    }
  }

  async findOne(id: string): Promise<Category> {
    try {
      // Use type assertion to work around TypeScript errors with Prisma schema changes
      const category = await this.prisma.category.findUnique({
        where: { id },
        include: {
          createdBy: true // Include user information
        }
      } as any);

      if (!category) {
        throw new NotFoundException(`Category with ID ${id} not found`);
      }

      // Use type assertion to work around TypeScript errors
      const typedCategory = category as any;

      // Convert to Category entity type
      const formattedCategory: Category = {
        id: typedCategory.id,
        name: typedCategory.name,
        description: typedCategory.description,
        isActive: typedCategory.isActive,
        createdById: typedCategory.createdById || null,
        createdAt: typedCategory.createdAt,
        updatedAt: typedCategory.updatedAt
      };

      // Add creator information if available
      if (typedCategory.createdBy) {
        formattedCategory.createdBy = {
          _id: typedCategory.createdBy.id,
          firstName: typedCategory.createdBy.firstName,
          lastName: typedCategory.createdBy.lastName,
          email: typedCategory.createdBy.email
        };
      }

      return formattedCategory;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Failed to find category: ${error.message}`);
      throw new InternalServerErrorException('Failed to retrieve category');
    }
  }

  async update(id: string, updateCategoryDto: UpdateCategoryDto): Promise<Category> {
    try {
      // Use type assertion to work around TypeScript errors with Prisma schema changes
      const updatedCategory = await this.prisma.category.update({
        where: { id },
        data: updateCategoryDto,
        include: {
          createdBy: true // Include user information
        }
      } as any);

      // Use type assertion to work around TypeScript errors
      const typedCategory = updatedCategory as any;

      // Convert to Category entity type
      const formattedCategory: Category = {
        id: typedCategory.id,
        name: typedCategory.name,
        description: typedCategory.description,
        isActive: typedCategory.isActive,
        createdById: typedCategory.createdById || null,
        createdAt: typedCategory.createdAt,
        updatedAt: typedCategory.updatedAt
      };

      // Add creator information if available
      if (typedCategory.createdBy) {
        formattedCategory.createdBy = {
          _id: typedCategory.createdBy.id,
          firstName: typedCategory.createdBy.firstName,
          lastName: typedCategory.createdBy.lastName,
          email: typedCategory.createdBy.email
        };
      }

      return formattedCategory;
    } catch (error) {
      console.error(`Failed to update category: ${error.message}`);
      throw new NotFoundException('Category not found');
    }
  }

  async remove(id: string) {
    // First check if the category exists
    await this.findOne(id);
    // Then delete it
    return this.prisma.category.delete({
      where: { id },
    });
  }

  async toggleActive(id: string): Promise<Category> {
    try {
      // First get the current category state
      const category = await this.findOne(id);

      // Update the isActive status
      // Use type assertion to work around TypeScript errors with Prisma schema changes
      const updatedCategory = await this.prisma.category.update({
        where: { id },
        data: {
          isActive: !category.isActive
        },
        include: {
          createdBy: true // Include user information
        }
      } as any);

      // Use type assertion to work around TypeScript errors
      const typedCategory = updatedCategory as any;

      // Convert to Category entity type
      const formattedCategory: Category = {
        id: typedCategory.id,
        name: typedCategory.name,
        description: typedCategory.description,
        isActive: typedCategory.isActive,
        createdById: typedCategory.createdById || null,
        createdAt: typedCategory.createdAt,
        updatedAt: typedCategory.updatedAt
      };

      // Add creator information if available
      if (typedCategory.createdBy) {
        formattedCategory.createdBy = {
          _id: typedCategory.createdBy.id,
          firstName: typedCategory.createdBy.firstName,
          lastName: typedCategory.createdBy.lastName,
          email: typedCategory.createdBy.email
        };
      }

      return formattedCategory;
    } catch (error) {
      console.error(`Failed to toggle category active state: ${error.message}`);
      throw new InternalServerErrorException('Failed to update category status');
    }
  }
}