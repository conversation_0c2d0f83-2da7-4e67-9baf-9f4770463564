// This class provides type-safe entities for TypeScript
export class Category {
  id?: string;
  name: string;
  description?: string | null;
  isActive?: boolean;
  createdById?: string | null;  // Allow null to match Prisma's types
  createdBy?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  } | null;  // Allow null to match Prisma's types
  createdAt?: Date | string;
  updatedAt?: Date | string;
} 