import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class LoginDto {
  @IsNotEmpty({ message: 'Please enter an email' })
  @IsEmail({}, { message: 'Invalid email format' })
  email: string;

  @IsNotEmpty({ message: 'Please enter a password' })
  @MinLength(6, { message: 'Password must be at least 6 characters' })
  password: string;
} 