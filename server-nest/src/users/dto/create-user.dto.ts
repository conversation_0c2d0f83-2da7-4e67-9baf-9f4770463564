import { Is<PERSON>mail, <PERSON><PERSON>num, <PERSON>Not<PERSON>mpt<PERSON>, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { Role } from '@prisma/client';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsEmail()
  email: string;

  @IsString()
  @MinLength(6)
  password: string;

  @IsEnum(Role)
  role?: Role;
} 