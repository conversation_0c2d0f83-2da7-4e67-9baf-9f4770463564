import { Injectable, NotFoundException, ForbiddenException, ConflictException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateGroupDto } from './dto/create-group.dto';
import { UpdateGroupDto } from './dto/update-group.dto';
import { AddStudentsDto } from './dto/add-students.dto';
import { AssignTestDto } from './dto/assign-test.dto';
import { CreateGroupInviteDto } from './dto/create-group-invite.dto';
import { GroupInviteDto } from './dto/group-invite.dto';
import { GroupInvite } from './entities/group-invite.entity';
import * as crypto from 'crypto';
import { Group } from './entities/group.entity';
import { StudentGroup } from './entities/student-group.entity';
import { GroupAssignment } from './entities/group-assignment.entity';
import { Role } from '@prisma/client';
import { TestQuestionAnalysisDto, QuestionAnalysisDto, QuestionType, IncorrectAnswerDto } from './dto/test-question-analysis.dto';

@Injectable()
export class TeacherService {
  private readonly logger = new Logger(TeacherService.name);

  constructor(private readonly prisma: PrismaService) {}

  // Group Management
  async createGroup(createGroupDto: CreateGroupDto, teacherId: string): Promise<Group> {
    // Verify the user is a teacher
    const teacher = await this.prisma.user.findUnique({
      where: { id: teacherId },
    });

    if (!teacher || teacher.role !== Role.TEACHER) {
      throw new ForbiddenException('Only teachers can create groups');
    }

    return (this.prisma as any).group.create({
      data: {
        ...createGroupDto,
        createdBy: teacherId,
      },
    });
  }

  async findAllGroups(teacherId: string): Promise<Group[]> {
    return (this.prisma as any).group.findMany({
      where: {
        createdBy: teacherId,
        isActive: true,
      },
      include: {
        students: {
          include: {
            student: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        assignments: {
          include: {
            test: {
              select: {
                id: true,
                title: true,
                description: true,
                type: true,
                difficulty: true,
              },
            },
          },
        },
      },
    });
  }

  async findGroupById(id: string, teacherId: string): Promise<Group> {
    const group = await (this.prisma as any).group.findUnique({
      where: { id },
      include: {
        students: {
          include: {
            student: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        assignments: {
          include: {
            test: {
              select: {
                id: true,
                title: true,
                description: true,
                type: true,
                difficulty: true,
              },
            },
          },
        },
      },
    });

    if (!group) {
      throw new NotFoundException(`Group with ID ${id} not found`);
    }

    // Check if the group belongs to the teacher
    if (group.createdBy !== teacherId) {
      throw new ForbiddenException('You do not have permission to access this group');
    }

    return group;
  }

  async updateGroup(id: string, updateGroupDto: UpdateGroupDto, teacherId: string): Promise<Group> {
    // Check if the group exists and belongs to the teacher
    await this.findGroupById(id, teacherId);

    return (this.prisma as any).group.update({
      where: { id },
      data: updateGroupDto,
    });
  }

  async removeGroup(id: string, teacherId: string): Promise<Group> {
    // Check if the group exists and belongs to the teacher
    await this.findGroupById(id, teacherId);

    // Instead of deleting, we'll mark it as inactive
    return (this.prisma as any).group.update({
      where: { id },
      data: { isActive: false },
    });
  }

  // Student Management
  async addStudentsToGroup(groupId: string, addStudentsDto: AddStudentsDto, teacherId: string): Promise<StudentGroup[]> {
    // Check if the group exists and belongs to the teacher
    await this.findGroupById(groupId, teacherId);

    // Verify all students exist and are students
    const students = await this.prisma.user.findMany({
      where: {
        id: { in: addStudentsDto.studentIds },
        role: Role.STUDENT,
      },
    });

    if (students.length !== addStudentsDto.studentIds.length) {
      throw new NotFoundException('One or more students not found or are not students');
    }

    // Check for existing memberships to avoid duplicates
    const existingMemberships = await (this.prisma as any).studentGroup.findMany({
      where: {
        groupId,
        studentId: { in: addStudentsDto.studentIds },
      },
    });

    const existingStudentIds = existingMemberships.map((membership: any) => membership.studentId);
    const newStudentIds = addStudentsDto.studentIds.filter(id => !existingStudentIds.includes(id));

    if (newStudentIds.length === 0) {
      throw new ConflictException('All students are already in this group');
    }

    // Add new students to the group
    const createdMemberships = await Promise.all(
      newStudentIds.map(studentId =>
        (this.prisma as any).studentGroup.create({
          data: {
            groupId,
            studentId,
          },
        })
      )
    );

    return createdMemberships;
  }

  async removeStudentFromGroup(groupId: string, studentId: string, teacherId: string): Promise<void> {
    // Check if the group exists and belongs to the teacher
    await this.findGroupById(groupId, teacherId);

    // Check if the student is in the group
    const membership = await (this.prisma as any).studentGroup.findUnique({
      where: {
        groupId_studentId: {
          groupId,
          studentId,
        },
      },
    });

    if (!membership) {
      throw new NotFoundException(`Student with ID ${studentId} is not in this group`);
    }

    // Remove the student from the group
    await (this.prisma as any).studentGroup.delete({
      where: {
        groupId_studentId: {
          groupId,
          studentId,
        },
      },
    });
  }

  async getStudentsInGroup(groupId: string, teacherId: string): Promise<any[]> {
    // Check if the group exists and belongs to the teacher
    const group = await this.findGroupById(groupId, teacherId);

    // Return the students
    if (!group.students) {
      return [];
    }
    return group.students.map(membership => membership.student);
  }

  // Get all students for adding to groups
  async getAllStudents(teacherId: string): Promise<any[]> {
    // Verify the user is a teacher
    const teacher = await this.prisma.user.findUnique({
      where: { id: teacherId },
    });

    if (!teacher || teacher.role !== Role.TEACHER) {
      throw new ForbiddenException('Only teachers can access student data');
    }

    // Get all students
    return this.prisma.user.findMany({
      where: {
        role: Role.STUDENT,
        isActive: true
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true
      }
    });
  }

  // Test Assignment
  async assignTestToGroup(groupId: string, assignTestDto: AssignTestDto, teacherId: string): Promise<GroupAssignment> {
    // Check if the group exists and belongs to the teacher
    await this.findGroupById(groupId, teacherId);

    // Check if the test exists
    const test = await this.prisma.test.findUnique({
      where: { id: assignTestDto.testId },
    });

    if (!test) {
      throw new NotFoundException(`Test with ID ${assignTestDto.testId} not found`);
    }

    // Check if the test is already assigned to the group
    const existingAssignment = await (this.prisma as any).groupAssignment.findUnique({
      where: {
        groupId_testId: {
          groupId,
          testId: assignTestDto.testId,
        },
      },
    });

    if (existingAssignment) {
      throw new ConflictException('This test is already assigned to the group');
    }

    // Assign the test to the group
    return (this.prisma as any).groupAssignment.create({
      data: {
        groupId,
        testId: assignTestDto.testId,
        dueDate: assignTestDto.dueDate ? new Date(assignTestDto.dueDate) : null,
      },
    });
  }

  async removeTestFromGroup(groupId: string, testId: string, teacherId: string): Promise<void> {
    // Check if the group exists and belongs to the teacher
    await this.findGroupById(groupId, teacherId);

    // Check if the test is assigned to the group
    const assignment = await (this.prisma as any).groupAssignment.findUnique({
      where: {
        groupId_testId: {
          groupId,
          testId,
        },
      },
    });

    if (!assignment) {
      throw new NotFoundException(`Test with ID ${testId} is not assigned to this group`);
    }

    // Remove the test assignment
    await (this.prisma as any).groupAssignment.delete({
      where: {
        groupId_testId: {
          groupId,
          testId,
        },
      },
    });
  }

  async getTestsAssignedToGroup(groupId: string, teacherId: string): Promise<any[]> {
    // Check if the group exists and belongs to the teacher
    const group = await this.findGroupById(groupId, teacherId);

    // Return the assigned tests
    if (!group.assignments) {
      return [];
    }
    return group.assignments.map(assignment => ({
      ...assignment.test,
      assignedAt: assignment.assignedAt,
      dueDate: assignment.dueDate,
    }));
  }

  // Group Invitation Management
  async createGroupInvite(
    groupId: string,
    createGroupInviteDto: CreateGroupInviteDto,
    teacherId: string
  ): Promise<GroupInviteDto> {
    // Verify teacher owns the group
    await this.findGroupById(groupId, teacherId);

    const { expiresInDays = 7, maxUses } = createGroupInviteDto;
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expiresInDays);

    // Generate a secure random token
    const token = crypto.randomBytes(32).toString('hex');

    try {
      // Use type assertion to tell TypeScript that prisma has a groupInvite property
      // This is safe because we've confirmed the model exists in the schema
      const invite = await (this.prisma as any).groupInvite.create({
        data: {
          token,
          expiresAt,
          maxUses,
          groupId,
          createdById: teacherId,
          isActive: true,
          useCount: 0
        },
      });

      this.logger.log(`Created group invite with ID: ${invite.id} for group: ${groupId}`);

      // Convert the Prisma model to our DTO format with proper type handling
      return {
        id: invite.id,
        token: invite.token,
        createdAt: invite.createdAt,
        expiresAt: invite.expiresAt,
        maxUses: invite.maxUses || undefined, // Convert null to undefined
        useCount: invite.useCount,
        isActive: invite.isActive,
        lastUsedAt: invite.lastUsedAt || undefined, // Convert null to undefined
        inviteUrl: `${(process.env.FRONTEND_URL || 'http://localhost:3000').replace(/\/$/, '')}/invites/groups/${invite.token}`,
      };
    } catch (error) {
      this.logger.error(`Failed to create group invite: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Results and Analytics
  async getGroupResults(groupId: string, teacherId: string): Promise<any> {
    // Check if the group exists and belongs to the teacher
    const group = await this.findGroupById(groupId, teacherId);

    // Get all students in the group
    const studentIds = group.students ? group.students.map(membership => membership.student.id) : [];

    // Get all tests assigned to the group
    const testIds = group.assignments ? group.assignments.map(assignment => assignment.test.id) : [];

    // Get all test results for these students and tests
    const testResults = await this.prisma.testResult.findMany({
      where: {
        testId: {
          in: testIds
        },
        userId: {
          in: studentIds
        }
      }
    });

    // Calculate group-level statistics
    const totalResults = testResults.length;
    const totalScore = testResults.reduce((sum, result) => sum + result.score, 0);
    const averageScore = totalResults > 0 ? Math.round(totalScore / totalResults) : 0;

    // Calculate completion rate (percentage of assigned tests that have been completed)
    const totalAssignments = testIds.length * studentIds.length;
    const completionRate = totalAssignments > 0
      ? Math.round((totalResults / totalAssignments) * 100)
      : 0;

    // Calculate student-level statistics
    const studentResults = await Promise.all(
      (group.students || []).map(async (membership) => {
        const studentId = membership.student.id;

        // Get results for this student
        const studentTestResults = testResults.filter(result => result.userId === studentId);
        const testsCompleted = studentTestResults.length;

        // Calculate average score for this student
        const studentTotalScore = studentTestResults.reduce((sum, result) => sum + result.score, 0);
        const studentAverageScore = testsCompleted > 0
          ? Math.round(studentTotalScore / testsCompleted)
          : 0;

        return {
          id: studentId,
          name: `${membership.student.firstName} ${membership.student.lastName}`,
          email: membership.student.email,
          testsCompleted,
          averageScore: studentAverageScore,
        };
      })
    );

    return {
      groupId: group.id,
      groupName: group.name,
      studentCount: studentIds.length,
      testCount: testIds.length,
      averageScore,
      completionRate,
      students: studentResults,
    };
  }

  async getStudentResults(groupId: string, studentId: string, teacherId: string): Promise<any> {
    // Check if the group exists and belongs to the teacher
    await this.findGroupById(groupId, teacherId);

    // Check if the student is in the group
    const membership = await (this.prisma as any).studentGroup.findUnique({
      where: {
        groupId_studentId: {
          groupId,
          studentId,
        },
      },
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!membership) {
      throw new NotFoundException(`Student with ID ${studentId} is not in this group`);
    }

    // Get all tests assigned to the group
    const assignments = await (this.prisma as any).groupAssignment.findMany({
      where: { groupId },
      include: {
        test: {
          select: {
            id: true,
            title: true,
            description: true,
            type: true,
            difficulty: true,
          },
        },
      },
    });

    // Get test results for this student
    const testResults = await this.prisma.testResult.findMany({
      where: {
        userId: studentId,
        testId: {
          in: assignments.map((a: any) => a.test.id)
        }
      }
    });

    // Calculate statistics
    const completedTests = testResults.length;
    const totalScore = testResults.reduce((sum, result) => sum + result.score, 0);
    const averageScore = completedTests > 0 ? Math.round(totalScore / completedTests) : 0;

    // Map test results to the response format
    const formattedTestResults = assignments.map((assignment: any) => {
      const result = testResults.find(r => r.testId === assignment.test.id);
      return {
        testId: assignment.test.id,
        testTitle: assignment.test.title,
        assignedAt: assignment.assignedAt,
        dueDate: assignment.dueDate,
        completed: !!result,
        score: result ? result.score : null,
      };
    });

    return {
      studentId: membership.student.id,
      studentName: `${membership.student.firstName} ${membership.student.lastName}`,
      email: membership.student.email,
      groupId: groupId,
      testsAssigned: assignments.length,
      testsCompleted: completedTests,
      averageScore: averageScore,
      testResults: formattedTestResults,
    };
  }

  async getTestQuestionAnalysis(groupId: string, testId: string, teacherId: string): Promise<TestQuestionAnalysisDto> {
    this.logger.log(`Getting test question analysis for group ${groupId}, test ${testId}`);

    // Check if the group exists and belongs to the teacher
    await this.findGroupById(groupId, teacherId);

    // Check if the test exists
    const test = await this.prisma.test.findUnique({
      where: { id: testId },
      include: {
        category: true
      }
    });

    if (!test) {
      throw new NotFoundException(`Test with ID ${testId} not found`);
    }

    // Check if the test is assigned to the group
    const assignment = await (this.prisma as any).groupAssignment.findUnique({
      where: {
        groupId_testId: {
          groupId,
          testId,
        },
      },
    });

    if (!assignment) {
      throw new NotFoundException(`Test with ID ${testId} is not assigned to group with ID ${groupId}`);
    }

    // Get all students in the group
    const studentMemberships = await (this.prisma as any).studentGroup.findMany({
      where: { groupId },
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    const studentIds = studentMemberships.map((membership: any) => membership.student.id);
    const totalStudents = studentIds.length;

    // Get all test results for this test and these students
    const testResults = await this.prisma.testResult.findMany({
      where: {
        testId,
        userId: {
          in: studentIds
        }
      }
    });

    const completedCount = testResults.length;
    const totalScore = testResults.reduce((sum, result) => sum + result.score, 0);
    const averageScore = completedCount > 0 ? Math.round(totalScore / completedCount) : 0;

    // Parse the test answer sheet
    let multipleChoiceAnswers: any[] = [];
    let textAnswers: any[] = [];

    try {
      if (test.multipleChoiceAnswers) {
        multipleChoiceAnswers = typeof test.multipleChoiceAnswers === 'string'
          ? JSON.parse(test.multipleChoiceAnswers as string)
          : test.multipleChoiceAnswers;
      }

      if (test.textAnswers) {
        textAnswers = typeof test.textAnswers === 'string'
          ? JSON.parse(test.textAnswers as string)
          : test.textAnswers;
      }
    } catch (error) {
      this.logger.error(`Error parsing answer sheet for test ${testId}:`, error);
    }

    // Combine all questions
    const allQuestions = [
      ...multipleChoiceAnswers.map((q: any) => ({
        questionNumber: q.questionNumber,
        correctOption: q.correctOption,
        type: QuestionType.MULTIPLE_CHOICE
      })),
      ...textAnswers.map((q: any) => ({
        questionNumber: q.questionNumber,
        correctAnswers: q.correctAnswers,
        type: QuestionType.FILL_IN_THE_BLANK
      }))
    ];

    // Process each question to analyze student answers
    const questionAnalysis: QuestionAnalysisDto[] = await Promise.all(
      allQuestions.map(async (question) => {
        const questionNumber = Number(question.questionNumber);

        // Extract the correct answer(s)
        let correctAnswer: string | string[];
        if (question.type === QuestionType.MULTIPLE_CHOICE) {
          correctAnswer = (question as any).correctOption;
        } else {
          correctAnswer = (question as any).correctAnswers;
        }

        // Initialize counters
        let correctCount = 0;
        let incorrectCount = 0;
        let skippedCount = 0;

        // Track incorrect answers and their frequencies
        const incorrectAnswerMap = new Map<string, number>();

        // Analyze each student's answer for this question
        for (const result of testResults) {
          try {
            // Parse the answers JSON
            const answers = typeof result.answers === 'string'
              ? JSON.parse(result.answers as string)
              : result.answers;

            // Find the answer for this question
            const answer = answers.find((a: any) => Number(a.questionNumber) === questionNumber);

            if (!answer) {
              // Question was skipped
              skippedCount++;
              continue;
            }

            if (answer.isCorrect) {
              correctCount++;
            } else {
              incorrectCount++;

              // Track the incorrect answer
              const answerValue = Array.isArray(answer.answer)
                ? answer.answer.join(', ')
                : String(answer.answer);

              const currentCount = incorrectAnswerMap.get(answerValue) || 0;
              incorrectAnswerMap.set(answerValue, currentCount + 1);
            }
          } catch (error) {
            this.logger.error(`Error processing answers for result ${result.id}:`, error);
          }
        }

        // Convert the map of incorrect answers to an array
        const incorrectAnswers: IncorrectAnswerDto[] = Array.from(incorrectAnswerMap.entries())
          .map(([answer, count]) => ({
            answer,
            count
          }))
          .sort((a, b) => b.count - a.count); // Sort by frequency (descending)

        // Get the question text from the test content
        // This is a simplified approach - in a real implementation, you would parse the test content
        // to extract the question text based on the question number
        const questionText = `Question ${questionNumber}`;

        return {
          questionNumber,
          questionText,
          questionType: question.type,
          correctAnswer,
          correctCount,
          incorrectCount,
          incorrectAnswers,
          skippedCount
        };
      })
    );

    // Sort questions by question number
    questionAnalysis.sort((a, b) => a.questionNumber - b.questionNumber);

    return {
      testId,
      testTitle: test.title,
      groupId,
      totalStudents,
      completedCount,
      averageScore,
      questions: questionAnalysis
    };
  }
}
