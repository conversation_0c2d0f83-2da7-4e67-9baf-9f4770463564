import { Test, TestingModule } from '@nestjs/testing';
import { TeacherService } from './teacher.service';
import { PrismaService } from '../prisma/prisma.service';
import { ForbiddenException, NotFoundException, ConflictException } from '@nestjs/common';
import { Role } from '@prisma/client';
import { CreateGroupInviteDto } from './dto/create-group-invite.dto';
import { TestQuestionAnalysisDto } from './dto/test-question-analysis.dto';

describe('TeacherService', () => {
  let service: TeacherService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
    group: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    studentGroup: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
    },
    groupAssignment: {
      findUnique: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findMany: jest.fn(),
    },
    test: {
      findUnique: jest.fn(),
    },
    testResult: {
      findMany: jest.fn(),
    },
    groupInvite: {
      create: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TeacherService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<TeacherService>(TeacherService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createGroup', () => {
    it('should create a group when user is a teacher', async () => {
      const teacherId = 'teacher-id';
      const createGroupDto = {
        name: 'Test Group',
        level: 'Intermediate',
        description: 'Test description',
      };
      const expectedGroup = {
        id: 'group-id',
        ...createGroupDto,
        createdBy: teacherId,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
      };

      mockPrismaService.user.findUnique.mockResolvedValue({
        id: teacherId,
        role: Role.TEACHER,
      });
      mockPrismaService.group.create.mockResolvedValue(expectedGroup);

      const result = await service.createGroup(createGroupDto, teacherId);

      expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: teacherId },
      });
      expect(mockPrismaService.group.create).toHaveBeenCalledWith({
        data: {
          ...createGroupDto,
          createdBy: teacherId,
        },
      });
      expect(result).toEqual(expectedGroup);
    });

    it('should throw ForbiddenException when user is not a teacher', async () => {
      const userId = 'student-id';
      const createGroupDto = {
        name: 'Test Group',
        level: 'Intermediate',
        description: 'Test description',
      };

      mockPrismaService.user.findUnique.mockResolvedValue({
        id: userId,
        role: Role.STUDENT,
      });

      await expect(service.createGroup(createGroupDto, userId)).rejects.toThrow(
        ForbiddenException,
      );
      expect(mockPrismaService.group.create).not.toHaveBeenCalled();
    });
  });

  describe('findAllGroups', () => {
    it('should return all active groups for a teacher', async () => {
      const teacherId = 'teacher-id';
      const expectedGroups = [
        {
          id: 'group-id-1',
          name: 'Group 1',
          level: 'Beginner',
          createdBy: teacherId,
          isActive: true,
          students: [],
          assignments: [],
        },
        {
          id: 'group-id-2',
          name: 'Group 2',
          level: 'Intermediate',
          createdBy: teacherId,
          isActive: true,
          students: [],
          assignments: [],
        },
      ];

      mockPrismaService.group.findMany.mockResolvedValue(expectedGroups);

      const result = await service.findAllGroups(teacherId);

      expect(mockPrismaService.group.findMany).toHaveBeenCalledWith({
        where: {
          createdBy: teacherId,
          isActive: true,
        },
        include: expect.any(Object),
      });
      expect(result).toEqual(expectedGroups);
    });
  });

  describe('findGroupById', () => {
    it('should return a group by id if it belongs to the teacher', async () => {
      const groupId = 'group-id';
      const teacherId = 'teacher-id';
      const expectedGroup = {
        id: groupId,
        name: 'Test Group',
        level: 'Intermediate',
        createdBy: teacherId,
        isActive: true,
        students: [],
        assignments: [],
      };

      mockPrismaService.group.findUnique.mockResolvedValue(expectedGroup);

      const result = await service.findGroupById(groupId, teacherId);

      expect(mockPrismaService.group.findUnique).toHaveBeenCalledWith({
        where: { id: groupId },
        include: expect.any(Object),
      });
      expect(result).toEqual(expectedGroup);
    });

    it('should throw NotFoundException when group does not exist', async () => {
      const groupId = 'non-existent-group-id';
      const teacherId = 'teacher-id';

      mockPrismaService.group.findUnique.mockResolvedValue(null);

      await expect(service.findGroupById(groupId, teacherId)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw ForbiddenException when group does not belong to the teacher', async () => {
      const groupId = 'group-id';
      const teacherId = 'teacher-id';
      const otherTeacherId = 'other-teacher-id';
      const group = {
        id: groupId,
        name: 'Test Group',
        level: 'Intermediate',
        createdBy: otherTeacherId,
        isActive: true,
        students: [],
        assignments: [],
      };

      mockPrismaService.group.findUnique.mockResolvedValue(group);

      await expect(service.findGroupById(groupId, teacherId)).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  // Group Invitation tests
  describe('createGroupInvite', () => {
    it('should create a group invite with default values', async () => {
      const groupId = 'group-id';
      const teacherId = 'teacher-id';
      const dto: CreateGroupInviteDto = {};

      // Mock group ownership check
      mockPrismaService.group.findUnique.mockResolvedValue({
        id: groupId,
        createdBy: teacherId,
      });

      // Mock invite creation
      const createdInvite = {
        id: 'invite-id',
        token: 'mock-token-123',
        groupId,
        createdById: teacherId,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        useCount: 0,
        isActive: true,
      };
      mockPrismaService.groupInvite.create.mockResolvedValue(createdInvite);

      // Set environment variable for frontend URL
      process.env.FRONTEND_URL = 'https://ielts-toolkit.example.com';

      // Call the service method
      const result = await service.createGroupInvite(groupId, dto, teacherId);

      // Verify the result includes both the invite data and the URL
      expect(result).toMatchObject({
        id: createdInvite.id,
        token: createdInvite.token,
        inviteUrl: `https://ielts-toolkit.example.com/invites/groups/${createdInvite.token}`,
        isActive: true,
        useCount: 0
      });

      // Verify group ownership was checked
      expect(mockPrismaService.group.findUnique).toHaveBeenCalledWith({
        where: { id: groupId },
        include: expect.any(Object),
      });

      // Verify correct data was passed to create
      expect(mockPrismaService.groupInvite.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            groupId,
            createdById: teacherId,
          }),
        })
      );
    });

    it('should create a group invite with custom expiration and max uses', async () => {
      const groupId = 'group-id';
      const teacherId = 'teacher-id';
      const dto: CreateGroupInviteDto = {
        expiresInDays: 14,
        maxUses: 5,
      };

      // Mock group ownership check
      mockPrismaService.group.findUnique.mockResolvedValue({
        id: groupId,
        createdBy: teacherId,
      });

      // Mock invite creation
      const createdInvite = {
        id: 'invite-id',
        token: 'mock-token-123',
        groupId,
        createdById: teacherId,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
        maxUses: 5,
        useCount: 0,
        isActive: true,
      };
      mockPrismaService.groupInvite.create.mockResolvedValue(createdInvite);

      // Call the service method
      const result = await service.createGroupInvite(groupId, dto, teacherId);

      // Verify the result
      expect(result.maxUses).toBe(5);

      // Verify correct data was passed to create
      expect(mockPrismaService.groupInvite.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            maxUses: 5,
            groupId,
            createdById: teacherId,
          }),
        })
      );
    });

    it('should throw NotFoundException when group does not exist', async () => {
      const groupId = 'non-existent-group-id';
      const teacherId = 'teacher-id';
      const dto: CreateGroupInviteDto = {};

      // Mock group not found
      mockPrismaService.group.findUnique.mockResolvedValue(null);

      // Expect the service to throw NotFoundException
      await expect(service.createGroupInvite(groupId, dto, teacherId))
        .rejects.toThrow(NotFoundException);

      // Verify group was looked up
      expect(mockPrismaService.group.findUnique).toHaveBeenCalledWith({
        where: { id: groupId },
        include: expect.any(Object),
      });

      // Verify invite was not created
      expect(mockPrismaService.groupInvite.create).not.toHaveBeenCalled();
    });

    it('should throw ForbiddenException when teacher does not own the group', async () => {
      const groupId = 'group-id';
      const teacherId = 'teacher-id';
      const otherTeacherId = 'other-teacher-id';
      const dto: CreateGroupInviteDto = {};

      // Mock group ownership check to fail
      mockPrismaService.group.findUnique.mockResolvedValue({
        id: groupId,
        createdBy: otherTeacherId, // Different teacher
      });

      // Expect the service to throw ForbiddenException
      await expect(service.createGroupInvite(groupId, dto, teacherId))
        .rejects.toThrow(ForbiddenException);

      // Verify invite was not created
      expect(mockPrismaService.groupInvite.create).not.toHaveBeenCalled();
    });
  });

  describe('getTestQuestionAnalysis', () => {
    const groupId = 'group-id';
    const testId = 'test-id';
    const teacherId = 'teacher-id';

    const mockGroup = {
      id: groupId,
      name: 'Test Group',
      level: 'Intermediate',
      description: 'A test group',
      createdBy: teacherId,
      students: [
        {
          student: {
            id: 'student-1',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
          }
        },
        {
          student: {
            id: 'student-2',
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
          }
        }
      ],
      assignments: [
        {
          test: {
            id: testId,
            title: 'Test Title',
          }
        }
      ]
    };

    const mockTest = {
      id: testId,
      title: 'Test Title',
      description: 'Test Description',
      multipleChoiceAnswers: JSON.stringify([
        {
          questionNumber: 1,
          correctOption: 'A'
        },
        {
          questionNumber: 2,
          correctOption: 'B'
        }
      ]),
      textAnswers: JSON.stringify([
        {
          questionNumber: 3,
          correctAnswers: ['answer1', 'answer2']
        }
      ])
    };

    const mockAssignment = {
      groupId,
      testId,
      assignedAt: new Date(),
      dueDate: new Date()
    };

    const mockStudentMemberships = [
      {
        student: {
          id: 'student-1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
        }
      },
      {
        student: {
          id: 'student-2',
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
        }
      }
    ];

    const mockTestResults = [
      {
        id: 'result-1',
        testId,
        userId: 'student-1',
        score: 80,
        maxScore: 100,
        submittedAt: new Date(),
        timeSpent: 1800,
        totalQuestions: 3,
        correctAnswers: 2,
        incorrectAnswers: 1,
        answers: JSON.stringify([
          {
            questionNumber: 1,
            answer: 'A',
            isCorrect: true
          },
          {
            questionNumber: 2,
            answer: 'C',
            isCorrect: false
          },
          {
            questionNumber: 3,
            answer: 'answer1',
            isCorrect: true
          }
        ])
      },
      {
        id: 'result-2',
        testId,
        userId: 'student-2',
        score: 60,
        maxScore: 100,
        submittedAt: new Date(),
        timeSpent: 1500,
        totalQuestions: 3,
        correctAnswers: 2,
        incorrectAnswers: 1,
        answers: JSON.stringify([
          {
            questionNumber: 1,
            answer: 'A',
            isCorrect: true
          },
          {
            questionNumber: 2,
            answer: 'D',
            isCorrect: false
          },
          {
            questionNumber: 3,
            answer: 'wrong answer',
            isCorrect: false
          }
        ])
      }
    ];

    it('should return test question analysis', async () => {
      // Mock the necessary methods
      mockPrismaService.group.findUnique.mockResolvedValue(mockGroup);
      mockPrismaService.test.findUnique.mockResolvedValue(mockTest);
      mockPrismaService.groupAssignment.findUnique.mockResolvedValue(mockAssignment);
      mockPrismaService.studentGroup.findMany.mockResolvedValue(mockStudentMemberships);
      mockPrismaService.testResult.findMany.mockResolvedValue(mockTestResults);

      // Call the method
      const result = await service.getTestQuestionAnalysis(groupId, testId, teacherId);

      // Verify the result
      expect(result).toBeDefined();
      expect(result.testId).toBe(testId);
      expect(result.testTitle).toBe('Test Title');
      expect(result.groupId).toBe(groupId);
      expect(result.totalStudents).toBe(2);
      expect(result.completedCount).toBe(2);
      expect(result.averageScore).toBe(70);

      // Verify questions analysis
      expect(result.questions).toHaveLength(3);

      // Check first question (multiple choice)
      const q1 = result.questions.find(q => q.questionNumber === 1);
      expect(q1).toBeDefined();
      if (q1) {
        expect(q1.correctCount).toBe(2);
        expect(q1.incorrectCount).toBe(0);
      }

      // Check second question (multiple choice)
      const q2 = result.questions.find(q => q.questionNumber === 2);
      expect(q2).toBeDefined();
      if (q2) {
        expect(q2.correctCount).toBe(0);
        expect(q2.incorrectCount).toBe(2);
        expect(q2.incorrectAnswers).toHaveLength(2);
      }

      // Check third question (fill in the blank)
      const q3 = result.questions.find(q => q.questionNumber === 3);
      expect(q3).toBeDefined();
      if (q3) {
        expect(q3.correctCount).toBe(1);
        expect(q3.incorrectCount).toBe(1);
        expect(q3.incorrectAnswers).toHaveLength(1);
        expect(q3.incorrectAnswers[0].answer).toBe('wrong answer');
        expect(q3.incorrectAnswers[0].count).toBe(1);
      }
    });

    it('should throw NotFoundException if group does not exist', async () => {
      // Mock the necessary methods
      mockPrismaService.group.findUnique.mockResolvedValue(null);

      // Call the method and expect it to throw
      await expect(service.getTestQuestionAnalysis(groupId, testId, teacherId))
        .rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if group does not belong to teacher', async () => {
      // Mock the necessary methods
      mockPrismaService.group.findUnique.mockResolvedValue({
        ...mockGroup,
        createdBy: 'other-teacher-id'
      });

      // Call the method and expect it to throw
      await expect(service.getTestQuestionAnalysis(groupId, testId, teacherId))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw NotFoundException if test does not exist', async () => {
      // Mock the necessary methods
      mockPrismaService.group.findUnique.mockResolvedValue(mockGroup);
      mockPrismaService.test.findUnique.mockResolvedValue(null);

      // Call the method and expect it to throw
      await expect(service.getTestQuestionAnalysis(groupId, testId, teacherId))
        .rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException if test is not assigned to group', async () => {
      // Mock the necessary methods
      mockPrismaService.group.findUnique.mockResolvedValue(mockGroup);
      mockPrismaService.test.findUnique.mockResolvedValue(mockTest);
      mockPrismaService.groupAssignment.findUnique.mockResolvedValue(null);

      // Call the method and expect it to throw
      await expect(service.getTestQuestionAnalysis(groupId, testId, teacherId))
        .rejects.toThrow(NotFoundException);
    });

    it('should handle empty test results', async () => {
      // Mock the necessary methods
      mockPrismaService.group.findUnique.mockResolvedValue(mockGroup);
      mockPrismaService.test.findUnique.mockResolvedValue(mockTest);
      mockPrismaService.groupAssignment.findUnique.mockResolvedValue(mockAssignment);
      mockPrismaService.studentGroup.findMany.mockResolvedValue(mockStudentMemberships);
      mockPrismaService.testResult.findMany.mockResolvedValue([]);

      // Call the method
      const result = await service.getTestQuestionAnalysis(groupId, testId, teacherId);

      // Verify the result
      expect(result).toBeDefined();
      expect(result.testId).toBe(testId);
      expect(result.testTitle).toBe('Test Title');
      expect(result.groupId).toBe(groupId);
      expect(result.totalStudents).toBe(2);
      expect(result.completedCount).toBe(0);
      expect(result.averageScore).toBe(0);

      // Verify questions analysis
      expect(result.questions).toHaveLength(3);

      // All questions should have zero counts
      result.questions.forEach(q => {
        expect(q.correctCount).toBe(0);
        expect(q.incorrectCount).toBe(0);
        expect(q.skippedCount).toBe(0);
        expect(q.incorrectAnswers).toHaveLength(0);
      });
    });
  });
});
