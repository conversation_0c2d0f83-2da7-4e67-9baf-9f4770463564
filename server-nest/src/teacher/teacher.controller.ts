import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, ForbiddenException } from '@nestjs/common';
import { TeacherService } from './teacher.service';
import { CreateGroupDto } from './dto/create-group.dto';
import { UpdateGroupDto } from './dto/update-group.dto';
import { AddStudentsDto } from './dto/add-students.dto';
import { AssignTestDto } from './dto/assign-test.dto';
import { CreateGroupInviteDto } from './dto/create-group-invite.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../common/enums/role.enum';
import { GetUser } from '../auth/decorators/get-user.decorator';
import { User } from '../users/user.entity';

@Controller('teacher')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.TEACHER)
export class TeacherController {
  constructor(private readonly teacherService: TeacherService) {}

  // Group Management
  @Post('groups')
  createGroup(@Body() createGroupDto: CreateGroupDto, @GetUser() user: User) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.createGroup(createGroupDto, user.id);
  }

  @Get('groups')
  findAllGroups(@GetUser() user: User) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.findAllGroups(user.id);
  }

  @Get('groups/:id')
  findGroupById(@Param('id') id: string, @GetUser() user: User) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.findGroupById(id, user.id);
  }

  @Patch('groups/:id')
  updateGroup(
    @Param('id') id: string,
    @Body() updateGroupDto: UpdateGroupDto,
    @GetUser() user: User,
  ) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.updateGroup(id, updateGroupDto, user.id);
  }

  @Delete('groups/:id')
  removeGroup(@Param('id') id: string, @GetUser() user: User) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.removeGroup(id, user.id);
  }

  // Student Management
  @Post('groups/:id/students')
  addStudentsToGroup(
    @Param('id') id: string,
    @Body() addStudentsDto: AddStudentsDto,
    @GetUser() user: User,
  ) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.addStudentsToGroup(id, addStudentsDto, user.id);
  }

  @Delete('groups/:id/students/:studentId')
  removeStudentFromGroup(
    @Param('id') id: string,
    @Param('studentId') studentId: string,
    @GetUser() user: User,
  ) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.removeStudentFromGroup(id, studentId, user.id);
  }

  @Get('groups/:id/students')
  getStudentsInGroup(@Param('id') id: string, @GetUser() user: User) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.getStudentsInGroup(id, user.id);
  }

  // Group Invitation Management
  @Post('groups/:id/invites')
  createGroupInvite(
    @Param('id') id: string,
    @Body() createGroupInviteDto: CreateGroupInviteDto,
    @GetUser() user: User,
  ) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.createGroupInvite(id, createGroupInviteDto, user.id);
  }

  // Test Assignment
  @Post('groups/:id/tests')
  assignTestToGroup(
    @Param('id') id: string,
    @Body() assignTestDto: AssignTestDto,
    @GetUser() user: User,
  ) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.assignTestToGroup(id, assignTestDto, user.id);
  }

  @Delete('groups/:id/tests/:testId')
  removeTestFromGroup(
    @Param('id') id: string,
    @Param('testId') testId: string,
    @GetUser() user: User,
  ) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.removeTestFromGroup(id, testId, user.id);
  }

  @Get('groups/:id/tests')
  getTestsAssignedToGroup(@Param('id') id: string, @GetUser() user: User) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.getTestsAssignedToGroup(id, user.id);
  }

  // Get all students available for adding to groups
  @Get('students')
  getAllStudents(@GetUser() user: User) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.getAllStudents(user.id);
  }

  // Results and Analytics
  @Get('groups/:id/results')
  getGroupResults(@Param('id') id: string, @GetUser() user: User) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.getGroupResults(id, user.id);
  }

  @Get('groups/:id/students/:studentId/results')
  getStudentResults(
    @Param('id') id: string,
    @Param('studentId') studentId: string,
    @GetUser() user: User,
  ) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.getStudentResults(id, studentId, user.id);
  }

  @Get('groups/:groupId/tests/:testId/results')
  getTestQuestionAnalysis(
    @Param('groupId') groupId: string,
    @Param('testId') testId: string,
    @GetUser() user: User,
  ) {
    if (!user.id) {
      throw new ForbiddenException('User ID is required');
    }
    return this.teacherService.getTestQuestionAnalysis(groupId, testId, user.id);
  }
}
