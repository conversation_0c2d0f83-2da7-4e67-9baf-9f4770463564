import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export enum QuestionType {
  MULTIPLE_CHOICE = 'multipleChoice',
  FILL_IN_THE_BLANK = 'fillInTheBlank',
}

export class IncorrectAnswerDto {
  @IsString()
  answer: string;

  @IsNumber()
  count: number;
}

export class QuestionAnalysisDto {
  @IsNumber()
  questionNumber: number;

  @IsString()
  questionText: string;

  @IsEnum(QuestionType)
  questionType: QuestionType;

  @IsString({ each: true })
  correctAnswer: string | string[];

  @IsNumber()
  correctCount: number;

  @IsNumber()
  incorrectCount: number;

  @ValidateNested({ each: true })
  @Type(() => IncorrectAnswerDto)
  @IsArray()
  incorrectAnswers: IncorrectAnswerDto[];

  @IsNumber()
  skippedCount: number;
}

export class TestQuestionAnalysisDto {
  @IsString()
  testId: string;

  @IsString()
  testTitle: string;

  @IsString()
  groupId: string;

  @IsNumber()
  totalStudents: number;

  @IsNumber()
  completedCount: number;

  @IsNumber()
  averageScore: number;

  @ValidateNested({ each: true })
  @Type(() => QuestionAnalysisDto)
  @IsArray()
  questions: QuestionAnalysisDto[];
}
