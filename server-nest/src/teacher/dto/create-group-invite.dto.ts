import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateGroupInviteDto {
  @ApiProperty({
    description: 'Number of days until the invitation expires',
    required: false,
    default: 7,
    minimum: 1,
    maximum: 30,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(30)
  expiresInDays?: number;

  @ApiProperty({
    description: 'Maximum number of times the invitation can be used',
    required: false,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxUses?: number;
}
