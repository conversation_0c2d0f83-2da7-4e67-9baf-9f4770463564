import { ApiProperty } from '@nestjs/swagger';

export class GroupInviteDto {
  @ApiProperty({
    description: 'Unique identifier of the invitation',
    example: 'a1b2c3d4-e5f6-7890-a1b2-c3d4e5f67890',
  })
  id: string;

  @ApiProperty({
    description: 'Unique token used in the invitation URL',
    example: 'a1b2c3d4e5f67890a1b2c3d4e5f67890a1b2c3d4e5f67890a1b2c3d4e5f67890',
  })
  token: string;

  @ApiProperty({
    description: 'Date and time when the invitation was created',
    example: '2025-05-02T12:30:45.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date and time when the invitation expires',
    example: '2025-05-09T12:30:45.000Z',
  })
  expiresAt: Date;

  @ApiProperty({
    description: 'Maximum number of times the invitation can be used',
    example: 10,
    required: false,
  })
  maxUses?: number;

  @ApiProperty({
    description: 'Number of times the invitation has been used',
    example: 2,
  })
  useCount: number;

  @ApiProperty({
    description: 'Whether the invitation is active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Date and time when the invitation was last used',
    example: '2025-05-03T10:15:30.000Z',
    required: false,
  })
  lastUsedAt?: Date;

  @ApiProperty({
    description: 'Full invitation URL that can be shared',
    example: 'https://ielts-toolkit.com/invites/groups/a1b2c3d4e5f67890',
  })
  inviteUrl: string;
}
