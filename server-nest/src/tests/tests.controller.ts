import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Put } from '@nestjs/common';
import { TestsService } from './tests.service';
import { CreateTestDto } from './dto/create-test.dto';
import { UpdateTestDto } from './dto/update-test.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../common/enums/role.enum';
import { GetUser } from '../auth/decorators/get-user.decorator';
import { User } from '../users/user.entity';
import { JwtPayload } from 'jsonwebtoken';

@Controller('tests')
@UseGuards(JwtAuthGuard, RolesGuard)
export class TestsController {
  constructor(private readonly testsService: TestsService) {}

  @Post()
  @Roles(Role.ADMIN, Role.TEACHER)
  create(@Body() createTestDto: CreateTestDto, @GetUser() user: User) {
    return this.testsService.create(createTestDto, user);
  }

  @Get()
  findAll(@GetUser() user: User) {
    return this.testsService.findAll(user);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.testsService.findOne(id);
  }

  @Patch(':id')
  @Roles(Role.ADMIN, Role.TEACHER)
  update(@Param('id') id: string, @Body() updateTestDto: UpdateTestDto) {
    return this.testsService.update(id, updateTestDto);
  }

  @Put(':id')
  @Roles(Role.ADMIN, Role.TEACHER)
  updateWithPut(@Param('id') id: string, @Body() updateTestDto: UpdateTestDto) {
    return this.testsService.update(id, updateTestDto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN)
  remove(@Param('id') id: string) {
    return this.testsService.remove(id);
  }

  @Patch(':id/toggle-published')
  @Roles(Role.TEACHER, Role.ADMIN)
  togglePublished(@Param('id') id: string, @GetUser() user: User) {
    return this.testsService.togglePublished(id, user);
  }

  @Patch(':id/toggle-active')
  @Roles(Role.TEACHER, Role.ADMIN)
  toggleActiveStatus(@Param('id') id: string, @GetUser() user: User) {
    return this.testsService.toggleActiveStatus(id, user);
  }
}