/* eslint-disable */
import { Test } from '@nestjs/testing';
import { TestsController } from './tests.controller';
import { TestsService } from './tests.service';
import { CreateTestDto } from './dto/create-test.dto';
import { UpdateTestDto } from './dto/update-test.dto';
import { User } from '../users/user.entity';
import { Role } from '../common/enums/role.enum';

// Define enum constants instead of importing directly
enum TestType {
  READING = 'READING',
  LISTENING = 'LISTENING',
  WRITING = 'WRITING',
  SPEAKING = 'SPEAKING'
}

enum Difficulty {
  EASY = 'EASY',
  MEDIUM = 'MEDIUM',
  HARD = 'HARD'
}

describe('TestsController', () => {
  let controller: TestsController;
  let service: TestsService;

  const mockTest = {
    id: 'test-id',
    title: 'Test IELTS Reading',
    description: 'Test description',
    type: TestType.READING,
    difficulty: Difficulty.MEDIUM,
    timeLimit: 60,
    duration: 60,
    categoryId: 'category-id',
    isPublished: false,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    maxScore: 100,
    instructions: '',
    multipleChoiceAnswers: null,
    textAnswers: null,
    content: JSON.stringify({ passages: [], questions: [] }),
    category: {
      id: 'category-id',
      name: 'Academic',
    },
  };

  const mockUser: User = {
    id: 'user-id',
    email: '<EMAIL>',
    password: 'hashed_password',
    role: Role.STUDENT,
    firstName: 'Test',
    lastName: 'User',
    name: 'Test User',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockService = {
    create: jest.fn().mockResolvedValue(mockTest),
    findAll: jest.fn().mockResolvedValue([mockTest]),
    findOne: jest.fn().mockResolvedValue(mockTest),
    update: jest.fn().mockResolvedValue(mockTest),
    remove: jest.fn().mockResolvedValue(mockTest),
    toggleActiveStatus: jest.fn().mockResolvedValue({ ...mockTest, isActive: false }),
    togglePublished: jest.fn().mockResolvedValue({ ...mockTest, isPublished: true }),
  };

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [TestsController],
      providers: [
        {
          provide: TestsService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<TestsController>(TestsController);
    service = module.get<TestsService>(TestsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new test', async () => {
      const createTestDto: CreateTestDto = {
        title: 'Test IELTS Reading',
        description: 'Test description',
        type: TestType.READING,
        difficulty: Difficulty.MEDIUM,
        categoryId: 'category-id',
        content: JSON.stringify({ passages: [], questions: [] }),
        durationMinutes: 60,
      };

      const result = await controller.create(createTestDto, mockUser);

      expect(result).toEqual(mockTest);
      expect(service.create).toHaveBeenCalledWith(createTestDto, mockUser);
    });
  });

  describe('findAll', () => {
    it('should return an array of tests', async () => {
      const result = await controller.findAll(mockUser);

      expect(result).toEqual([mockTest]);
      expect(service.findAll).toHaveBeenCalledWith(mockUser);
    });
  });

  describe('findOne', () => {
    it('should return a single test', async () => {
      const result = await controller.findOne('test-id', mockUser);

      expect(result).toEqual(mockTest);
      expect(service.findOne).toHaveBeenCalledWith('test-id', mockUser);
    });
  });

  describe('update', () => {
    it('should update a test', async () => {
      const updateTestDto: UpdateTestDto = {
        title: 'Updated IELTS Reading Test',
      };

      const result = await controller.update('test-id', updateTestDto, mockUser);

      expect(result).toEqual(mockTest);
      expect(service.update).toHaveBeenCalledWith('test-id', updateTestDto, mockUser);
    });
  });

  describe('remove', () => {
    it('should remove a test', async () => {
      const result = await controller.remove('test-id', mockUser);

      expect(result).toEqual(mockTest);
      expect(service.remove).toHaveBeenCalledWith('test-id', mockUser);
    });
  });

  describe('toggleActiveStatus', () => {
    it('should toggle the active status of a test', async () => {
      const result = await controller.toggleActiveStatus('test-id', mockUser);

      expect(result.isActive).toBe(false);
      expect(service.toggleActiveStatus).toHaveBeenCalledWith('test-id', mockUser);
    });
  });

  describe('togglePublished', () => {
    it('should toggle the published status of a test', async () => {
      const result = await controller.togglePublished('test-id', mockUser);

      expect(result.isPublished).toBe(true);
      expect(service.togglePublished).toHaveBeenCalledWith('test-id', mockUser);
    });
  });
});
