import { IsEnum, IsString, IsNotEmpty, IsOptional, IsNumber, IsUUID, IsArray, ValidateNested, IsBoolean } from 'class-validator';
import { TestType, Difficulty } from '@prisma/client';
import { Type } from 'class-transformer';

export class MultipleChoiceAnswerDto {
  @IsNumber()
  @IsNotEmpty()
  questionNumber: number;

  @IsString()
  @IsNotEmpty()
  correctOption: string; // A, B, C, or D
}

export class TextAnswerDto {
  @IsNumber()
  @IsNotEmpty()
  questionNumber: number;

  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  correctAnswers: string[]; // Array of acceptable answers

  @IsOptional()
  @IsBoolean()
  caseSensitive?: boolean;
}

export class AnswerSheetDto {
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MultipleChoiceAnswerDto)
  multipleChoiceAnswers?: MultipleChoiceAnswerDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TextAnswerDto)
  textAnswers?: TextAnswerDto[];
}

export class CreateTestDto {
  @IsNotEmpty({ message: 'Please enter a title' })
  @IsString({ message: 'Title must be a string' })
  title: string;

  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string;

  @IsNotEmpty({ message: 'Please select a test type' })
  @IsEnum(TestType, { message: 'Invalid test type' })
  type: TestType;

  @IsOptional()
  @IsEnum(Difficulty, { message: 'Invalid difficulty level' })
  difficulty?: Difficulty;

  @IsNotEmpty({ message: 'Please select a category' })
  @IsUUID('4', { message: 'Invalid category ID format' })
  categoryId: string;

  @IsOptional()
  @IsNumber({}, { message: 'Time limit must be a number' })
  timeLimit?: number;

  @IsOptional()
  @IsNumber({}, { message: 'Max score must be a number' })
  maxScore?: number;

  @IsOptional()
  @IsString({ message: 'Instructions must be a string' })
  instructions?: string;

  @IsOptional()
  @IsString({ message: 'Content must be a string' })
  content?: string;

  @IsOptional()
  @IsBoolean()
  isPublished?: boolean;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => AnswerSheetDto)
  answerSheet?: AnswerSheetDto;
}