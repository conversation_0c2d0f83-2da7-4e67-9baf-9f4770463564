import { TestType, Difficulty } from '@prisma/client';
import { Category } from '../categories/category.entity';

// This class provides type-safe entities for TypeScript
export class MultipleChoiceAnswer {
  questionNumber: number;
  correctOption: string; // A, B, C, or D
}

export class TextAnswer {
  questionNumber: number;
  correctAnswers: string[]; // Array of acceptable answers
  caseSensitive?: boolean;
}

export class AnswerSheet {
  multipleChoiceAnswers: MultipleChoiceAnswer[];
  textAnswers: TextAnswer[];
}

export interface TestCreator {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export interface Test {
  id: string;
  title: string;
  description?: string;
  content?: string;
  type: string;
  difficulty: string;
  timeLimit: number;
  maxScore: number;
  instructions?: string;
  isPublished: boolean;
  isActive?: boolean;
  categoryId?: string;
  category?: any;
  createdAt: Date;
  updatedAt: Date;
  answerSheet?: AnswerSheet;
  createdBy?: TestCreator | string;
} 