import { Injectable, NotFoundException, InternalServerErrorException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateTestDto } from './dto/create-test.dto';
import { UpdateTestDto } from './dto/update-test.dto';
import { Test, AnswerSheet, MultipleChoiceAnswer, TextAnswer } from './test.entity';
import { Logger } from '@nestjs/common';
import { User } from '../users/user.entity';
import { Role } from '../users/role.enum';

@Injectable()
export class TestsService {
  private readonly logger = new Logger(TestsService.name);

  constructor(private prisma: PrismaService) {}

  async create(createTestDto: CreateTestDto, currentUser?: User): Promise<Test> {
    const { categoryId, answerSheet, ...restData } = createTestDto;

    try {
      // Validate that multiple choice questions have correct options
      if (answerSheet?.multipleChoiceAnswers && answerSheet.multipleChoiceAnswers.length > 0) {
        const invalidQuestions = answerSheet.multipleChoiceAnswers.filter(
          mc => !mc.correctOption || !['A', 'B', 'C', 'D'].includes(mc.correctOption.toUpperCase())
        );

        if (invalidQuestions.length > 0) {
          throw new Error(`Multiple choice questions missing valid correct options: ${invalidQuestions.map(q => q.questionNumber).join(', ')}`);
        }
      }

      // Also validate text answers
      if (answerSheet?.textAnswers && answerSheet.textAnswers.length > 0) {
        const invalidQuestions = answerSheet.textAnswers.filter(
          txt => !txt.correctAnswers || txt.correctAnswers.length === 0 || txt.correctAnswers.every(ans => !ans.trim())
        );

        if (invalidQuestions.length > 0) {
          throw new Error(`Fill in the blank questions missing valid answers: ${invalidQuestions.map(q => q.questionNumber).join(', ')}`);
        }
      }

      // Convert answerSheet arrays to JSON format that Prisma expects
      let multipleChoiceAnswersJson: any = null;
      let textAnswersJson: any = null;

      if (answerSheet?.multipleChoiceAnswers) {
        multipleChoiceAnswersJson = JSON.stringify(answerSheet.multipleChoiceAnswers);
      }

      if (answerSheet?.textAnswers) {
        textAnswersJson = JSON.stringify(answerSheet.textAnswers);
      }

      // Use type assertion to work around TypeScript errors with Prisma schema changes
      const createdTest = await this.prisma.test.create({
        data: {
          ...restData,
          categoryId: categoryId || undefined,
          multipleChoiceAnswers: multipleChoiceAnswersJson,
          textAnswers: textAnswersJson,
          createdById: currentUser?.id, // Set the creator ID if a user is provided
        },
        include: {
          category: true,
          createdBy: true, // Include the creator in the response
        },
      } as any);

      // Type assertion for the created test since TypeScript doesn't recognize our schema changes yet
      const typedTest = createdTest as any;

      // Convert the JSON strings back to objects for the response
      const answerSheetData = {
        multipleChoiceAnswers: typedTest.multipleChoiceAnswers
          ? JSON.parse(typedTest.multipleChoiceAnswers as string)
          : [],
        textAnswers: typedTest.textAnswers
          ? JSON.parse(typedTest.textAnswers as string)
          : []
      };

      // Transform DB model to API response
      const response: Test = {
        id: typedTest.id,
        title: typedTest.title,
        description: typedTest.description || undefined,
        content: typedTest.content || undefined,
        type: typedTest.type,
        difficulty: typedTest.difficulty,
        timeLimit: typedTest.timeLimit,
        maxScore: typedTest.maxScore,
        instructions: typedTest.instructions || undefined,
        isPublished: typedTest.isPublished,
        categoryId: typedTest.categoryId || undefined,
        createdAt: typedTest.createdAt,
        updatedAt: typedTest.updatedAt,
        answerSheet: answerSheetData,
        isActive: typedTest.isActive,
      };

      // Add category if available
      if (typedTest.category) {
        response.category = typedTest.category;
      }

      // Add the createdBy information if available
      if (typedTest.createdBy) {
        response.createdBy = {
          _id: typedTest.createdBy.id,
          firstName: typedTest.createdBy.firstName,
          lastName: typedTest.createdBy.lastName,
          email: typedTest.createdBy.email
        };
      }

      return response;
    } catch (error) {
      this.logger.error(`Failed to create test: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to create test');
    }
  }

  async findAll(currentUser?: User): Promise<Test[]> {
    try {
      // Build query based on user role
      const whereCondition: any = {};
      
      // If user is not admin, filter tests by creator
      if (currentUser && currentUser.role !== Role.ADMIN) {
        whereCondition.createdById = currentUser.id;
      }
      
      // Use explicit casting to handle Prisma include options
      const tests = await this.prisma.test.findMany({
        where: whereCondition,
        include: {
          category: true,
          createdBy: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      } as any);

      // Map database entities to Test models
      return tests.map((dbTest: any) => {
        // Type assertion for the database test
        const typedTest = dbTest as any;

        // Log the raw data for debugging
        this.logger.debug(`Processing test ID: ${typedTest.id}`);
        if (typedTest.multipleChoiceAnswers) {
          this.logger.debug(`multipleChoiceAnswers type: ${typeof typedTest.multipleChoiceAnswers}`);
          this.logger.debug(`multipleChoiceAnswers value: ${JSON.stringify(typedTest.multipleChoiceAnswers).substring(0, 200)}...`);
        }

        if (typedTest.textAnswers) {
          this.logger.debug(`textAnswers type: ${typeof typedTest.textAnswers}`);
          this.logger.debug(`textAnswers value: ${JSON.stringify(typedTest.textAnswers).substring(0, 200)}...`);
        }

        // Convert JSON strings to objects for the answer sheet
        let multipleChoiceAnswersValue = [];
        let textAnswersValue = [];

        try {
          if (typedTest.multipleChoiceAnswers) {
            if (typeof typedTest.multipleChoiceAnswers === 'string') {
              try {
                multipleChoiceAnswersValue = JSON.parse(typedTest.multipleChoiceAnswers as string);
                this.logger.debug('Successfully parsed multipleChoiceAnswers string');
              } catch (error) {
                this.logger.error(`Error parsing multipleChoiceAnswers: ${error.message}`);
                // Default to empty array on error
                multipleChoiceAnswersValue = [];
              }
            } else {
              // Already an object
              multipleChoiceAnswersValue = typedTest.multipleChoiceAnswers;
              this.logger.debug('Using multipleChoiceAnswers as object directly');
            }
          }

          if (typedTest.textAnswers) {
            if (typeof typedTest.textAnswers === 'string') {
              try {
                textAnswersValue = JSON.parse(typedTest.textAnswers as string);
                this.logger.debug('Successfully parsed textAnswers string');
              } catch (error) {
                this.logger.error(`Error parsing textAnswers: ${error.message}`);
                // Default to empty array on error
                textAnswersValue = [];
              }
            } else {
              // Already an object
              textAnswersValue = typedTest.textAnswers;
              this.logger.debug('Using textAnswers as object directly');
            }
          }
        } catch (error) {
          this.logger.error(`Unexpected error processing JSON: ${error.message}`, error.stack);
          // Default to empty arrays if any error occurs
          multipleChoiceAnswersValue = [];
          textAnswersValue = [];
        }

        const answerSheetData = {
          multipleChoiceAnswers: multipleChoiceAnswersValue,
          textAnswers: textAnswersValue
        };

        // Create base test object
        const result: Test = {
          id: typedTest.id,
          title: typedTest.title,
          description: typedTest.description || undefined,
          type: typedTest.type,
          difficulty: typedTest.difficulty,
          timeLimit: typedTest.timeLimit,
          maxScore: typedTest.maxScore,
          instructions: typedTest.instructions || undefined,
          isPublished: typedTest.isPublished,
          categoryId: typedTest.categoryId || undefined,
          createdAt: typedTest.createdAt,
          updatedAt: typedTest.updatedAt,
          answerSheet: answerSheetData,
          isActive: typedTest.isActive
        };

        // Add category if available
        if (typedTest.category) {
          result.category = typedTest.category;
        }

        // Add the createdBy information if available
        if (typedTest.createdBy) {
          result.createdBy = {
            _id: typedTest.createdBy.id,
            firstName: typedTest.createdBy.firstName,
            lastName: typedTest.createdBy.lastName,
            email: typedTest.createdBy.email
          };
        }

        return result;
      });
    } catch (error) {
      this.logger.error(`Failed to find tests: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to retrieve tests');
    }
  }

  async findOne(id: string): Promise<Test> {
    try {
      // Use explicit casting to handle Prisma include options
      const dbTest = await this.prisma.test.findUnique({
        where: { id },
        include: {
          category: true,
          createdBy: true
        },
      } as any);

      if (!dbTest) {
        throw new NotFoundException(`Test with ID ${id} not found`);
      }

      // Transform the raw data into the proper shape with answer sheet
      // Type assertion for the result since TypeScript doesn't recognize our schema changes yet
      const result = dbTest as any;

      // Convert JSON strings to objects for the answer sheet
      const answerSheetData = {
        multipleChoiceAnswers: result.multipleChoiceAnswers
          ? JSON.parse(result.multipleChoiceAnswers as string)
          : [],
        textAnswers: result.textAnswers
          ? JSON.parse(result.textAnswers as string)
          : []
      };

      // Build formatted test object
      const formattedTest: Test = {
        id: result.id,
        title: result.title,
        description: result.description || undefined,
        content: result.content || undefined,
        type: result.type,
        difficulty: result.difficulty,
        timeLimit: result.timeLimit,
        maxScore: result.maxScore,
        instructions: result.instructions || undefined,
        isPublished: result.isPublished,
        categoryId: result.categoryId || undefined,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
        answerSheet: answerSheetData,
        isActive: result.isActive
      };

      // Add category if available
      if (result.category) {
        formattedTest.category = result.category;
      }

      // Add the createdBy information if available
      if (result.createdBy) {
        formattedTest.createdBy = {
          _id: result.createdBy.id,
          firstName: result.createdBy.firstName,
          lastName: result.createdBy.lastName,
          email: result.createdBy.email
        };
      }

      return formattedTest as Test;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to find test: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to retrieve test');
    }
  }

  async update(id: string, updateTestDto: UpdateTestDto): Promise<Test> {
    const { categoryId, answerSheet, ...restData } = updateTestDto;

    const test = await this.prisma.test.findUnique({
      where: { id },
    });

    if (!test) {
      throw new NotFoundException(`Test with ID ${id} not found`);
    }

    // Validate that multiple choice questions have correct options
    if (answerSheet?.multipleChoiceAnswers && answerSheet.multipleChoiceAnswers.length > 0) {
      const invalidQuestions = answerSheet.multipleChoiceAnswers.filter(
        mc => !mc.correctOption || !['A', 'B', 'C', 'D'].includes(mc.correctOption.toUpperCase())
      );

      if (invalidQuestions.length > 0) {
        throw new Error(`Multiple choice questions missing valid correct options: ${invalidQuestions.map(q => q.questionNumber).join(', ')}`);
      }
    }

    // Also validate text answers
    if (answerSheet?.textAnswers && answerSheet.textAnswers.length > 0) {
      const invalidQuestions = answerSheet.textAnswers.filter(
        txt => !txt.correctAnswers || txt.correctAnswers.length === 0 || txt.correctAnswers.every(ans => !ans.trim())
      );

      if (invalidQuestions.length > 0) {
        throw new Error(`Fill in the blank questions missing valid answers: ${invalidQuestions.map(q => q.questionNumber).join(', ')}`);
      }
    }

    const updateData: any = {};

    // Only include fields that are actually provided
    if (restData.title) updateData.title = restData.title;
    if (restData.description !== undefined) updateData.description = restData.description;
    if (restData.content !== undefined) updateData.content = restData.content;
    if (restData.type) updateData.type = restData.type;
    if (restData.difficulty) updateData.difficulty = restData.difficulty;
    if (restData.timeLimit) updateData.timeLimit = restData.timeLimit;
    if (restData.maxScore) updateData.maxScore = restData.maxScore;
    if (restData.instructions !== undefined) updateData.instructions = restData.instructions;
    if (categoryId) updateData.categoryId = categoryId;

    // Handle answerSheet updating if provided
    if (answerSheet?.multipleChoiceAnswers) {
      updateData.multipleChoiceAnswers = JSON.stringify(answerSheet.multipleChoiceAnswers);
    }

    if (answerSheet?.textAnswers) {
      updateData.textAnswers = JSON.stringify(answerSheet.textAnswers);
    }

    const updatedTest = await this.prisma.test.update({
      where: { id },
      data: updateData,
      include: {
        category: true,
      },
    });

    // Convert the JSON strings back to objects for the response
    const answerSheetData = {
      multipleChoiceAnswers: updatedTest.multipleChoiceAnswers
        ? JSON.parse(updatedTest.multipleChoiceAnswers as string)
        : [],
      textAnswers: updatedTest.textAnswers
        ? JSON.parse(updatedTest.textAnswers as string)
        : []
    };

    // Transform DB model to API response
    const response: Test = {
      id: updatedTest.id,
      title: updatedTest.title,
      description: updatedTest.description || undefined,
      content: (updatedTest as any).content || undefined,
      type: updatedTest.type,
      difficulty: updatedTest.difficulty,
      timeLimit: updatedTest.timeLimit,
      maxScore: updatedTest.maxScore,
      instructions: updatedTest.instructions || undefined,
      isPublished: updatedTest.isPublished,
      categoryId: updatedTest.categoryId || undefined,
      category: updatedTest.category || undefined,
      createdAt: updatedTest.createdAt,
      updatedAt: updatedTest.updatedAt,
      answerSheet: answerSheetData,
    };

    return response;
  }

  async remove(id: string): Promise<Test> {
    try {
      const test = await this.findOne(id);
      const dbTest = await this.prisma.test.delete({
        where: { id },
        include: {
          category: true,
        },
      });

      return {
        id: dbTest.id,
        title: dbTest.title,
        description: dbTest.description || undefined,
        content: (dbTest as any).content || undefined,
        type: dbTest.type,
        difficulty: dbTest.difficulty,
        timeLimit: dbTest.timeLimit,
        maxScore: dbTest.maxScore,
        instructions: dbTest.instructions || undefined,
        isPublished: dbTest.isPublished,
        categoryId: dbTest.categoryId || undefined,
        category: dbTest.category || undefined,
        createdAt: dbTest.createdAt,
        updatedAt: dbTest.updatedAt,
        answerSheet: {
          multipleChoiceAnswers: (dbTest.multipleChoiceAnswers as any as MultipleChoiceAnswer[]) || [],
          textAnswers: (dbTest.textAnswers as any as TextAnswer[]) || []
        }
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to remove test: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to remove test');
    }
  }

  async togglePublished(id: string, user: User): Promise<Test> {
    try {
      const test = await this.findOne(id);

      if (!test) {
        throw new NotFoundException(`Test with ID ${id} not found`);
      }

      // Add a check for null user to prevent the error
      if (!user) {
        throw new ForbiddenException('Authentication required to perform this action');
      }

      // Check if the user is an admin (always allowed) or if createdBy is not set
      // If test.createdBy exists, verify user is the creator
      const creatorId = typeof test.createdBy === 'object' && test.createdBy?._id
        ? test.createdBy._id
        : (typeof test.createdBy === 'string' ? test.createdBy : null);

      this.logger.debug(`Toggle published check: user.id=${user.id}, creatorId=${creatorId}, user.role=${user.role}`);

      if (user.role !== Role.ADMIN && creatorId && creatorId !== user.id) {
        throw new ForbiddenException('You do not have permission to publish this test');
      }

      // Toggle the isPublished status
      const updatedTest = await this.prisma.test.update({
        where: { id },
        data: { isPublished: !test.isPublished },
        include: { category: true },
      });

      // Convert the JSON strings back to objects for the response
      const answerSheetData = {
        multipleChoiceAnswers: updatedTest.multipleChoiceAnswers
          ? (typeof updatedTest.multipleChoiceAnswers === 'string'
              ? JSON.parse(updatedTest.multipleChoiceAnswers as string)
              : updatedTest.multipleChoiceAnswers)
          : [],
        textAnswers: updatedTest.textAnswers
          ? (typeof updatedTest.textAnswers === 'string'
              ? JSON.parse(updatedTest.textAnswers as string)
              : updatedTest.textAnswers)
          : []
      };

      // Transform DB model to API response
      const response: Test = {
        id: updatedTest.id,
        title: updatedTest.title,
        description: updatedTest.description || undefined,
        type: updatedTest.type,
        difficulty: updatedTest.difficulty,
        timeLimit: updatedTest.timeLimit,
        maxScore: updatedTest.maxScore,
        instructions: updatedTest.instructions || undefined,
        isPublished: updatedTest.isPublished,
        isActive: updatedTest.isActive,
        categoryId: updatedTest.categoryId || undefined,
        category: updatedTest.category || undefined,
        createdAt: updatedTest.createdAt,
        updatedAt: updatedTest.updatedAt,
        answerSheet: answerSheetData,
      };

      // Add the createdBy information if available in the test object
      if (test.createdBy) {
        if (typeof test.createdBy === 'object' && test.createdBy._id) {
          response.createdBy = test.createdBy;
        } else if (typeof test.createdBy === 'string') {
          response.createdBy = test.createdBy;
        }
      }

      return response;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }
      this.logger.error(`Failed to toggle published status: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to toggle published status');
    }
  }

  async toggleActiveStatus(id: string, user: User): Promise<Test> {
    try {
      const test = await this.findOne(id);

      if (!test) {
        throw new NotFoundException(`Test with ID ${id} not found`);
      }

      // Add a check for null user to prevent the error
      if (!user) {
        throw new ForbiddenException('Authentication required to perform this action');
      }

      // Check if the user is an admin (always allowed) or if createdBy is not set
      // If test.createdBy exists, verify user is the creator
      const creatorId = typeof test.createdBy === 'object' && test.createdBy?._id
        ? test.createdBy._id
        : (typeof test.createdBy === 'string' ? test.createdBy : null);

      this.logger.debug(`Toggle active check: user.id=${user.id}, creatorId=${creatorId}, user.role=${user.role}`);

      if (user.role !== Role.ADMIN && creatorId && creatorId !== user.id) {
        throw new ForbiddenException('You do not have permission to change the active status of this test');
      }

      // Toggle the isActive status
      const updatedTest = await this.prisma.test.update({
        where: { id },
        data: { isActive: !test.isActive },
        include: { category: true },
      });

      // Convert the JSON strings back to objects for the response
      const answerSheetData = {
        multipleChoiceAnswers: updatedTest.multipleChoiceAnswers
          ? (typeof updatedTest.multipleChoiceAnswers === 'string'
              ? JSON.parse(updatedTest.multipleChoiceAnswers as string)
              : updatedTest.multipleChoiceAnswers)
          : [],
        textAnswers: updatedTest.textAnswers
          ? (typeof updatedTest.textAnswers === 'string'
              ? JSON.parse(updatedTest.textAnswers as string)
              : updatedTest.textAnswers)
          : []
      };

      // Transform DB model to API response
      const response: Test = {
        id: updatedTest.id,
        title: updatedTest.title,
        description: updatedTest.description || undefined,
        type: updatedTest.type,
        difficulty: updatedTest.difficulty,
        timeLimit: updatedTest.timeLimit,
        maxScore: updatedTest.maxScore,
        instructions: updatedTest.instructions || undefined,
        isPublished: updatedTest.isPublished,
        isActive: updatedTest.isActive,
        categoryId: updatedTest.categoryId || undefined,
        category: updatedTest.category || undefined,
        createdAt: updatedTest.createdAt,
        updatedAt: updatedTest.updatedAt,
        answerSheet: answerSheetData,
      };

      // Add the createdBy information if available in the test object
      if (test.createdBy) {
        if (typeof test.createdBy === 'object' && test.createdBy._id) {
          response.createdBy = test.createdBy;
        } else if (typeof test.createdBy === 'string') {
          response.createdBy = test.createdBy;
        }
      }

      return response;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }
      this.logger.error(`Failed to toggle active status: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to toggle active status');
    }
  }
}