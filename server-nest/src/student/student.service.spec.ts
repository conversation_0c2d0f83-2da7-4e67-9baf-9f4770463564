import { Test, TestingModule } from '@nestjs/testing';
import { StudentService } from './student.service';
import { PrismaService } from '../prisma/prisma.service';
import { NotFoundException, Logger } from '@nestjs/common';
import { TestWithProgressContent } from './student.service';

// Define test interfaces that match what's used in StudentService
interface TestMockData {
  id: string;
  title: string;
  description: string; // Required field in TestWithProgressContent
  difficulty: string; // Required field in TestWithProgressContent
  timeLimit: number; // Required field in TestWithProgressContent
  type: string; // Required field in TestWithProgressContent
  content: string; // Required field in TestWithProgressContent
  category: {
    name: string;
    id?: string;
    description?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
  };
  questions: TestQuestion[];
  maxScore: number;
  multipleChoiceAnswers: any; // Using any for simplicity in tests
  textAnswers: any; // Using any for simplicity in tests
  answerSheet?: {
    multipleChoiceAnswers?: any;
    textAnswers?: any;
  };
}

interface TestQuestion {
  id: string;
  prompt: string;
  type: string;
  options?: string[];
}

interface TestProgress {
  testId: string;
  userId: string;
  status: string;
  startedAt: Date;
  lastActivityAt: Date;
  lastSaved?: string;
  timeSpent: number;
  flaggedQuestions: number[];
  answers: TestAnswer[];
  currentQuestion?: number;
  submittedAt?: Date;
}

interface TestAnswer {
  questionNumber: number;
  answer: string | string[];
  isCorrect?: boolean;
}

interface TestResult {
  id: string;
  testId: string;
  userId: string;
  score: number;
  maxScore: number;
  submittedAt: Date;
  timeSpent: number;
  totalQuestions: number;
  correctAnswers: number;
  incorrectAnswers: number;
  answers: TestAnswer[];
}

describe('StudentService', () => {
  let service: StudentService;
  let prismaService: PrismaService;

  beforeEach(async () => {
    // Create a mock logger
    const mockLogger = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn()
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StudentService,
        {
          provide: PrismaService,
          useValue: {
            test: {
              findUnique: jest.fn(),
              findMany: jest.fn(),
            },
          },
        },
        {
          provide: Logger,
          useValue: mockLogger
        }
      ],
    }).compile();

    // Override the logger in the service with our mock
    service = module.get<StudentService>(StudentService);
    prismaService = module.get<PrismaService>(PrismaService);

    // We don't need to replace the logger as it's already provided via the module
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('submitTest', () => {
    const testId = 'test-1';
    const userId = 'user-1';

    beforeEach(() => {
      // Mock the test data
      (prismaService.test.findUnique as jest.Mock).mockResolvedValue({
        id: testId,
        title: 'Test Title',
        category: { name: 'Category' },
        multipleChoiceAnswers: JSON.stringify([
          { questionNumber: 1, correctOption: 'A' },
          { questionNumber: 2, correctOption: 'B' },
        ]),
        textAnswers: JSON.stringify([
          { questionNumber: 3, correctAnswers: ['correct answer'] },
          { questionNumber: 4, correctAnswers: ['answer1', 'answer2'] },
        ]),
      });

      // Mock getTestWithProgress method
      jest.spyOn(service, 'getTestWithProgress').mockResolvedValue({
        test: {
          id: testId,
          title: 'Test Title',
          description: '',
          difficulty: 'MEDIUM',
          timeLimit: 60,
          type: 'READING',
          content: '',
          category: {
            name: 'Category',
            id: 'cat-1',
            description: null,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          questions: [
            {
              id: '1',
              prompt: 'Question 1',
              type: 'multipleChoice',
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
            {
              id: '2',
              prompt: 'Question 2',
              type: 'multipleChoice',
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
            { id: '3', prompt: 'Question 3', type: 'fillInTheBlank' },
            { id: '4', prompt: 'Question 4', type: 'fillInTheBlank' },
          ],
          maxScore: 100,
          // The actual properties are passed to the implementation via type assertion
          multipleChoiceAnswers: JSON.stringify([
            { questionNumber: 1, correctOption: 'A' },
            { questionNumber: 2, correctOption: 'B' },
          ]),
          textAnswers: JSON.stringify([
            { questionNumber: 3, correctAnswers: ['correct answer'] },
            { questionNumber: 4, correctAnswers: ['answer1', 'answer2'] },
          ]),
        } as any,
        progress: {
          testId,
          userId,
          status: 'IN_PROGRESS',
          startedAt: new Date('2023-01-01T00:00:00Z'),
          lastActivityAt: new Date(),
          timeSpent: 0,
          flaggedQuestions: [],
          answers: [],
          lastSaved: new Date().toISOString(),
        } as any,
      });
    });

    it('should correctly evaluate multiple choice answers', async () => {
      // Set up progress with answers
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: 'Option A' },
              { questionNumber: 2, answer: 'Option C' }, // wrong answer
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      const result = await service.submitTest(testId, userId);

      expect(result.correctAnswers).toBe(1);
      expect(result.incorrectAnswers).toBe(1);
      expect(result.answers[0].isCorrect).toBe(true);
      expect(result.answers[1].isCorrect).toBe(false);
    });

    it('should correctly evaluate text answers', async () => {
      // Set up progress with answers
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 3, answer: 'correct answer' },
              { questionNumber: 4, answer: 'wrong answer' }, // wrong answer
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      const result = await service.submitTest(testId, userId);

      expect(result.correctAnswers).toBe(1);
      expect(result.incorrectAnswers).toBe(1);
      expect(result.answers[0].isCorrect).toBe(true);
      expect(result.answers[1].isCorrect).toBe(false);
    });

    it('should correctly evaluate mixed question types', async () => {
      // Set up progress with answers to different question types
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: 'Option A' }, // correct multiple choice
              { questionNumber: 3, answer: 'correct answer' }, // correct text answer
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      const result = await service.submitTest(testId, userId);

      expect(result.correctAnswers).toBe(2);
      expect(result.incorrectAnswers).toBe(0);
      expect(result.answers[0].isCorrect).toBe(true); // multiple choice should be correct
      expect(result.answers[1].isCorrect).toBe(true); // text answer should be correct
    });

    it('should handle normalized answer formats', async () => {
      // Set up progress with answers in different formats
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: 'A' }, // just the letter
              { questionNumber: 2, answer: 'option b' }, // lowercase with space
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      const result = await service.submitTest(testId, userId);

      expect(result.correctAnswers).toBe(2);
      expect(result.incorrectAnswers).toBe(0);
      expect(result.answers[0].isCorrect).toBe(true); // 'A' should match 'A'
      expect(result.answers[1].isCorrect).toBe(true); // 'option b' should match 'B' after normalization
    });

    it('should throw NotFoundException if test not found', async () => {
      (prismaService.test.findUnique as jest.Mock).mockResolvedValue(null);

      await expect(service.submitTest('nonexistent', userId)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should correctly evaluate text answers with array of possible answers', async () => {
      // Mock the test data with array of correct answers
      (prismaService.test.findUnique as jest.Mock).mockResolvedValue({
        id: testId,
        title: 'Test Title',
        category: { name: 'Category' },
        multipleChoiceAnswers: JSON.stringify([
          { questionNumber: 1, correctOption: 'A' },
        ]),
        textAnswers: JSON.stringify([
          {
            questionNumber: 3,
            correctAnswers: ['answer1', 'answer2', 'answer3'],
          },
        ]),
      });

      // Mock getTestWithProgress method with array format for textAnswers
      jest.spyOn(service, 'getTestWithProgress').mockResolvedValue({
        test: {
          id: testId,
          title: 'Test Title',
          description: '',
          difficulty: 'MEDIUM',
          timeLimit: 60,
          type: 'READING',
          content: '',
          category: { name: 'Category' },
          questions: [
            {
              id: '1',
              prompt: 'Question 1',
              type: 'multipleChoice',
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
            { id: '3', prompt: 'Question 3', type: 'fillInTheBlank' },
          ],
          maxScore: 100,
          multipleChoiceAnswers: [{ questionNumber: 1, correctOption: 'A' }],
          textAnswers: [
            {
              questionNumber: 3,
              correctAnswers: ['answer1', 'answer2', 'answer3'],
            },
          ],
        } as any,
        progress: {
          testId,
          userId,
          status: 'IN_PROGRESS',
          startedAt: new Date('2023-01-01T00:00:00Z'),
          lastActivityAt: new Date(),
          timeSpent: 0,
          flaggedQuestions: [],
          answers: [],
          lastSaved: new Date().toISOString(),
        } as any,
      });

      // Set up progress with answers - testing each possible correct answer
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 3, answer: 'answer1' }, // correct - first in array
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      let result = await service.submitTest(testId, userId);
      expect(result.answers[0].isCorrect).toBe(true);

      // Test the second correct answer
      service['testProgress'][testId][userId].answers = [
        { questionNumber: 3, answer: 'answer2' }, // correct - second in array
      ];

      result = await service.submitTest(testId, userId);
      expect(result.answers[0].isCorrect).toBe(true);

      // Test the third correct answer
      service['testProgress'][testId][userId].answers = [
        { questionNumber: 3, answer: 'answer3' }, // correct - third in array
      ];

      result = await service.submitTest(testId, userId);
      expect(result.answers[0].isCorrect).toBe(true);

      // Test incorrect answer
      service['testProgress'][testId][userId].answers = [
        { questionNumber: 3, answer: 'wrong answer' }, // incorrect - not in array
      ];

      result = await service.submitTest(testId, userId);
      expect(result.answers[0].isCorrect).toBe(false);
    });

    it('should correctly evaluate text answers with case insensitivity', async () => {
      // Mock getTestWithProgress method with mixed case answers
      jest.spyOn(service, 'getTestWithProgress').mockResolvedValue({
        test: {
          id: testId,
          title: 'Test Title',
          description: '',
          difficulty: 'MEDIUM',
          timeLimit: 60,
          type: 'READING',
          content: '',
          category: { name: 'Category' },
          questions: [
            { id: '3', prompt: 'Question 3', type: 'fillInTheBlank' },
          ],
          maxScore: 100,
          textAnswers: [
            {
              questionNumber: 3,
              correctAnswers: ['Correct Answer', 'ANOTHER answer'],
            },
          ],
        } as any,
        progress: {
          testId,
          userId,
          status: 'IN_PROGRESS',
          startedAt: new Date('2023-01-01T00:00:00Z'),
          lastActivityAt: new Date(),
          timeSpent: 0,
          flaggedQuestions: [],
          answers: [],
          lastSaved: new Date().toISOString(),
        } as any,
      });

      // Test lowercase input matching mixed case correct answer
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 3, answer: 'correct answer' }, // should match 'Correct Answer'
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      let result = await service.submitTest(testId, userId);
      expect(result.answers[0].isCorrect).toBe(true);

      // Test uppercase input matching mixed case correct answer
      service['testProgress'][testId][userId].answers = [
        { questionNumber: 3, answer: 'ANOTHER ANSWER' }, // should match 'ANOTHER answer'
      ];

      result = await service.submitTest(testId, userId);
      expect(result.answers[0].isCorrect).toBe(true);
    });

    it('should correctly evaluate text answers with alternative correctAnswer format', async () => {
      // Mock getTestWithProgress method with correctAnswer (singular) instead of correctAnswers (plural)
      jest.spyOn(service, 'getTestWithProgress').mockResolvedValue({
        test: {
          id: testId,
          title: 'Test Title',
          description: '',
          difficulty: 'MEDIUM',
          timeLimit: 60,
          type: 'READING',
          content: '',
          category: { name: 'Category' },
          questions: [
            { id: '3', prompt: 'Question 3', type: 'fillInTheBlank' },
            { id: '4', prompt: 'Question 4', type: 'fillInTheBlank' },
          ],
          maxScore: 100,
          textAnswers: [
            { questionNumber: 3, correctAnswer: 'single answer' }, // Using singular correctAnswer
            {
              questionNumber: 4,
              correctAnswer: ['multiple', 'possible', 'answers'],
            }, // Using array with singular property name
          ],
        } as any,
        progress: {
          testId,
          userId,
          status: 'IN_PROGRESS',
          startedAt: new Date('2023-01-01T00:00:00Z'),
          lastActivityAt: new Date(),
          timeSpent: 0,
          flaggedQuestions: [],
          answers: [],
          lastSaved: new Date().toISOString(),
        } as any,
      });

      // Test singular correctAnswer string
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [{ questionNumber: 3, answer: 'single answer' }],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      let result = await service.submitTest(testId, userId);
      expect(result.answers[0].isCorrect).toBe(true);

      // Test array with singular correctAnswer property
      service['testProgress'][testId][userId].answers = [
        { questionNumber: 4, answer: 'possible' }, // second item in array
      ];

      result = await service.submitTest(testId, userId);
      expect(result.answers[0].isCorrect).toBe(true);
    });

    it('should handle whitespace trimming in text answers', async () => {
      // Mock getTestWithProgress method with answers that need trimming
      jest.spyOn(service, 'getTestWithProgress').mockResolvedValue({
        test: {
          id: testId,
          title: 'Test Title',
          description: '',
          difficulty: 'MEDIUM',
          timeLimit: 60,
          type: 'READING',
          content: '',
          category: { name: 'Category' },
          questions: [
            { id: '3', prompt: 'Question 3', type: 'fillInTheBlank' },
          ],
          maxScore: 100,
          textAnswers: [
            { questionNumber: 3, correctAnswers: ['trimmed answer'] },
          ],
        } as any,
        progress: {
          testId,
          userId,
          status: 'IN_PROGRESS',
          startedAt: new Date('2023-01-01T00:00:00Z'),
          lastActivityAt: new Date(),
          timeSpent: 0,
          flaggedQuestions: [],
          answers: [],
          lastSaved: new Date().toISOString(),
        } as any,
      });

      // Test answer with extra whitespace
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 3, answer: '  trimmed answer  ' }, // has extra spaces
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      const result = await service.submitTest(testId, userId);
      expect(result.answers[0].isCorrect).toBe(true);
    });

    it('should correctly handle array-type answers without answer definitions', async () => {
      // Mock getTestWithProgress method with a test that doesn't define answers for question 4
      jest.spyOn(service, 'getTestWithProgress').mockResolvedValue({
        test: {
          id: testId,
          title: 'Test Title',
          description: '',
          difficulty: 'MEDIUM',
          timeLimit: 60,
          type: 'READING',
          content: '',
          category: { name: 'Category' },
          questions: [
            {
              id: '1',
              prompt: 'Question 1',
              type: 'multipleChoice',
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
            {
              id: '2',
              prompt: 'Question 2',
              type: 'multipleChoice',
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
            { id: '3', prompt: 'Question 3', type: 'fillInTheBlank' },
            { id: '4', prompt: 'Question 4', type: 'fillInTheBlank' },
          ],
          maxScore: 100,
          multipleChoiceAnswers: [
            { questionNumber: 1, correctOption: 'A' },
            { questionNumber: 2, correctOption: 'B' },
          ],
          textAnswers: [
            { questionNumber: 3, correctAnswers: ['correct answer'] },
          ],
        } as any,
        progress: {
          testId,
          userId,
          status: 'IN_PROGRESS',
          startedAt: new Date('2023-01-01T00:00:00Z'),
          lastActivityAt: new Date(),
          timeSpent: 0,
          flaggedQuestions: [],
          answers: [],
          lastSaved: new Date().toISOString(),
        } as any,
      });

      // Set up progress with different types of array answers for question 4
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: 'A' }, // normal answer
              { questionNumber: 4, answer: ['A'] }, // array answer with one item
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      const result = await service.submitTest(testId, userId);

      // Verify results
      expect(result.correctAnswers).toBe(2); // Question 1 and Question 4 should be correct
      expect(result.incorrectAnswers).toBe(0);
      expect(result.score).toBe(50); // 2 out of 4 questions correct = 50% of maxScore

      expect(result.answers[0].isCorrect).toBe(true); // Question 1 is correct
      expect(result.answers[1].isCorrect).toBe(true); // Question 4 should be marked correct since it has array values

      // Now test with an empty array which should be marked incorrect
      service['testProgress'][testId][userId].answers = [
        { questionNumber: 1, answer: 'A' }, // normal answer
        { questionNumber: 4, answer: [] }, // empty array answer
      ];

      const result2 = await service.submitTest(testId, userId);

      expect(result2.correctAnswers).toBe(1); // Only Question 1 should be correct
      expect(result2.incorrectAnswers).toBe(1); // Question 4 should be incorrect
      expect(result2.answers[1].isCorrect).toBe(false); // Question 4 should be marked incorrect
    });

    it('should maintain array answer handling consistency between submitTest and getTestResult', async () => {
      // Setup test with array answers for question 4 but no definition
      jest.spyOn(service, 'getTestWithProgress').mockResolvedValue({
        test: {
          id: testId,
          title: 'Test Title',
          description: '',
          difficulty: 'MEDIUM',
          timeLimit: 60,
          type: 'READING',
          content: '',
          category: { name: 'Category' },
          questions: [
            {
              id: '1',
              prompt: 'Question 1',
              type: 'multipleChoice',
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
            { id: '4', prompt: 'Question 4', type: 'fillInTheBlank' },
          ],
          maxScore: 100,
          multipleChoiceAnswers: [{ questionNumber: 1, correctOption: 'A' }],
          textAnswers: [],
        } as any,
        progress: {
          testId,
          userId,
          status: 'IN_PROGRESS',
          startedAt: new Date('2023-01-01T00:00:00Z'),
          lastActivityAt: new Date(),
          timeSpent: 0,
          flaggedQuestions: [],
          answers: [],
          lastSaved: new Date().toISOString(),
        } as any,
      });

      // Setup progress with array answers for question 4
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: 'A' }, // correct multiple choice
              { questionNumber: 4, answer: ['A', 'B', 'C'] }, // array answer without definition
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      // First submit the test
      const result = await service.submitTest(testId, userId);
      expect(
        result.answers.find((a) => a.questionNumber === 4)?.isCorrect,
      ).toBe(true);

      // Then check the result is consistent through getTestResult
      const testResult = await service.getTestResult(testId, userId);
      expect(
        testResult.answers.find((a) => a.questionNumber === 4)?.isCorrect,
      ).toBe(true);
    });

    it('should handle malformed JSON in multipleChoiceAnswers', async () => {
      // Mock the test data with malformed JSON
      (prismaService.test.findUnique as jest.Mock).mockResolvedValue({
        id: testId,
        title: 'Test Title',
        category: { name: 'Category' },
        multipleChoiceAnswers: '{invalid-json}',
        textAnswers: JSON.stringify([
          { questionNumber: 3, correctAnswers: ['correct answer'] },
        ]),
      });

      // Override getTestWithProgress to return test data with properly parsed answers
      jest.spyOn(service, 'getTestWithProgress').mockResolvedValue({
        test: {
          id: testId,
          title: 'Test Title',
          description: '',
          difficulty: 'MEDIUM',
          timeLimit: 60,
          type: 'READING',
          content: '',
          category: { name: 'Category' },
          questions: [
            {
              id: '1',
              prompt: 'Question 1',
              type: 'multipleChoice',
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
            { id: '3', prompt: 'Question 3', type: 'fillInTheBlank' },
          ],
          maxScore: 100,
          // Empty multipleChoiceAnswers to simulate parsing error
          multipleChoiceAnswers: [],
          textAnswers: [
            { questionNumber: 3, correctAnswers: ['correct answer'] },
          ],
        } as any,
        progress: {
          testId,
          userId,
          status: 'IN_PROGRESS',
          startedAt: new Date('2023-01-01T00:00:00Z'),
          lastActivityAt: new Date(),
          timeSpent: 0,
          flaggedQuestions: [],
          answers: [],
          lastSaved: new Date().toISOString(),
        } as any,
      });

      // Setup progress with answers
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: 'Option A' },
              { questionNumber: 3, answer: 'correct answer' },
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      // Should still process without crashing - text answers should be valid
      const result = await service.submitTest(testId, userId);

      // The multiple choice question should be marked incorrect due to parsing error
      expect(result.answers.find(a => a.questionNumber === 1)?.isCorrect).toBe(false);
      // Text answer should still be evaluated correctly
      expect(result.answers.find(a => a.questionNumber === 3)?.isCorrect).toBe(true);
    });

    it('should handle malformed JSON in textAnswers', async () => {
      // Mock the test data with malformed JSON
      (prismaService.test.findUnique as jest.Mock).mockResolvedValue({
        id: testId,
        title: 'Test Title',
        category: { name: 'Category' },
        multipleChoiceAnswers: JSON.stringify([
          { questionNumber: 1, correctOption: 'A' },
        ]),
        textAnswers: '{invalid-json}',
      });

      // Override getTestWithProgress to return test data with properly parsed answers
      jest.spyOn(service, 'getTestWithProgress').mockResolvedValue({
        test: {
          id: testId,
          title: 'Test Title',
          description: '',
          difficulty: 'MEDIUM',
          timeLimit: 60,
          type: 'READING',
          content: '',
          category: { name: 'Category' },
          questions: [
            {
              id: '1',
              prompt: 'Question 1',
              type: 'multipleChoice',
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
            { id: '3', prompt: 'Question 3', type: 'fillInTheBlank' },
          ],
          maxScore: 100,
          multipleChoiceAnswers: [
            { questionNumber: 1, correctOption: 'A' },
          ],
          // Empty textAnswers to simulate parsing error
          textAnswers: [],
        } as any,
        progress: {
          testId,
          userId,
          status: 'IN_PROGRESS',
          startedAt: new Date('2023-01-01T00:00:00Z'),
          lastActivityAt: new Date(),
          timeSpent: 0,
          flaggedQuestions: [],
          answers: [],
          lastSaved: new Date().toISOString(),
        } as any,
      });

      // Setup progress with answers
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: 'Option A' },
              { questionNumber: 3, answer: 'correct answer' },
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      // Should still process without crashing
      const result = await service.submitTest(testId, userId);

      // The multiple choice question should be correct
      expect(result.answers.find(a => a.questionNumber === 1)?.isCorrect).toBe(true);
      // Text answer should be marked incorrect due to parsing error
      expect(result.answers.find(a => a.questionNumber === 3)?.isCorrect).toBe(false);
    });

    it('should handle missing progress data', async () => {
      // Clear the progress data
      service['testProgress'] = {};

      // Should create default progress and not crash
      const result = await service.submitTest(testId, userId);

      // Should have no answers
      expect(result.answers.length).toBe(0);
      expect(result.correctAnswers).toBe(0);
      expect(result.incorrectAnswers).toBe(0);
    });

    it('should handle questions without matching answer definitions', async () => {
      // Mock the test data with answer definitions that don't match any questions
      (prismaService.test.findUnique as jest.Mock).mockResolvedValue({
        id: testId,
        title: 'Test Title',
        category: { name: 'Category' },
        multipleChoiceAnswers: JSON.stringify([
          { questionNumber: 99, correctOption: 'A' }, // Non-existent question
        ]),
        textAnswers: JSON.stringify([
          { questionNumber: 999, correctAnswers: ['correct answer'] }, // Non-existent question
        ]),
      });

      // Override getTestWithProgress to ensure it has no matching answer definitions
      jest.spyOn(service, 'getTestWithProgress').mockResolvedValue({
        test: {
          id: testId,
          title: 'Test Title',
          description: '',
          difficulty: 'MEDIUM',
          timeLimit: 60,
          type: 'READING',
          content: '',
          category: { name: 'Category' },
          questions: [
            {
              id: '1',
              prompt: 'Question 1',
              type: 'multipleChoice',
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
            { id: '3', prompt: 'Question 3', type: 'fillInTheBlank' },
          ],
          maxScore: 100,
          // Answer definitions for non-existent questions
          multipleChoiceAnswers: [
            { questionNumber: 99, correctOption: 'A' },
          ],
          textAnswers: [
            { questionNumber: 999, correctAnswers: ['correct answer'] },
          ],
        } as any,
        progress: {
          testId,
          userId,
          status: 'IN_PROGRESS',
          startedAt: new Date('2023-01-01T00:00:00Z'),
          lastActivityAt: new Date(),
          timeSpent: 0,
          flaggedQuestions: [],
          answers: [],
          lastSaved: new Date().toISOString(),
        } as any,
      });

      // Setup progress with answers to questions that don't have answer definitions
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: 'Option A' },
              { questionNumber: 3, answer: 'correct answer' },
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      // Should handle gracefully
      const result = await service.submitTest(testId, userId);

      // Both answers should be marked incorrect due to missing answer definitions
      expect(result.answers.find(a => a.questionNumber === 1)?.isCorrect).toBe(false);
      expect(result.answers.find(a => a.questionNumber === 3)?.isCorrect).toBe(false);
    });

    it('should handle questions with unexpected types', async () => {
      // Mock getTestWithProgress with test having unexpected question types
      jest.spyOn(service, 'getTestWithProgress').mockResolvedValue({
        test: {
          id: testId,
          title: 'Test Title',
          description: '',
          difficulty: 'MEDIUM',
          timeLimit: 60,
          type: 'READING',
          content: '',
          category: { name: 'Category' },
          questions: [
            {
              id: '1',
              prompt: 'Question 1',
              type: 'unsupportedType', // Unexpected type
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
            { id: '2', prompt: 'Question 2', type: null }, // null type
          ],
          maxScore: 100,
          multipleChoiceAnswers: [
            { questionNumber: 1, correctOption: 'A' },
          ],
          textAnswers: [],
        } as any,
        progress: {
          testId,
          userId,
          status: 'IN_PROGRESS',
          startedAt: new Date('2023-01-01T00:00:00Z'),
          lastActivityAt: new Date(),
          timeSpent: 0,
          flaggedQuestions: [],
          answers: [],
          lastSaved: new Date().toISOString(),
        } as any,
      });

      // Setup progress with answers
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: 'Option A' },
              { questionNumber: 2, answer: 'Some answer' },
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      // Should handle gracefully by defaulting to marking answers as incorrect
      const result = await service.submitTest(testId, userId);

      // Both answers should be marked incorrect due to unsupported types
      expect(result.answers.find(a => a.questionNumber === 1)?.isCorrect).toBe(false);
      expect(result.answers.find(a => a.questionNumber === 2)?.isCorrect).toBe(false);
    });

    it('should handle invalid answer formats', async () => {
      // Setup progress with malformed answers
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: null }, // null answer
              { questionNumber: 2, answer: undefined }, // undefined answer
              { questionNumber: 3, answer: {} }, // object answer (invalid)
              { questionNumber: 4, answer: true }, // boolean answer (invalid)
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      // Should handle gracefully
      const result = await service.submitTest(testId, userId);

      // All answers should be marked incorrect due to invalid formats
      expect(result.answers.find(a => a.questionNumber === 1)?.isCorrect).toBe(false);
      expect(result.answers.find(a => a.questionNumber === 2)?.isCorrect).toBe(false);
      expect(result.answers.find(a => a.questionNumber === 3)?.isCorrect).toBe(false);
      expect(result.answers.find(a => a.questionNumber === 4)?.isCorrect).toBe(false);
    });

    it('should handle empty answers array', async () => {
      // Mock the test data
      (prismaService.test.findUnique as jest.Mock).mockResolvedValue({
        id: testId,
        title: 'Test Title',
        category: { name: 'Category' },
        multipleChoiceAnswers: JSON.stringify([
          { questionNumber: 1, correctOption: 'A' },
        ]),
        textAnswers: JSON.stringify([
          { questionNumber: 3, correctAnswers: ['correct answer'] },
        ]),
      });

      // Setup empty progress answers
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      // Should handle gracefully and return 0 score
      const result = await service.submitTest(testId, userId);

      expect(result.score).toBe(0);
      expect(result.answers).toHaveLength(0);
    });

    it('should handle when test progress exists but has no answers property', async () => {
      // Mock the test data
      (prismaService.test.findUnique as jest.Mock).mockResolvedValue({
        id: testId,
        title: 'Test Title',
        category: { name: 'Category' },
        multipleChoiceAnswers: JSON.stringify([
          { questionNumber: 1, correctOption: 'A' },
        ]),
        textAnswers: JSON.stringify([
          { questionNumber: 3, correctAnswers: ['correct answer'] },
        ]),
      });

      // Setup progress without answers property
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
            // No answers property
          } as any,
        },
      };

      // Should handle gracefully and return 0 score
      const result = await service.submitTest(testId, userId);

      expect(result.score).toBe(0);
      expect(result.answers).toHaveLength(0);
    });

    it('should handle when no test progress exists', async () => {
      // Mock the test data
      (prismaService.test.findUnique as jest.Mock).mockResolvedValue({
        id: testId,
        title: 'Test Title',
        category: { name: 'Category' },
        multipleChoiceAnswers: JSON.stringify([
          { questionNumber: 1, correctOption: 'A' },
        ]),
        textAnswers: JSON.stringify([
          { questionNumber: 3, correctAnswers: ['correct answer'] },
        ]),
      });

      // No progress setup at all
      service['testProgress'] = {};

      // Should handle gracefully and return 0 score
      const result = await service.submitTest(testId, userId);

      expect(result.score).toBe(0);
      expect(result.answers).toHaveLength(0);
    });
  });

  describe('getTestResult', () => {
    const testId = 'test-1';
    const userId = 'user-1';

    beforeEach(() => {
      // Reset in-memory storage
      service['testResults'] = {};
      service['testProgress'] = {};

      // Mock the getTestWithProgress method
      jest.spyOn(service, 'getTestWithProgress').mockResolvedValue({
        test: {
          id: testId,
          title: 'Test Title',
          description: '',
          difficulty: 'MEDIUM',
          timeLimit: 60,
          type: 'READING',
          content: '',
          category: { name: 'Category' },
          questions: [
            {
              id: '1',
              prompt: 'Question 1',
              type: 'multipleChoice',
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
            { id: '3', prompt: 'Question 3', type: 'fillInTheBlank' },
          ],
          maxScore: 100,
          multipleChoiceAnswers: [
            { questionNumber: 1, correctOption: 'A' },
          ],
          textAnswers: [
            { questionNumber: 3, correctAnswers: ['correct answer'] },
          ],
        } as any,
        progress: {
          testId,
          userId,
          status: 'IN_PROGRESS',
          startedAt: new Date('2023-01-01T00:00:00Z'),
          lastActivityAt: new Date(),
          timeSpent: 0,
          flaggedQuestions: [],
          answers: [],
          lastSaved: new Date().toISOString(),
        } as any,
      });
    });

    it('should return empty result when no data exists', async () => {
      const result = await service.getTestResult(testId, userId);

      expect(result.score).toBe(0);
      expect(result.answers.length).toBe(0);
      expect(result.correctAnswers).toBe(0);
      expect(result.incorrectAnswers).toBe(0);
    });

    it('should create partial result from progress with unsubmitted answers', async () => {
      // Setup progress with answers but not submitted
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: 'A' },
              { questionNumber: 3, answer: 'correct answer' },
            ],
            status: 'IN_PROGRESS', // Not submitted
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 600, // 10 minutes
            flaggedQuestions: [],
          },
        },
      };

      const result = await service.getTestResult(testId, userId);

      // Score should be 0 for unsubmitted test
      expect(result.score).toBe(0);
      // Answers should be included but marked as incorrect since it's unsubmitted
      expect(result.answers.length).toBe(2);
      expect(result.answers[0].isCorrect).toBe(false);
      expect(result.answers[1].isCorrect).toBe(false);
    });

    it('should handle errors in getTestResult gracefully', async () => {
      // Make getTestWithProgress throw an error
      jest.spyOn(service, 'getTestWithProgress').mockRejectedValue(new Error('Test error'));

      // Setup submitted progress
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: 'A' },
            ],
            status: 'SUBMITTED',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            submittedAt: new Date(),
            lastActivityAt: new Date(),
            timeSpent: 600,
            flaggedQuestions: [],
          },
        },
      };

      // Should throw the error
      await expect(service.getTestResult(testId, userId)).rejects.toThrow('Test error');
    });

    it('should handle when testResults exists but has no matching result ID', async () => {
      // Setup testResults with non-matching ID
      service['testResults'] = {
        'different-test-id': {
          'different-user-id': {
            id: 'result-id',
            testId: 'different-test-id',
            userId: 'different-user-id',
            score: 100,
            answers: [],
            submittedAt: new Date(),
          },
        },
      };

      // Setup progress with answers to fall back to
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            answers: [
              { questionNumber: 1, answer: 'Option A' },
            ],
            status: 'IN_PROGRESS',
            startedAt: new Date('2023-01-01T00:00:00Z'),
            lastActivityAt: new Date(),
            timeSpent: 0,
            flaggedQuestions: [],
          },
        },
      };

      const result = await service.getTestResult(testId, userId);

      // Should fall back to progress answers
      expect(result.answers).toHaveLength(1);
      expect(result.score).toBe(0); // Score should be 0 for unsubmitted tests
    });

    it('should return correct data when testResults has the result', async () => {
      // Create a fixed date for consistent testing
      const fixedDate = new Date('2025-01-01T12:00:00Z');

      // Create a complete mock result with all required fields
      const mockResult = {
        id: 'result-id',
        testId,
        userId,
        score: 75,
        maxScore: 100,
        correctAnswers: 1,
        incorrectAnswers: 1,
        totalQuestions: 2,
        timeSpent: 0,
        answers: [
          { questionNumber: 1, answer: 'Option A', isCorrect: true, type: 'multipleChoice' },
          { questionNumber: 2, answer: 'Option B', isCorrect: false, type: 'multipleChoice' },
        ],
        submittedAt: fixedDate,
      };

      // Clear any existing test results
      service['testResults'] = {};

      // Setup testResults with the result
      service['testResults'][testId] = {};
      service['testResults'][testId][userId] = { ...mockResult };

      // Mock getTestWithProgress to not be called in this test
      jest.spyOn(service, 'getTestWithProgress').mockImplementation(() => {
        throw new Error('getTestWithProgress should not be called in this test');
      });

      const result = await service.getTestResult(testId, userId);

      // Should return the stored result
      expect(result).toEqual(mockResult);
    });

    it('should handle test progress with incomplete data', async () => {
      // No testResults
      service['testResults'] = {};

      // Setup incomplete progress
      service['testProgress'] = {
        [testId]: {
          [userId]: {
            // Missing startedAt, timeSpent, etc.
            status: 'IN_PROGRESS',
            answers: [
              { questionNumber: 1, answer: 'Option A' },
            ],
          } as any,
        },
      };

      const result = await service.getTestResult(testId, userId);

      // Should still return data from the incomplete progress
      expect(result.answers).toHaveLength(1);
      expect(result.score).toBe(0);
    });
  });
});
