import { <PERSON>, Get, Post, Body, Param, UseGuards, Request, Query, Patch, ForbiddenException, NotFoundException, OnModuleInit } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { StudentService } from './student.service';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../common/enums/role.enum';
import { ProgressDto, AnswerDto, FlagDto } from './dto/progress.dto';
import { GetUser } from '../auth/decorators/get-user.decorator';
import { User } from '../users/user.entity';
import { PrismaService } from '../prisma/prisma.service';

@Controller('student')
@UseGuards(JwtAuthGuard)
export class StudentController implements OnModuleInit {
  constructor(
    private readonly studentService: StudentService,
    private readonly prisma: PrismaService
  ) {
    console.log('StudentController initialized');
  }

  // Initialize with test data if none exists
  async onModuleInit() {
    try {
      // Check if we have any tests in the database
      const testCount = await this.prisma.test.count();

      if (testCount === 0) {
        console.log('No tests found in database. Creating sample test...');

        // Create a sample category if none exists
        let category = await this.prisma.category.findFirst();
        if (!category) {
          // Find an admin user to set as creator
          const adminUser = await this.prisma.user.findFirst({
            where: { role: 'ADMIN' }
          });

          // Use type assertion to work around TypeScript errors with Prisma schema changes
          category = await this.prisma.category.create({
            data: {
              name: 'Reading',
              description: 'Reading tests for IELTS',
              createdById: adminUser?.id // Set the creator ID if an admin user exists
            }
          } as any);
        }

        // Create a sample test
        await this.prisma.test.create({
          data: {
            id: '14445e0a-0177-4e0b-af3a-f0c5c7413af8', // Using a fixed ID for easy testing
            title: 'IELTS Reading Practice Test',
            description: '<p>This is a sample reading passage for the IELTS test.</p><p>It contains multiple paragraphs with information that students need to read and understand.</p>',
            timeLimit: 60,
            difficulty: 'MEDIUM',
            type: 'READING',
            isPublished: true,
            categoryId: category.id,
            multipleChoiceAnswers: [
              { questionNumber: 1, correctOption: 'B' },
              { questionNumber: 2, correctOption: 'C' }
            ],
            textAnswers: [
              { questionNumber: 3, correctAnswer: 'Paris' },
              { questionNumber: 4, correctAnswer: 'Berlin' }
            ]
          }
        });

        console.log('Sample test created successfully.');
      }
    } catch (error) {
      console.error('Error initializing test data:', error);
    }
  }

  // PUBLISHED TESTS LIST
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.STUDENT)
  @Get('tests/published')
  async getPublishedTests(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10
  ) {
    try {
      // Convert string parameters to numbers
      const pageNum = Number(page) || 1;
      const limitNum = Number(limit) || 10;

      // Get paginated tests
      const result = await this.studentService.getPublishedTests(pageNum, limitNum);

      // Return the paginated response
      return result;
    } catch (error) {
      console.error('Error getting published tests:', error);
      // Return empty paginated response in case of error
      return {
        tests: [],
        meta: {
          totalItems: 0,
          itemsPerPage: Number(limit) || 10,
          totalPages: 0,
          currentPage: Number(page) || 1
        }
      };
    }
  }

  // AUTHENTICATED ENDPOINTS
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.STUDENT)
  @Get('tests/:id')
  async getTestWithProgress(
    @Param('id') id: string,
    @GetUser() user: User,
    @Request() req
  ) {
    try {
      const userId = user?.id || req.user?.sub || req.user?.id;

      if (!userId) {
        throw new Error('User ID is required to get test progress');
      }

      return await this.studentService.getTestWithProgress(id, userId);
    } catch (error) {
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.STUDENT)
  @Post('tests/:id/start')
  async startTest(
    @Param('id') id: string,
    @GetUser() user: User,
    @Request() req
  ) {
    const userId = user?.id || req.user?.sub || req.user?.id;

    if (!userId) {
      throw new Error('User ID is required to start a test');
    }

    return this.studentService.startTest(id, userId);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.STUDENT)
  @Patch('tests/:id/progress')
  async saveProgress(
    @Param('id') id: string,
    @Body() progressDto: ProgressDto,
    @GetUser() user: User,
    @Request() req
  ) {
    try {
      // Try to get userId from the User decorator first, then fall back to request
      const userId = user?.id || req.user?.sub || req.user?.id;

      console.log(`[saveProgress] Processing request for testId=${id}, userId=${userId}`);
      console.log('[saveProgress] User object:', JSON.stringify(user, null, 2));
      console.log('[saveProgress] Request user object:', JSON.stringify(req.user, null, 2));

      if (!userId) {
        throw new Error('User ID is required to save test progress');
      }

      console.log(`[saveProgress] Received progress data:`, JSON.stringify(progressDto));

      if (progressDto.answers) {
        console.log(`[saveProgress] Number of answers received: ${progressDto.answers.length}`);
        progressDto.answers.forEach((answer, idx) => {
          console.log(`[saveProgress] Answer ${idx+1}: question=${answer.questionNumber}, answer=${JSON.stringify(answer.answer)}`);
        });
      }

      const result = await this.studentService.saveProgress(id, userId, progressDto);
      console.log(`[saveProgress] Progress saved successfully, returned ${result.answers?.length || 0} answers`);
      return result;
    } catch (error) {
      console.error(`[saveProgress] Error:`, error);
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.STUDENT)
  @Patch('tests/:id/flag')
  async flagQuestion(
    @Param('id') id: string,
    @Body() flagDto: FlagDto,
    @GetUser() user: User,
    @Request() req
  ) {
    try {
      const userId = user?.id || req.user?.sub || req.user?.id;

      if (!userId) {
        throw new Error('User ID is required to flag a question');
      }

      return await this.studentService.toggleQuestionFlag(id, userId, flagDto.questionNumber);
    } catch (error) {
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.STUDENT)
  @Post('tests/:id/submit')
  async submitTest(
    @Param('id') id: string,
    @GetUser() user: User,
    @Request() req
  ) {
    try {
      // Try to get userId from the User decorator first, then fall back to request
      const userId = user?.id || req.user?.sub || req.user?.id;

      console.log(`[submitTest] Processing submission for testId=${id}, userId=${userId}`);
      console.log('[submitTest] User object:', JSON.stringify(user, null, 2));
      console.log('[submitTest] Request user object:', JSON.stringify(req.user, null, 2));

      if (!userId) {
        throw new Error('User ID is required to submit a test');
      }

      const result = await this.studentService.submitTest(id, userId);

      console.log(`[submitTest] Test submitted successfully`);
      console.log(`[submitTest] Result contains ${result.answers?.length || 0} answers`);
      console.log(`[submitTest] Score: ${result.score}/${result.maxScore}`);

      return result;
    } catch (error) {
      console.error(`[submitTest] Error:`, error);
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.STUDENT)
  @Get('tests/:id/result')
  async getLatestTestResult(
    @Param('id') id: string,
    @GetUser() user: User
  ) {
    try {
      console.log(`[getLatestTestResult] Fetching latest result for testId=${id}, userId=${user.id || 'unknown'}`);

      const result = await this.studentService.getTestResult(id, user.id || '');

      console.log(`[getLatestTestResult] Retrieved result with id=${result.id}`);
      console.log(`[getLatestTestResult] Result contains ${result.answers?.length || 0} answers`);

      console.log(`[getLatestTestResult] Result data:`, JSON.stringify({
        id: result.id,
        testTitle: result.testTitle,
        score: result.score,
        maxScore: result.maxScore,
        totalQuestions: result.totalQuestions,
        correctAnswers: result.correctAnswers,
        incorrectAnswers: result.incorrectAnswers,
        submittedAt: result.submittedAt,
        answersCount: result.answers?.length || 0
      }));

      return result;
    } catch (error) {
      console.error(`[getLatestTestResult] Error:`, error);
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.STUDENT)
  @Get('tests/:id/result/:resultId')
  async getTestResult(
    @Param('id') id: string,
    @Param('resultId') resultId: string,
    @GetUser() user: User
  ) {
    try {
      console.log(`[getTestResult] Fetching result for testId=${id}, userId=${user.id || 'unknown'}, resultId=${resultId}`);

      const result = await this.studentService.getTestResult(id, user.id || '', resultId);

      console.log(`[getTestResult] Retrieved result with id=${result.id}`);
      console.log(`[getTestResult] Result contains ${result.answers?.length || 0} answers`);

      console.log(`[getTestResult] Result data:`, JSON.stringify({
        id: result.id,
        testTitle: result.testTitle,
        score: result.score,
        maxScore: result.maxScore,
        totalQuestions: result.totalQuestions,
        correctAnswers: result.correctAnswers,
        incorrectAnswers: result.incorrectAnswers,
        submittedAt: result.submittedAt,
        answersCount: result.answers?.length || 0
      }));

      return result;
    } catch (error) {
      console.error(`[getTestResult] Error:`, error);
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.STUDENT)
  @Get('results')
  async getTestResults(
    @GetUser() user: User,
    @Query('testId') testId?: string
  ) {
    try {
      console.log(`[getTestResults] Fetching results for userId=${user.id || 'unknown'}, testId=${testId || 'all'}`);
      const results = await this.studentService.getTestResults(user.id || '', testId);
      console.log(`[getTestResults] Found ${results.length} results`);
      return results;
    } catch (error) {
      console.error(`[getTestResults] Error:`, error);
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.STUDENT)
  @Get('tests/:id/results')
  async getTestResultsByTest(
    @Param('id') id: string,
    @GetUser() user: User
  ) {
    try {
      console.log(`[getTestResultsByTest] Fetching results for testId=${id}, userId=${user.id || 'unknown'}`);
      const results = await this.studentService.getTestResults(user.id || '', id);
      console.log(`[getTestResultsByTest] Found ${results.length} results`);
      return results;
    } catch (error) {
      console.error(`[getTestResultsByTest] Error:`, error);
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.STUDENT)
  @Post('tests/:id/pause')
  async pauseTest(
    @Param('id') id: string,
    @GetUser() user: User,
    @Request() req
  ) {
    const userId = user?.id || req.user?.sub || req.user?.id;

    if (!userId) {
      throw new Error('User ID is required to pause a test');
    }

    return this.studentService.pauseTest(id, userId);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.STUDENT)
  @Post('tests/:id/resume')
  async resumeTest(
    @Param('id') id: string,
    @GetUser() user: User,
    @Request() req
  ) {
    const userId = user?.id || req.user?.sub || req.user?.id;

    if (!userId) {
      throw new Error('User ID is required to resume a test');
    }

    return this.studentService.resumeTest(id, userId);
  }

  @Get('test-answer')
  async getTestAnswer(
    @Param('id') id: string,
    @Request() req
  ) {
    try {
      // Get user ID from request
      const userId = req.user.sub;

      // For now, just return mock data
      const answer = {
        id: '1',
        testId: id || 'test-id-1',
        submittedAt: new Date(),
        answers: [
          {
            questionNumber: 1,
            answer: 'A',
            isCorrect: true
          },
          {
            questionNumber: 2,
            answer: 'B',
            isCorrect: false
          }
        ]
      };

      return answer;
    } catch (error) {
      throw error;
    }
  }
}