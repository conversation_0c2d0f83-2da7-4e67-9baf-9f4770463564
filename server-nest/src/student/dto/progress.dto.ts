import { <PERSON><PERSON><PERSON>y, <PERSON><PERSON><PERSON>ber, IsO<PERSON>al, IsString, IsNotEmpty } from 'class-validator';

export class AnswerDto {
  @IsNumber()
  @IsNotEmpty()
  questionNumber: number;

  @IsNotEmpty()
  answer: string | string[];
}

export class ProgressDto {
  @IsArray()
  @IsOptional()
  answers?: AnswerDto[];

  @IsOptional()
  @IsArray()
  flaggedQuestions?: number[];
}

export class FlagDto {
  @IsNumber()
  @IsNotEmpty()
  questionNumber: number;
} 