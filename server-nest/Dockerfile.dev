FROM node:18-alpine

WORKDIR /app

# Install OpenSSL and other dependencies
RUN apk add --no-cache \
    openssl \
    openssl-dev \
    libc6-compat \
    curl

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy Prisma schema
COPY prisma ./prisma/

# Generate Prisma client
RUN npx prisma generate

# Copy source code (not required if mounted as volume)
COPY . .

# Expose the port
EXPOSE 5000

# Start in development mode
CMD ["npm", "run", "start:dev"] 