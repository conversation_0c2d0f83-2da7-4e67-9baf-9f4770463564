# Railway Environment Configuration
# Copy these variables to your Railway service environment variables

# Database (Railway will auto-populate this from PostgreSQL service)
DATABASE_URL=postgresql://username:password@host:port/database

# Application
NODE_ENV=production
PORT=8080

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this

# CORS (Update with your frontend Railway URL)
CORS_ORIGIN=https://your-frontend-url.railway.app
FRONTEND_URL=https://your-frontend-url.railway.app

# Optional: Logging
LOG_LEVEL=info
