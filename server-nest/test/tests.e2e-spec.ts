import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { JwtService } from '@nestjs/jwt';
import { UserRole } from '../src/users/user.schema';
import { TestType, TestDifficulty } from '../src/tests/test.schema';

describe('Tests E2E', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let testId: string;
  let teacherToken: string;
  let studentToken: string;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    jwtService = moduleFixture.get<JwtService>(JwtService);
    await app.init();

    // Create tokens for testing
    teacherToken = jwtService.sign({
      sub: '123456789012345678901234', // 24 characters for ObjectId
      email: '<EMAIL>',
      role: UserRole.Teacher,
    });

    studentToken = jwtService.sign({
      sub: '123456789012345678902345',
      email: '<EMAIL>',
      role: UserRole.Student,
    });
  });

  afterAll(async () => {
    await app.close();
  });

  it('should be defined', () => {
    expect(app).toBeDefined();
  });

  describe('/tests (POST)', () => {
    it('should create a new test (teacher role)', async () => {
      const createTestDto = {
        title: 'E2E Test IELTS Reading',
        description: 'Test created by E2E test',
        type: TestType.Reading,
        difficulty: TestDifficulty.Medium,
        category: '123456789012345678903456', // Assuming this category exists
        content: { passages: ['Test passage'], questions: ['Test question'] },
        durationMinutes: 60,
      };

      const response = await request(app.getHttpServer())
        .post('/tests')
        .set('Authorization', `Bearer ${teacherToken}`)
        .send(createTestDto)
        .expect(201);

      expect(response.body).toHaveProperty('_id');
      expect(response.body.title).toBe(createTestDto.title);

      testId = response.body._id; // Save for later tests
    });

    it('should not allow students to create tests', async () => {
      const createTestDto = {
        title: 'Student Test IELTS Reading',
        description: 'Test created by student',
        type: TestType.Reading,
        difficulty: TestDifficulty.Medium,
        category: '123456789012345678903456',
        content: { passages: ['Test passage'], questions: ['Test question'] },
        durationMinutes: 60,
      };

      await request(app.getHttpServer())
        .post('/tests')
        .set('Authorization', `Bearer ${studentToken}`)
        .send(createTestDto)
        .expect(403);
    });
  });

  describe('/tests (GET)', () => {
    it('should return an array of tests', async () => {
      const response = await request(app.getHttpServer())
        .get('/tests')
        .set('Authorization', `Bearer ${teacherToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });
  });

  describe('/tests/:id (GET)', () => {
    it('should return a test by id', async () => {
      // Skip if testId wasn't set (create test failed)
      if (!testId) {
        return;
      }

      const response = await request(app.getHttpServer())
        .get(`/tests/${testId}`)
        .set('Authorization', `Bearer ${teacherToken}`)
        .expect(200);

      expect(response.body._id).toBe(testId);
    });

    it('should return 404 for non-existent test', async () => {
      await request(app.getHttpServer())
        .get('/tests/123456789012345678909999')
        .set('Authorization', `Bearer ${teacherToken}`)
        .expect(404);
    });
  });

  describe('/tests/:id (PATCH)', () => {
    it('should update a test', async () => {
      // Skip if testId wasn't set (create test failed)
      if (!testId) {
        return;
      }

      const updateTestDto = {
        title: 'Updated E2E Test',
        description: 'Updated by E2E test',
      };

      const response = await request(app.getHttpServer())
        .patch(`/tests/${testId}`)
        .set('Authorization', `Bearer ${teacherToken}`)
        .send(updateTestDto)
        .expect(200);

      expect(response.body.title).toBe(updateTestDto.title);
      expect(response.body.description).toBe(updateTestDto.description);
    });
  });

  describe('/tests/:id/toggle-publish (PATCH)', () => {
    it('should toggle publish status', async () => {
      // Skip if testId wasn't set (create test failed)
      if (!testId) {
        return;
      }

      const response = await request(app.getHttpServer())
        .patch(`/tests/${testId}/toggle-publish`)
        .set('Authorization', `Bearer ${teacherToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('isPublished');
    });

    it('should not allow students to toggle publish status', async () => {
      // Skip if testId wasn't set (create test failed)
      if (!testId) {
        return;
      }

      await request(app.getHttpServer())
        .patch(`/tests/${testId}/toggle-publish`)
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(403);
    });
  });

  describe('/tests/:id/toggle-active (PATCH)', () => {
    it('should toggle active status', async () => {
      // Skip if testId wasn't set (create test failed)
      if (!testId) {
        return;
      }

      const response = await request(app.getHttpServer())
        .patch(`/tests/${testId}/toggle-active`)
        .set('Authorization', `Bearer ${teacherToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('isActive');
    });
  });

  describe('/tests/:id (DELETE)', () => {
    it('should delete a test', async () => {
      // Skip if testId wasn't set (create test failed)
      if (!testId) {
        return;
      }

      await request(app.getHttpServer())
        .delete(`/tests/${testId}`)
        .set('Authorization', `Bearer ${teacherToken}`)
        .expect(200);

      // Verify test is deleted
      await request(app.getHttpServer())
        .get(`/tests/${testId}`)
        .set('Authorization', `Bearer ${teacherToken}`)
        .expect(404);
    });
  });
}); 