import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { JwtService } from '@nestjs/jwt';
import { Role } from '@prisma/client';
import { PrismaService } from '../src/prisma/prisma.service';

describe('Teacher E2E', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let prisma: PrismaService;
  let teacherToken: string;
  let studentToken: string;
  let teacherId: string;
  let groupId: string;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    jwtService = moduleFixture.get<JwtService>(JwtService);
    prisma = moduleFixture.get<PrismaService>(PrismaService);
    await app.init();

    // Prepare test data - create teacher and student
    const teacherEmail = `teacher-${Date.now()}@example.com`;
    const teacher = await prisma.user.create({
      data: {
        email: teacherEmail,
        password: '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm', // 'secret42'
        role: Role.TEACHER,
        firstName: 'Test',
        lastName: 'Teacher',
      },
    });
    teacherId = teacher.id;

    const studentEmail = `student-${Date.now()}@example.com`;
    await prisma.user.create({
      data: {
        email: studentEmail,
        password: '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm', // 'secret42'
        role: Role.STUDENT,
        firstName: 'Test',
        lastName: 'Student',
      },
    });

    // Create tokens for testing
    teacherToken = jwtService.sign({
      sub: teacherId,
      email: teacherEmail,
      role: Role.TEACHER,
    });

    studentToken = jwtService.sign({
      sub: 'student-id-123456789012',
      email: studentEmail,
      role: Role.STUDENT,
    });

    // Create a test group
    const groupResponse = await request(app.getHttpServer())
      .post('/teacher/groups')
      .set('Authorization', `Bearer ${teacherToken}`)
      .send({
        name: 'Test Group',
        level: 'Intermediate',
        description: 'Group for E2E testing',
      });

    groupId = groupResponse.body.id;
  });

  afterAll(async () => {
    // Clean up test data
    if (groupId) {
      await prisma.group.delete({ where: { id: groupId } }).catch(() => {});
    }
    if (teacherId) {
      await prisma.user.delete({ where: { id: teacherId } }).catch(() => {});
    }
    await prisma.$executeRaw`TRUNCATE "group_invites" CASCADE;`.catch(() => {});
    
    await app.close();
  });

  it('should be defined', () => {
    expect(app).toBeDefined();
  });

  describe('/teacher/groups/:id/invites (POST)', () => {
    it('should create a group invite with default values', async () => {
      const response = await request(app.getHttpServer())
        .post(`/teacher/groups/${groupId}/invites`)
        .set('Authorization', `Bearer ${teacherToken}`)
        .send({});
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('groupId', groupId);
      expect(response.body).toHaveProperty('createdById', teacherId);
      expect(response.body).toHaveProperty('useCount', 0);
      expect(response.body).toHaveProperty('isActive', true);
      expect(response.body).toHaveProperty('inviteUrl');
      
      // Verify expiration is ~7 days from now
      const expiresAt = new Date(response.body.expiresAt);
      const now = new Date();
      const sevenDaysFromNow = new Date(now.setDate(now.getDate() + 7));
      const daysDiff = Math.round((expiresAt.getTime() - sevenDaysFromNow.getTime()) / (1000 * 60 * 60 * 24));
      expect(Math.abs(daysDiff)).toBeLessThanOrEqual(1); // Allow a one-day difference for test timing
    });

    it('should create a group invite with custom expiration and max uses', async () => {
      const response = await request(app.getHttpServer())
        .post(`/teacher/groups/${groupId}/invites`)
        .set('Authorization', `Bearer ${teacherToken}`)
        .send({
          expiresInDays: 14,
          maxUses: 5
        });
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('maxUses', 5);
      
      // Verify expiration is ~14 days from now
      const expiresAt = new Date(response.body.expiresAt);
      const now = new Date();
      const fourteenDaysFromNow = new Date(now.setDate(now.getDate() + 14));
      const daysDiff = Math.round((expiresAt.getTime() - fourteenDaysFromNow.getTime()) / (1000 * 60 * 60 * 24));
      expect(Math.abs(daysDiff)).toBeLessThanOrEqual(1); // Allow a one-day difference for test timing
    });

    it('should reject when a student tries to create an invite', async () => {
      const response = await request(app.getHttpServer())
        .post(`/teacher/groups/${groupId}/invites`)
        .set('Authorization', `Bearer ${studentToken}`)
        .send({});
      
      // Either 403 (forbidden) or 404 (not found) is acceptable depending on the implementation
      // When role-based guards run first, we get 403, when ownership check runs first, we get 404
      expect([403, 404]).toContain(response.status);
    });

    it('should return 404 when group does not exist', async () => {
      const response = await request(app.getHttpServer())
        .post('/teacher/groups/non-existent-id/invites')
        .set('Authorization', `Bearer ${teacherToken}`)
        .send({});
      
      expect(response.status).toBe(404);
    });
  });
});
