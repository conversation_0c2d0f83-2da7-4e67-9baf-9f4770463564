# Railway Setup Guide - Step by Step

## 🚂 Recommended Approach: Use Railway Dashboard

Instead of GitHub Actions, use Railway's web interface for the easiest setup:

### Step 1: Create Railway Account
1. Go to https://railway.app
2. Sign up with your GitHub account

### Step 2: Create New Project
1. Click "New Project"
2. Select "Deploy from GitHub repo"
3. Choose your `ielts-toolkit` repository
4. Railway will scan your repository

### Step 3: Add Services

**Add Backend Service:**
1. Click "Add Service" → "GitHub Repo"
2. Configure:
   - **Name**: `backend`
   - **Root Directory**: `server-nest`
   - **Build Command**: `npm ci && npm run prisma:generate && npm run build`
   - **Start Command**: `npm run start:prod`

**Add Frontend Service:**
1. Click "Add Service" → "GitHub Repo"  
2. Configure:
   - **Name**: `frontend`
   - **Root Directory**: `client`
   - **Build Command**: `npm ci && npm run build`
   - **Start Command**: `npx serve -s dist -l 8080`

**Add Database:**
1. Click "Add Service" → "Database" → "PostgreSQL"
2. Railway will automatically create the database

### Step 4: Configure Environment Variables

**Backend Environment Variables:**
```
NODE_ENV=production
PORT=8080
DATABASE_URL=${{Postgres.DATABASE_URL}}
JWT_SECRET=your-super-secret-jwt-key-change-this
CORS_ORIGIN=https://your-frontend-url.railway.app
```

**Frontend Environment Variables:**
```
VITE_API_URL=https://your-backend-url.railway.app/api
```

### Step 5: Deploy
1. Railway will automatically deploy when you push to main branch
2. No GitHub Actions needed!
3. Check the deployments in Railway dashboard

### Step 6: Run Database Migrations
1. Go to your backend service in Railway
2. Click "Deploy" → "View Logs"
3. Once deployed, go to backend service settings
4. Add a "Deploy Hook" or run manually:
   ```bash
   npm run prisma:deploy
   ```

## 🎯 Benefits of Dashboard Approach

✅ **No tokens needed** - Railway handles authentication  
✅ **No service IDs needed** - Railway manages everything  
✅ **Auto-deployments** - Deploys on every push to main  
✅ **Built-in monitoring** - Logs, metrics, alerts included  
✅ **Easy rollbacks** - One-click rollback to previous versions  
✅ **Environment management** - Easy variable configuration  

## 💰 Cost Estimate

- **PostgreSQL Database**: $5/month
- **Backend Service**: $0-5/month (free tier: 500 hours)
- **Frontend Service**: $0-5/month (free tier: 500 hours)
- **Total**: $5-15/month

## 🔧 Alternative: GitHub Actions (Advanced)

If you still want to use GitHub Actions, you need to:

1. Create services in Railway dashboard first
2. Get service IDs from each service settings
3. Add these GitHub secrets:
   - `RAILWAY_TOKEN`
   - `RAILWAY_PROJECT_ID` 
   - `RAILWAY_BACKEND_SERVICE_ID`
   - `RAILWAY_FRONTEND_SERVICE_ID`
   - `PAT_TOKEN`

But the dashboard approach is much simpler and more reliable!

## 🆘 Need Help?

- Railway Discord: https://discord.gg/railway
- Railway Docs: https://docs.railway.app
- This repository issues: Create an issue for help
