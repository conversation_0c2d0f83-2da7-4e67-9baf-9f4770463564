# Railway Deployment Guide

This document outlines the deployment process for the IELTS Toolkit application to Railway - a cost-effective platform perfect for full-stack applications.

## Why Railway?

Railway offers excellent value for small to medium applications:
- **Cost**: ~$5-8/month total (vs $15-30+ on other platforms)
- **PostgreSQL**: $5/month for hobby plan (1GB storage, 1M rows)
- **Applications**: Generous free tier, then $5/month per service
- **Zero Configuration**: Automatic builds and deployments
- **Built-in Monitoring**: Logs, metrics, and alerts included

## Prerequisites

- Railway account (sign up at https://railway.app)
- Git repository with the application code
- Git submodules properly initialized (the server-nest code is in a submodule)

## Deployment Architecture

The deployment architecture consists of:

1. **Frontend**: React application (Vite build)
2. **Backend**: NestJS application with Prisma ORM
3. **Database**: PostgreSQL database (Railway managed)
4. **CI/CD**: Automated with GitHub Actions

## Deployment Options

### Option 1: Manual Deployment (Recommended for first-time setup)

1. **Create Railway Account and Project**:
   - Sign up at https://railway.app
   - Create a new project
   - Connect your GitHub repository

2. **Set up Database**:
   - In Railway dashboard, click "New Service" → "Database" → "PostgreSQL"
   - Note the connection details (Railway will provide environment variables)

3. **Deploy Backend**:
   - Click "New Service" → "GitHub Repo" → Select your repository
   - Set root directory to `server-nest`
   - Railway will auto-detect it's a Node.js app
   - Add environment variables:
     - `DATABASE_URL`: (Railway will auto-populate from PostgreSQL service)
     - `JWT_SECRET`: Your JWT secret key
     - `NODE_ENV`: production
     - `PORT`: 8080

4. **Deploy Frontend**:
   - Click "New Service" → "GitHub Repo" → Select your repository
   - Set root directory to `client`
   - Add environment variables:
     - `VITE_API_URL`: Your backend service URL + `/api`

5. **Run Database Migrations**:
   - In the backend service, go to "Deploy" tab
   - Run: `npm run prisma:deploy`

### Option 2: Automated Deployment with GitHub Actions

1. **Setup Railway CLI Token**:
   - Install Railway CLI: `npm install -g @railway/cli`
   - Login: `railway login`
   - Get your token from Railway dashboard:
     - Go to https://railway.app/account/tokens
     - Create a new token
     - Copy the token value

2. **Configure GitHub Secrets**:
   - Go to your GitHub repository → Settings → Secrets and variables → Actions
   - Add these secrets:
     - `RAILWAY_TOKEN`: Your Railway CLI token from step 1
     - `RAILWAY_PROJECT_ID`: Your Railway project ID (same for both frontend and backend)
     - `PAT_TOKEN`: GitHub Personal Access Token (for submodules)

   **To get Project ID**:
   - Go to your Railway project dashboard
   - Click on Settings → General
   - Copy the Project ID (it looks like: `********-89ab-cdef-0123-456789abcdef`)
   - Use this same ID for both frontend and backend since they're in the same project

3. **Setup Railway Projects**:
   - Create separate Railway projects for frontend and backend
   - Connect each project to the appropriate directory in your repository
   - Configure environment variables in Railway dashboard

4. **Continuous Deployment**:
   - Push changes to the main branch
   - GitHub Actions will automatically deploy to Railway

## Cost Breakdown

Railway pricing is transparent and affordable:

- **PostgreSQL Database**: $5/month (Hobby plan)
  - 1GB storage
  - 1M rows
  - Automated backups

- **Backend Service**: $0-5/month
  - Free tier: 500 hours/month
  - Paid: $5/month for unlimited usage

- **Frontend Service**: $0-5/month
  - Free tier: 500 hours/month
  - Paid: $5/month for unlimited usage

**Total Monthly Cost: $5-15/month** (much cheaper than GCP/AWS)

## Environment Variables

### Backend (server-nest)
```
DATABASE_URL=postgresql://username:password@host:port/database
JWT_SECRET=your-super-secret-jwt-key
NODE_ENV=production
PORT=8080
CORS_ORIGIN=https://your-frontend-url.railway.app
```

### Frontend (client)
```
VITE_API_URL=https://your-backend-url.railway.app/api
```

## CI/CD Pipeline

The GitHub Actions pipeline:

1. **Checkout**: Get code with submodules
2. **Install Railway CLI**: Set up deployment tools
3. **Deploy Backend**: Deploy NestJS API
4. **Deploy Frontend**: Deploy React app
5. **Run Migrations**: Update database schema

## Customization

You can customize the deployment by:

1. Modifying `railway.json` files in each service directory
2. Updating environment variables in Railway dashboard
3. Modifying GitHub Actions workflow in `.github/workflows/railway-deploy.yml`

## Troubleshooting

If you encounter issues:

1. Check Railway service logs in the dashboard
2. Verify environment variables are set correctly
3. Ensure database connection is working
4. Check GitHub Actions logs for deployment issues

## Monitoring

Railway provides built-in monitoring:
- **Logs**: Real-time application logs
- **Metrics**: CPU, memory, and network usage
- **Alerts**: Email notifications for issues
- **Uptime**: Service availability monitoring
