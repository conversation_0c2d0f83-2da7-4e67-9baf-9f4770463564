# Test System Troubleshooting Guide

This document provides solutions for common issues encountered in the IELTS Toolkit test system.

## Content Display Issues

### Issue: Test content shows "No content available"

**Cause**: The test's `content` field in the database is empty or null, and the system isn't falling back to the `description` field.

**Solution**:
1. Check if the test has content in the database:
   ```sql
   SELECT id, title, content, description FROM Test WHERE id = 'test_id';
   ```
2. If content is empty but description contains the test content, ensure the server code has the fallback logic:
   ```typescript
   // In student.service.ts - getTestWithProgress method
   content: test.content || test.description || '',
   ```
3. Restart the server after making changes:
   ```bash
   docker-compose restart server
   ```

### Issue: Formatting is lost in test content (no line breaks, paragraphs, etc.)

**Cause**: The content display component doesn't preserve whitespace.

**Solution**:
1. Add these CSS properties to the content container:
   ```tsx
   <div 
     dangerouslySetInnerHTML={{ __html: content }} 
     className="whitespace-pre-wrap"
     style={{ lineHeight: '1.6' }}
   />
   ```
2. For plain text content (not HTML), ensure it's wrapped in a `<pre>` or appropriately styled div.

## Answer Evaluation Issues

### Issue: Student answers are marked incorrect despite matching the correct answer

**Cause**: This can happen due to:
- Format differences (e.g., "Option A" vs "A")
- Case sensitivity
- Whitespace differences
- The server and client using different answer checking logic

**Solution**:

1. Normalize answers before comparison:
   ```typescript
   // Server-side in submitTest method
   let normalizedUserAnswer = userAnswer?.toUpperCase()?.trim() || '';
   if (normalizedUserAnswer.startsWith('OPTION ')) {
     normalizedUserAnswer = normalizedUserAnswer.substring(7);
   }
   
   // Compare first character for multiple choice
   const firstChar = normalizedUserAnswer.charAt(0);
   isCorrect = firstChar === mcAnswerDef.correctOption;
   ```

2. For client-side validation, implement similar normalization:
   ```typescript
   // In TestResultView.tsx
   const normalizeAnswer = (answer: string): string => {
     const normalized = answer.toUpperCase().trim();
     return normalized.startsWith('OPTION ') 
       ? normalized.charAt(7) 
       : normalized.charAt(0);
   };
   
   // Then compare normalized answers
   isCorrect = normalizeAnswer(userAnswerText) === normalizeAnswer(correctAnswerText);
   ```

### Issue: The "Correct Answer" section always shows "Option B" regardless of the actual answer

**Cause**: The client code has a hardcoded default value instead of using the teacher-defined answers.

**Solution**:
1. Ensure the `answerSheet` property is correctly parsed from the API response:
   ```typescript
   // In TestResultView.tsx
   // Parse the answer sheet data
   let parsedMultipleChoiceAnswers = [];
   try {
     if (typeof test.answerSheet.multipleChoiceAnswers === 'string') {
       parsedMultipleChoiceAnswers = JSON.parse(test.answerSheet.multipleChoiceAnswers);
     } else {
       parsedMultipleChoiceAnswers = test.answerSheet.multipleChoiceAnswers || [];
     }
   } catch (err) {
     console.error('Error parsing answer sheet data:', err);
   }
   
   // Find the answer definition for this question
   const mcAnswerDef = parsedMultipleChoiceAnswers.find(
     a => parseInt(a.questionNumber.toString()) === questionNumber
   );
   
   // Use the correct option from the answer definition
   if (mcAnswerDef) {
     const correctOption = mcAnswerDef.correctOption.toUpperCase();
     // Map to full option text
     correctAnswerText = `Option ${correctOption}`;
   }
   ```

2. Ensure the `Test` interface includes the `answerSheet` property:
   ```typescript
   // In types/test.ts
   export interface Test {
     // ... other properties
     answerSheet?: {
       multipleChoiceAnswers: MultipleChoiceAnswer[] | string;
       textAnswers: TextAnswer[] | string;
     };
   }
   ```

## Progress Tracking Issues

### Issue: Test progress is lost when reloading the page

**Cause**: Progress is only stored in memory on the server and not persisted to the database.

**Solution**:
1. Implement localStorage backup for progress:
   ```typescript
   // In TestSessionContext.tsx - add localStorage backup
   const saveProgress = async (answers: Answer[]) => {
     try {
       // Save to server
       const response = await axios.post(`/api/student/tests/${testId}/progress`, { answers });
       
       // Backup to localStorage
       localStorage.setItem(`test_progress_${testId}`, JSON.stringify({
         answers,
         lastUpdated: new Date().toISOString()
       }));
       
       return response.data;
     } catch (error) {
       console.error('Error saving progress:', error);
     }
   };
   
   // On page load, check for local backup if server progress is empty
   useEffect(() => {
     if (progress && progress.answers.length === 0) {
       const savedProgress = localStorage.getItem(`test_progress_${testId}`);
       if (savedProgress) {
         try {
           const parsed = JSON.parse(savedProgress);
           setAnswers(parsed.answers);
         } catch (e) {
           console.error('Error parsing saved progress:', e);
         }
       }
     }
   }, [progress]);
   ```

2. For a more permanent solution, modify the server to store progress in the database:
   ```typescript
   // In student.service.ts - saveProgress method
   async saveProgress(testId: string, userId: string, answers: Answer[]): Promise<TestProgress> {
     // Store in memory for immediate access
     this.progressStore[`${userId}_${testId}`] = {
       ...this.progressStore[`${userId}_${testId}`],
       answers,
       lastActivity: new Date()
     };
     
     // Also persist to database
     await this.prisma.testProgress.upsert({
       where: {
         userId_testId: {
           userId,
           testId
         }
       },
       create: {
         userId,
         testId,
         answers: JSON.stringify(answers),
         startTime: new Date(),
         lastActivity: new Date(),
         status: 'IN_PROGRESS'
       },
       update: {
         answers: JSON.stringify(answers),
         lastActivity: new Date()
       }
     });
     
     return this.progressStore[`${userId}_${testId}`];
   }
   ```

### Issue: Test timer resets on page reload

**Cause**: The start time is only stored in memory and not persisted.

**Solution**:
1. Store the start time in localStorage:
   ```typescript
   // In TestSessionContext.tsx
   useEffect(() => {
     if (progress?.startTime) {
       // Backup start time to localStorage
       localStorage.setItem(`test_start_${testId}`, progress.startTime.toISOString());
     }
   }, [progress?.startTime, testId]);
   
   // On initial load, if no progress.startTime, check localStorage
   useEffect(() => {
     if (!progress?.startTime) {
       const savedStartTime = localStorage.getItem(`test_start_${testId}`);
       if (savedStartTime) {
         setStartTime(new Date(savedStartTime));
       }
     }
   }, []);
   ```

2. For a server-side solution, ensure the start time is persisted to the database as described in the previous solution.

## Question and Answer Display Issues

### Issue: Question numbers don't match between test taking and results

**Cause**: Inconsistent ID generation for questions between different parts of the application.

**Solution**:
1. Use consistent question IDs based on the question number from the answer sheet:
   ```typescript
   // In student.service.ts - getTestWithProgress method
   if (test.multipleChoiceAnswers) {
     const mcAnswers = JSON.parse(test.multipleChoiceAnswers.toString());
     mcAnswers.forEach((answer: any) => {
       formattedTest.questions.push({
         id: `${answer.questionNumber}`, // Use actual question number
         prompt: `Question ${answer.questionNumber}`,
         type: 'multipleChoice' as ClientQuestionType,
         options: ['Option A', 'Option B', 'Option C', 'Option D'],
       });
     });
   }
   ```

2. In results display, use the same question numbering scheme:
   ```typescript
   // In TestResultView.tsx
   questions.map((question, index) => {
     // Use the question.id rather than index+1 for question number
     const questionNumber = parseInt(question.id);
     
     // Rest of the rendering logic...
   })
   ```

## Performance Issues

### Issue: Test loads slowly, especially for tests with many questions

**Cause**: Inefficient data loading or processing.

**Solution**:
1. Implement pagination for questions:
   ```typescript
   // In TestTaking.tsx
   const [currentPage, setCurrentPage] = useState(1);
   const questionsPerPage = 5;
   
   // Get current questions
   const indexOfLastQuestion = currentPage * questionsPerPage;
   const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;
   const currentQuestions = questions.slice(indexOfFirstQuestion, indexOfLastQuestion);
   
   // Render only current questions
   {currentQuestions.map((question) => (
     <QuestionItem 
       key={question.id} 
       question={question} 
       // other props 
     />
   ))}
   
   // Add pagination controls
   <div className="flex justify-between mt-4">
     <button 
       onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
       disabled={currentPage === 1}
       className="px-4 py-2 bg-gray-200 rounded"
     >
       Previous
     </button>
     <span>Page {currentPage} of {Math.ceil(questions.length / questionsPerPage)}</span>
     <button 
       onClick={() => setCurrentPage(prev => Math.min(prev + 1, Math.ceil(questions.length / questionsPerPage)))}
       disabled={currentPage === Math.ceil(questions.length / questionsPerPage)}
       className="px-4 py-2 bg-gray-200 rounded"
     >
       Next
     </button>
   </div>
   ```

2. Optimize the server response:
   - Add caching for published tests
   - Implement more efficient database queries

### Issue: Test submission takes a long time

**Cause**: The scoring algorithm may be inefficient, especially for tests with many questions.

**Solution**:
1. Optimize the scoring algorithm:
   ```typescript
   // In student.service.ts - submitTest method
   
   // Create lookup maps for faster access
   const mcAnswersMap = new Map();
   const textAnswersMap = new Map();
   
   mcAnswers.forEach((answer: any) => {
     mcAnswersMap.set(answer.questionNumber.toString(), answer);
   });
   
   textAnswers.forEach((answer: any) => {
     textAnswersMap.set(answer.questionNumber.toString(), answer);
   });
   
   // Now use the maps for faster lookups
   answers.forEach((answer) => {
     const mcAnswerDef = mcAnswersMap.get(answer.questionNumber);
     const textAnswerDef = textAnswersMap.get(answer.questionNumber);
     
     // Rest of answer checking logic
   });
   ```

2. Consider processing answers in batches if there are many:
   ```typescript
   // Process in batches of 10
   const batchSize = 10;
   for (let i = 0; i < answers.length; i += batchSize) {
     const batch = answers.slice(i, i + batchSize);
     await Promise.all(batch.map(answer => processAnswer(answer)));
   }
   ```

## API Error Handling

### Issue: Test API requests fail with 500 Internal Server Error

**Cause**: Unhandled exceptions in the server code.

**Solution**:
1. Implement proper try/catch blocks:
   ```typescript
   // In student.controller.ts - getTestWithProgress method
   @Get('tests/:id')
   async getTestWithProgress(@Param('id') id: string, @Request() req) {
     try {
       return await this.studentService.getTestWithProgress(id, req.user.id);
     } catch (error) {
       this.logger.error(`Error getting test: ${error.message}`, error.stack);
       if (error.status === 404 || error.message.includes('not found')) {
         throw new NotFoundException('Test not found');
       }
       throw new InternalServerErrorException('Error retrieving test data');
     }
   }
   ```

2. Add global exception filters in NestJS:
   ```typescript
   // In app.module.ts
   @Module({
     providers: [
       {
         provide: APP_FILTER,
         useClass: HttpExceptionFilter,
       },
     ],
   })
   ```

3. Create a custom exception filter:
   ```typescript
   // In core/filters/http-exception.filter.ts
   @Catch(HttpException)
   export class HttpExceptionFilter implements ExceptionFilter {
     catch(exception: HttpException, host: ArgumentsHost) {
       const ctx = host.switchToHttp();
       const response = ctx.getResponse<Response>();
       const request = ctx.getRequest<Request>();
       const status = exception.getStatus();
       const error = exception.getResponse();
       
       // Log the error
       console.error(`Error in ${request.method} ${request.url}:`, error);
       
       response
         .status(status)
         .json({
           statusCode: status,
           timestamp: new Date().toISOString(),
           path: request.url,
           error: typeof error === 'string' ? error : (error as any).message || 'Internal server error',
         });
     }
   }
   ```

## Browser Compatibility Issues

### Issue: Test doesn't work correctly in older browsers

**Cause**: Using modern JavaScript features without proper polyfills.

**Solution**:
1. Add the necessary polyfills:
   ```typescript
   // In client/src/polyfills.ts (create if it doesn't exist)
   import 'core-js/stable';
   import 'regenerator-runtime/runtime';
   ```

2. Update the client build process to include babel transformations:
   ```json
   // In client/.babelrc
   {
     "presets": [
       ["@babel/preset-env", {
         "useBuiltIns": "usage",
         "corejs": 3
       }],
       "@babel/preset-react",
       "@babel/preset-typescript"
     ]
   }
   ```

3. Add fallbacks for modern CSS features:
   ```css
   /* In global styles */
   .modern-feature {
     display: flex; /* Modern */
     display: -webkit-box; /* Older WebKit */
     display: -ms-flexbox; /* IE10 */
   }
   ```

## Security Considerations

### Issue: Students can potentially access other students' answers or results

**Cause**: Insufficient access control in API endpoints.

**Solution**:
1. Implement proper authorization checks:
   ```typescript
   // In student.service.ts - getResult method
   async getResult(resultId: string, userId: string): Promise<TestResult> {
     const result = await this.prisma.testResult.findUnique({
       where: { id: resultId }
     });
     
     // Check if the result belongs to the requesting user
     if (!result || result.userId !== userId) {
       throw new ForbiddenException('You do not have access to this result');
     }
     
     return result;
   }
   ```

2. Add a global guard:
   ```typescript
   // In app.module.ts
   @Module({
     providers: [
       {
         provide: APP_GUARD,
         useClass: JwtAuthGuard,
       },
     ],
   })
   ```

3. Implement role-based access control:
   ```typescript
   // In auth/roles.guard.ts
   @Injectable()
   export class RolesGuard implements CanActivate {
     constructor(private reflector: Reflector) {}
     
     canActivate(context: ExecutionContext): boolean {
       const requiredRoles = this.reflector.getAllAndOverride<Role[]>('roles', [
         context.getHandler(),
         context.getClass(),
       ]);
       
       if (!requiredRoles) {
         return true;
       }
       
       const { user } = context.switchToHttp().getRequest();
       return requiredRoles.some((role) => user.roles?.includes(role));
     }
   }
   ```

## Diagnosing Unknown Issues

If you encounter issues not covered in this guide, follow these diagnostic steps:

1. **Check browser console for errors**:
   - Press F12 to open developer tools
   - Look for red error messages in the Console tab

2. **Check server logs**:
   ```bash
   docker-compose logs -f server
   ```

3. **Add temporary debug logging**:
   ```typescript
   // Client-side
   console.log('TestData:', test);
   
   // Server-side
   this.logger.debug('Processing test submission:', JSON.stringify(test));
   ```

4. **Verify API responses with network tab**:
   - Open developer tools (F12)
   - Go to Network tab
   - Perform the action that's failing
   - Check the response of the relevant API call

5. **Compare with a working example**:
   - Create a simple test with minimal content and questions
   - If it works, gradually add complexity to identify what causes the issue

6. **Check database state directly**:
   ```sql
   SELECT * FROM Test WHERE id = 'test_id';
   SELECT * FROM TestProgress WHERE testId = 'test_id';
   ```

7. **Review recent code changes**:
   ```bash
   git diff HEAD~10..HEAD -- path/to/relevant/files
   ```

If all else fails, consider temporarily reverting to a known working version while investigating further. 