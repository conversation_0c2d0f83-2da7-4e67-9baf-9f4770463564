## As student I want to have my own answer sheet to answer the test

The current UI we have to switch between the questions, it's different from the first intent.

The idea is the student answer sheet only have the answer sheet and the content of the test is in scrollable so that student can scroll betweens the question. The matching between the test has been created by teacher and student so that we can check the number of correct answer.

The countdown timing is good please keep it.

Also the ui showing the list of test for student should not showing the content. if you check the page now in: http://localhost:3000/student/tests if the content is too long then it should show an ugly UI

## Implementation Requirements

### Layout Changes
1. **Two-Panel Layout**:
   - Left panel (2/3 width): Scrollable test content containing all questions and reading passages
   - Right panel (1/3 width): Fixed answer sheet with input fields for all questions
   - Both panels should be visible simultaneously on desktop screens
   - On mobile, panels should stack with content first, then answer sheet

2. **Content Display**:
   - The content panel should:
     - Display the full HTML content including all questions and passages
     - Render rich text formatting correctly, preserving all styling, tables, images and special formatting
     - Support and display all HTML elements used in IELTS exams (e.g., tables, lists, images, special characters)
     - Have its own scrollbar independent of the answer sheet
     - Maintain proper formatting and readability (font size, spacing, etc.)
     - Show question numbers clearly to correspond with the answer sheet
     - Use proper typography for readability (appropriate line height, letter spacing, etc.)

3. **Answer Sheet Interface**:
   - The answer sheet should:
     - Display all answer inputs at once (no need to navigate between questions)
     - Clearly distinguish between multiple choice and text input questions
     - Use radio buttons for multiple choice questions
     - Use appropriate text inputs for written answers
     - Show question numbers matching the content
     - Indicate answered/unanswered status
     - Support flagging questions for review

### Test List Display Improvements
1. **Card Display**:
   - Truncate long descriptions (max 2-3 lines)
   - Show only essential information:
     - Test title
     - Test type and category
     - Difficulty level
     - Time limit
   - Use consistent card heights regardless of content length

### Preserved Features
1. **Timer**:
   - Keep the countdown timer with warning colors
   - Maintain auto-submission when time expires

2. **Test Navigation**:
   - Maintain question flagging functionality
   - Keep save & exit functionality
   - Preserve test submission workflow including confirmation
   - Keep track of answered/unanswered status

## Technical Implementation Notes

1. **Component Changes**:
   - Update `TestTaking.tsx` with new grid layout
   - Create a scrollable content container
   - Implement a static answer sheet component
   - Ensure responsive design for all screen sizes
   - Use appropriate rich text rendering techniques (dangerouslySetInnerHTML with proper sanitization)

2. **State Management**:
   - Ensure `TestSessionContext` handles all questions simultaneously
   - Maintain progress tracking and flagging functionality
   - Keep auto-save functionality working with the new layout

3. **Performance Considerations**:
   - Optimize rendering for potentially large HTML content
   - Ensure smooth scrolling in content area
   - Handle answer state updates efficiently

## Example Layout Structure

```tsx
<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
  {/* Test Content - Scrollable */}
  <div className="lg:col-span-2 bg-white p-4 rounded-lg shadow">
    <div className="prose prose-sm md:prose-base lg:prose-lg overflow-y-auto max-h-[70vh]">
      <div dangerouslySetInnerHTML={{ __html: session.test.content }} />
    </div>
  </div>
  
  {/* Answer Sheet - Fixed */}
  <div className="lg:col-span-1 bg-white p-4 rounded-lg shadow">
    <h3>Answer Sheet</h3>
    <div className="space-y-4">
      {/* All question inputs displayed at once */}
    </div>
  </div>
</div>
```
