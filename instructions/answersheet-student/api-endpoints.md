# Test System API Endpoints

This document provides an overview of the API endpoints related to the testing system in the IELTS Toolkit application.

## Student Endpoints

### Test Retrieval

#### `GET /api/student/tests`
- **Description**: Retrieves a list of all published tests available to the student
- **Query Parameters**: None
- **Response**: Array of test objects with basic information
- **Implementation**: `getPublishedTests` method in `StudentController`

#### `GET /api/student/tests/:id`
- **Description**: Retrieves a specific test by ID with the student's progress
- **Path Parameters**: 
  - `id`: Test ID
- **Response**: Test object with questions, content, and the student's progress
- **Implementation**: `getTestWithProgress` method in `StudentController`

### Test Progress Management

#### `POST /api/student/tests/:id/start`
- **Description**: Starts a new test session for the student
- **Path Parameters**: 
  - `id`: Test ID
- **Response**: Progress object with initial state
- **Implementation**: `startTest` method in `StudentController`

#### `POST /api/student/tests/:id/progress`
- **Description**: Saves the student's progress on a test (answers and time)
- **Path Parameters**: 
  - `id`: Test ID
- **Request Body**:
  ```typescript
  {
    answers: {
      questionNumber: string;
      answer: string | string[];
    }[];
  }
  ```
- **Response**: Updated progress object
- **Implementation**: `saveProgress` method in `StudentController`

#### `POST /api/student/tests/:id/submit`
- **Description**: Submits a completed test for grading
- **Path Parameters**: 
  - `id`: Test ID
- **Response**: Test result object with score and correct/incorrect answers
- **Implementation**: `submitTest` method in `StudentController`

### Test Results

#### `GET /api/student/results`
- **Description**: Retrieves all test results for the current student
- **Query Parameters**: None
- **Response**: Array of test result objects
- **Implementation**: `getResults` method in `StudentController`

#### `GET /api/student/results/:id`
- **Description**: Retrieves a specific test result by ID
- **Path Parameters**: 
  - `id`: Result ID
- **Response**: Test result object with detailed information
- **Implementation**: `getResult` method in `StudentController`

## Teacher Endpoints

### Test Management

#### `GET /api/teacher/tests`
- **Description**: Retrieves all tests created by the teacher
- **Query Parameters**: None
- **Response**: Array of test objects
- **Implementation**: `getTests` method in `TeacherController`

#### `GET /api/teacher/tests/:id`
- **Description**: Retrieves a specific test by ID
- **Path Parameters**: 
  - `id`: Test ID
- **Response**: Test object with all details
- **Implementation**: `getTest` method in `TeacherController`

#### `POST /api/teacher/tests`
- **Description**: Creates a new test
- **Request Body**:
  ```typescript
  {
    title: string;
    description: string;
    content: string;
    type: TestType;
    difficulty: Difficulty;
    timeLimit: number;
    categoryId: string;
    maxScore: number;
    answerSheet: {
      multipleChoiceAnswers: {
        questionNumber: string;
        correctOption: string;
      }[];
      textAnswers: {
        questionNumber: string;
        correctAnswers: string[];
      }[];
    };
  }
  ```
- **Response**: Created test object
- **Implementation**: `createTest` method in `TeacherController`

#### `PUT /api/teacher/tests/:id`
- **Description**: Updates an existing test
- **Path Parameters**: 
  - `id`: Test ID
- **Request Body**: Same as POST endpoint
- **Response**: Updated test object
- **Implementation**: `updateTest` method in `TeacherController`

#### `DELETE /api/teacher/tests/:id`
- **Description**: Deletes a test
- **Path Parameters**: 
  - `id`: Test ID
- **Response**: Success message
- **Implementation**: `deleteTest` method in `TeacherController`

#### `POST /api/teacher/tests/:id/publish`
- **Description**: Publishes a test, making it available to students
- **Path Parameters**: 
  - `id`: Test ID
- **Response**: Updated test object with `isPublished: true`
- **Implementation**: `publishTest` method in `TeacherController`

### Student Result Viewing

#### `GET /api/teacher/results`
- **Description**: Retrieves all student test results
- **Query Parameters**: 
  - `testId` (optional): Filter by test ID
  - `studentId` (optional): Filter by student ID
- **Response**: Array of test result objects
- **Implementation**: `getResults` method in `TeacherController`

#### `GET /api/teacher/results/:id`
- **Description**: Retrieves a specific test result by ID
- **Path Parameters**: 
  - `id`: Result ID
- **Response**: Test result object with detailed information
- **Implementation**: `getResult` method in `TeacherController`

## Data Structures

### Test Object

```typescript
interface Test {
  id: string;
  title: string;
  description: string;
  content: string;
  type: TestType; // 'LISTENING' | 'READING' | 'WRITING' | 'SPEAKING'
  difficulty: Difficulty; // 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED'
  timeLimit: number; // in minutes
  maxScore: number;
  isPublished: boolean;
  categoryId: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Only present when retrieving a specific test
  questions?: Question[];
  
  // Answer definitions stored as JSON strings or parsed objects
  answerSheet?: {
    multipleChoiceAnswers: MultipleChoiceAnswer[] | string;
    textAnswers: TextAnswer[] | string;
  };
  
  // Only present when retrieving test with progress
  progress?: TestProgress;
}
```

### Question Object

```typescript
interface Question {
  id: string; // Usually matches questionNumber from the answerSheet
  prompt: string;
  type: 'multipleChoice' | 'fillInTheBlank';
  options?: string[]; // For multiple choice questions
}
```

### Test Progress Object

```typescript
interface TestProgress {
  id: string;
  testId: string;
  userId: string;
  startTime: Date;
  lastActivity: Date;
  status: 'STARTED' | 'IN_PROGRESS' | 'SUBMITTED';
  answers: Answer[];
}
```

### Answer Object

```typescript
interface Answer {
  questionNumber: string;
  answer: string | string[];
  isCorrect?: boolean; // Present in test results
}
```

### Test Result Object

```typescript
interface TestResult {
  id: string;
  testId: string;
  userId: string;
  score: number;
  maxScore: number;
  timeSpent: number; // in seconds
  submittedAt: Date;
  
  // Detailed information on correct/incorrect answers
  answers: Answer[];
  
  // The associated test data
  test?: Test;
}
```

### Answer Sheet Objects

```typescript
interface MultipleChoiceAnswer {
  questionNumber: string;
  correctOption: string; // 'A', 'B', 'C', or 'D'
}

interface TextAnswer {
  questionNumber: string;
  correctAnswers: string[]; // Array of acceptable answers
}
``` 