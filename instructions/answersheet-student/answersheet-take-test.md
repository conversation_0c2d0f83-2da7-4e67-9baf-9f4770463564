# As a student I want to have a proper UI to take the test

## Description

When accessing the test-taking page (e.g., http://localhost:3000/student/tests/[test-id]), I want to have a comprehensive UI that provides:

- A well-organized display of the test content in a scrollable component, allowing me to read through rich text passages or instructions
- An intuitive answer sheet interface where I can:
  - Toggle between different questions
  - Save answers as I go
  - Flag questions for later review
  - Track my progress (answered vs. unanswered questions)
- Clear navigation controls to move between questions
- A visible timer showing remaining time
- The ability to submit my test when complete
- After completing the test, access to a detailed results view showing my performance

## Acceptance Criteria

### Test Content Display
- Test content must be stored and displayed as rich text (HTML)
- Content should appear in a dedicated scrollable area with proper formatting
- The content section should have a reasonable maximum height to avoid overwhelming the UI

### Question Navigation
- Provide a sidebar or grid showing all question numbers
- Visual indicators should show which questions are:
  - Currently being viewed
  - Already answered
  - Flagged for review
- Allow direct navigation to any question by clicking its number

### Answer Input
- Support different question types:
  - Multiple choice with radio button options
  - Text/essay with appropriate input fields
- Answers should be automatically saved when navigating between questions
- Allow flagging questions for later review

### Test Progress Tracking
- Display a summary showing:
  - Total number of questions
  - Number of questions answered
  - Number of questions flagged
- Provide clear confirmation before submitting the test

### Timer and Test Control
- Display a countdown timer showing remaining time
- Timer should visually alert when time is running low
- Provide options to:
  - Save and exit (pause the test)
  - Submit the test (with confirmation)
- Auto-submit when time expires

### Results View
- After submission, redirect to a results page showing:
  - Overall score
  - Number and percentage of correct/incorrect answers
  - Time spent on the test
  - Detailed breakdown of each question with:
    - The student's answer
    - The correct answer
    - Visual indicators for correct/incorrect responses

### Technical Requirements
- Test sessions must persist properly to avoid data loss
- API integration should provide real-time feedback
- The UI must be responsive and work on different devices
- All interactions should be accessible and keyboard-navigable
