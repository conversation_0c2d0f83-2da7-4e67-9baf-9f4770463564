# Category Teacher Feature - Filter Categories by Teacher ID

## Current Issue

As a teacher when accessing the categories page, I want to see only categories that I have created, not all categories in the system. Currently, the `/categories` endpoint returns all categories regardless of which teacher is logged in.

## Analysis

1. **Problem Description**:
   - Teachers need a personalized view of categories they've created
   - The current `/categories` API endpoint returns all categories 
   - The frontend displays all categories returned from the API
   - The user authentication and role checks are already in place

2. **Current Implementation**:
   - Backend query does not filter by the authenticated user's ID
   - The `Category` entity does not have a `createdById` field in the database schema
   - Frontend displays all categories returned from the API

## Implementation Plan

### 1. Database Schema Update

1. **Update the Prisma Schema**:
   - Add a `createdById` field to the Category model in the Prisma schema

   ```prisma
   // In schema.prisma
   model Category {
     id          String   @id @default(uuid())
     name        String
     description String?
     isActive    Boolean  @default(true)
     createdById String?  // Add this field
     createdBy   User?    @relation(fields: [createdById], references: [id]) // Add relation
     createdAt   DateTime @default(now())
     updatedAt   DateTime @updatedAt
     tests       Test[]   // Assuming there's a relation to tests
   }
   ```

2. **Apply Database Migration**:
   - Run prisma migration to update the database schema

   ```bash
   npx prisma migrate dev --name add-created-by-to-category
   ```

### 2. Backend Updates

1. **Update Category Entity**:
   - Update the `Category` entity class to include the `createdBy` relation

   ```typescript
   // In category.entity.ts
   export class Category {
     id?: string;
     name: string;
     description?: string | null;
     isActive?: boolean;
     createdById?: string;
     createdBy?: {
       _id: string;
       firstName: string;
       lastName: string;
       email: string;
     };
     createdAt?: Date | string;
     updatedAt?: Date | string;
   }
   ```

2. **Update Controller**:
   - Modify the `findAll` method in `CategoriesController` to pass the authenticated user to the service

   ```typescript
   // In categories.controller.ts
   @Get()
   findAll(@GetUser() user: User) {
     return this.categoriesService.findAll(user);
   }
   ```

3. **Update Service**:
   - Modify the `findAll` method in `CategoriesService` to filter categories based on user role
   - Admins should see all categories, teachers only see their own

   ```typescript
   // In categories.service.ts
   async findAll(currentUser?: User): Promise<Category[]> {
     try {
       // Build query based on user role
       const whereCondition: any = {};
       
       // If user is not admin, filter categories by creator
       if (currentUser && currentUser.role !== Role.ADMIN) {
         whereCondition.createdById = currentUser.id;
       }
       
       return this.prisma.category.findMany({
         where: whereCondition,
         include: {
           createdBy: true
         },
         orderBy: {
           createdAt: 'desc'
         }
       });
     } catch (error) {
       console.error(`Failed to find categories: ${error.message}`);
       throw new InternalServerErrorException('Failed to retrieve categories');
     }
   }
   ```

4. **Update Create Method**:
   - Ensure the `create` method properly saves the creator ID

   ```typescript
   // In categories.service.ts
   async create(createCategoryDto: CreateCategoryDto, user: User): Promise<Category> {
     const newCategory = await this.prisma.category.create({
       data: {
         ...createCategoryDto,
         createdById: user?.id, // Set the creator ID
       },
       include: {
         createdBy: true, // Include creator in response
       },
     });
     return newCategory;
   }
   ```

### 3. Frontend Adjustments

1. **No API Client Changes Needed**:
   - The existing `categoriesApi.getAll()` method doesn't need to change
   - The backend will handle the filtering based on the authenticated user

2. **Update UI to Indicate Filtering (Optional Enhancement)**:
   - Add an indicator to show teachers they're viewing only their categories

   ```tsx
   // In CategoriesList.tsx (optional enhancement)
   <div className="flex justify-between items-center mb-6">
     <div>
       <h1 className="text-2xl font-bold dark:text-white">Danh sách danh mục</h1>
       {user?.role === UserRole.TEACHER && (
         <span className="text-sm text-gray-500 dark:text-gray-400">
           Chỉ hiển thị danh mục của bạn
         </span>
       )}
     </div>
     {/* Rest of the existing code */}
   </div>
   ```

## Testing Plan

1. **Backend Testing**:
   - Test the `findAll` method in `CategoriesService` with different user roles
   - Verify the correct filtering is applied based on user role
   - Test with both admin and teacher roles

   ```typescript
   // Example test for the findAll method
   describe('findAll', () => {
     it('should return all categories for admin users', async () => {
       // Test implementation
     });

     it('should return only categories created by the teacher for teacher users', async () => {
       // Test implementation
     });
   });
   ```

2. **API Testing**:
   - Test the `/categories` endpoint when authenticated as a teacher
   - Verify only categories created by that teacher are returned
   - Test the endpoint as an admin and verify all categories are returned

3. **Frontend Testing**:
   - Log in as a teacher and verify only their categories appear
   - Log in as an admin and verify all categories appear
   - Check handling of edge cases (no categories available, server errors)

## Completion Criteria

The feature will be considered complete when:
1. Teachers can only see categories they have created when accessing the categories page
2. Admins can see all categories in the system
3. The UI clearly indicates that teachers are seeing only their categories (if enhancement implemented)
4. All error cases are properly handled
5. Tests for both frontend and backend are passing

## Migration Strategy

Since we're adding a required field to existing data, we need a migration strategy:

1. **For Existing Categories**:
   - During migration, set `createdById` to NULL for existing categories
   - Alternatively, assign existing categories to an admin user

2. **Database Migration Script**:
   ```typescript
   // Example migration for existing data
   async function migrateCategoryData() {
     // Get an admin user to assign as creator for existing categories
     const adminUser = await prisma.user.findFirst({
       where: { role: 'ADMIN' }
     });
     
     // Update all categories without a createdById
     await prisma.category.updateMany({
       where: { createdById: null },
       data: { createdById: adminUser?.id }
     });
   }
   ```

## Additional Considerations

1. **Performance**: The filter is applied at the database query level which is efficient
2. **Compatibility**: This change follows the same pattern as the tests filter implementation
3. **Security**: The endpoint remains protected by existing guards (JwtAuthGuard, RolesGuard)
4. **User Experience**: Consider adding a filter toggle for admins to switch between "all categories" and "my categories" in a future enhancement