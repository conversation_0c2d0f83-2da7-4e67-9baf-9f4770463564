# Internationalization Implementation Plan for IELTS Toolkit

## Overview
As an IELTS Toolkit user, I want the frontend to support multiple languages, specifically Vietnamese and English, with the ability to switch between them seamlessly.

## Current State
- The application currently has hardcoded text in Vietnamese
- No internationalization framework is implemented
- Users cannot switch between languages

## Implementation Requirements

### 1. Setup Internationalization Framework
- Install and configure `react-i18next` and `i18next` libraries
- Set up language detection and switching functionality
- Configure Vietnamese as the default language

### 2. Create Translation Files
- Create translation files for Vietnamese (vi.json) and English (en.json)
- Organize translations by feature/page for better maintainability
- Include all text currently displayed in the application

### 3. Refactor Components
- Replace all hardcoded text with translation keys
- Update components to use the translation hooks/HOCs
- Ensure dynamic content (like dates, numbers) is properly formatted according to locale

### 4. Language Switching UI
- Add a language selector component in the navigation bar
- Implement language switching functionality
- Persist language preference in local storage

### 5. Testing
- Test all pages in both languages
- Verify that dynamic content is properly formatted
- Ensure language switching works correctly

## Technical Approach
1. Use `react-i18next` as the internationalization framework
2. Implement the `useTranslation` hook for functional components
3. Create a language context to manage language state across the application
4. Use namespaces to organize translations by feature

## Translation File Structure Example
```json
// vi.json
{
  "common": {
    "submit": "Gửi",
    "cancel": "Hủy",
    "save": "Lưu",
    "delete": "Xóa"
  },
  "auth": {
    "login": "Đăng nhập",
    "register": "Đăng ký",
    "email": "Email",
    "password": "Mật khẩu"
  },
  "tests": {
    "create": "Tạo bài kiểm tra",
    "edit": "Chỉnh sửa bài kiểm tra",
    "title": "Tiêu đề",
    "description": "Mô tả"
  }
}
```

## Acceptance Criteria
- The site supports both Vietnamese and English languages
- Vietnamese is set as the default language
- Users can switch between languages via a UI element
- All hardcoded text is moved to translation configuration files
- All components use the translation framework instead of hardcoded text
- Language preference persists between sessions
- Date and number formats respect the selected locale

## Implementation Steps
1. Install required packages
2. Set up i18next configuration
3. Create initial translation files
4. Implement language switching functionality
5. Refactor components to use translations
6. Add language selector to the UI
7. Test and verify functionality

## Estimated Effort
- Setup and configuration: 1 day
- Creating translation files: 2-3 days
- Refactoring components: 3-4 days
- Testing and bug fixing: 1-2 days

## Code Examples

### 1. Installation
```bash
npm install i18next react-i18next i18next-browser-languagedetector i18next-http-backend
```

### 2. i18next Configuration (src/i18n.ts)
```typescript
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

i18n
  // Load translations from /public/locales
  .use(Backend)
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    fallbackLng: 'vi',
    debug: process.env.NODE_ENV === 'development',

    interpolation: {
      escapeValue: false, // React already escapes values
    },

    // Language detection options
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },

    // Backend configuration
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },

    // Default namespace
    defaultNS: 'common',
  });

export default i18n;
```

### 3. Language Switcher Component
```tsx
import React from 'react';
import { useTranslation } from 'react-i18next';

const LanguageSwitcher: React.FC = () => {
  const { i18n } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <div className="flex items-center space-x-2">
      <button
        className={`px-2 py-1 rounded ${i18n.language === 'vi' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
        onClick={() => changeLanguage('vi')}
      >
        VI
      </button>
      <button
        className={`px-2 py-1 rounded ${i18n.language === 'en' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
        onClick={() => changeLanguage('en')}
      >
        EN
      </button>
    </div>
  );
};

export default LanguageSwitcher;
```

### 4. Using Translations in Components
```tsx
import React from 'react';
import { useTranslation } from 'react-i18next';

const LoginForm: React.FC = () => {
  const { t } = useTranslation(['auth', 'common']);

  return (
    <div className="p-4 bg-white rounded shadow">
      <h2 className="text-xl font-bold mb-4">{t('auth:login')}</h2>
      <form>
        <div className="mb-4">
          <label className="block mb-1">{t('auth:email')}</label>
          <input type="email" className="form-input w-full" />
        </div>
        <div className="mb-4">
          <label className="block mb-1">{t('auth:password')}</label>
          <input type="password" className="form-input w-full" />
        </div>
        <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded">
          {t('common:submit')}
        </button>
      </form>
    </div>
  );
};

export default LoginForm;
```

### 5. Formatting Dates and Numbers
```tsx
import React from 'react';
import { useTranslation } from 'react-i18next';

const TestCard: React.FC<{ test: any }> = ({ test }) => {
  const { t, i18n } = useTranslation('tests');

  // Format date according to locale
  const formattedDate = new Date(test.createdAt).toLocaleDateString(i18n.language, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="p-4 border rounded">
      <h3 className="font-bold">{test.title}</h3>
      <p>{test.description}</p>
      <div className="mt-2 text-sm text-gray-600">
        {t('tests:createdOn', { date: formattedDate })}
      </div>
    </div>
  );
};

export default TestCard;
```

### 6. App Entry Point Integration
```tsx
// src/main.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css';
import './i18n'; // Import the i18n configuration

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
```

## Best Practices and Common Pitfalls

### Best Practices
1. **Use translation keys instead of full sentences**
   - Makes it easier to maintain and update translations
   - Example: Use `common.buttons.save` instead of the full text

2. **Organize translations by feature/page**
   - Keeps translation files manageable
   - Makes it easier to find and update specific translations

3. **Use interpolation for dynamic content**
   - Example: `t('greeting', { name: user.name })` with translation key `"greeting": "Hello, {{name}}!"`

4. **Handle pluralization properly**
   - Use i18next's plural features for content that changes based on count
   - Example: `t('itemCount', { count: items.length })` with translation keys:
     ```json
     {
       "itemCount_zero": "No items",
       "itemCount_one": "One item",
       "itemCount_other": "{{count}} items"
     }
     ```

5. **Lazy load translations**
   - Load translations only when needed to improve initial load time
   - Especially important for large applications with many pages

### Common Pitfalls

1. **Hardcoding formats for dates, numbers, and currencies**
   - Use locale-aware formatting functions instead
   - Example: `new Date().toLocaleDateString(i18n.language)`

2. **Forgetting to handle RTL languages**
   - If supporting languages like Arabic or Hebrew, ensure UI adapts to RTL layout

3. **Not handling missing translations**
   - Provide fallbacks for missing translations
   - Consider using a translation management system for larger projects

4. **Ignoring context in translations**
   - Some words have different translations based on context
   - Use context or separate keys for ambiguous terms

5. **Not considering text expansion/contraction**
   - Some languages require more space than others
   - Design UI with flexibility to accommodate varying text lengths

## Monitoring and Maintenance

1. **Track missing translations**
   - Configure i18next to log missing translations during development
   - Regularly review and add missing translations

2. **Automated testing**
   - Add tests to verify that all pages render correctly in different languages
   - Consider screenshot testing for UI verification

3. **Translation updates**
   - Establish a process for updating translations when new features are added
   - Consider using a translation management system for larger teams
