# Test Teacher Feature - Filter Tests by Teacher ID

## Current Issue

As a teacher when accessing the tests page, I want to see only tests that I have created, not all tests in the system. Currently, the `/tests` endpoint returns all tests regardless of which teacher is logged in.

## Analysis

1. **Problem Description**:
   - Teachers need a personalized view of tests they've created
   - The current `/tests` API endpoint returns all tests 
   - The frontend displays all tests returned from the API

2. **Current Implementation**:
   - Backend query does not filter by the authenticated user's ID
   - Frontend displays all tests returned from the API
   - The user authentication and role checks are already in place

## Implementation Plan

### 1. Backend Updates

1. **Update Controller**:
   - Modify the `findAll` method in `TestsController` to pass the authenticated user to the service

   ```typescript
   // In tests.controller.ts
   @Get()
   @UseGuards(JwtAuthGuard, RolesGuard)
   findAll(@GetUser() user: User) {
     return this.testsService.findAll(user);
   }
   ```

2. **Update Service**:
   - Modify the `findAll` method in `TestsService` to filter tests based on user role
   - <PERSON><PERSON> should see all tests, teachers only see their own

   ```typescript
   // In tests.service.ts
   async findAll(currentUser?: User): Promise<Test[]> {
     try {
       // Build query based on user role
       const whereCondition: any = {};
       
       // If user is not admin, filter tests by creator
       if (currentUser && currentUser.role !== Role.ADMIN) {
         whereCondition.createdById = currentUser.id;
       }
       
       // Use explicit casting to handle Prisma include options
       const tests = await this.prisma.test.findMany({
         where: whereCondition,
         include: {
           category: true,
           createdBy: true
         },
         orderBy: {
           createdAt: 'desc'
         }
       } as any);
       
       // The rest of the method remains the same...
       // Process and return tests as before
     } catch (error) {
       this.logger.error(`Failed to find tests: ${error.message}`, error.stack);
       throw new InternalServerErrorException('Failed to retrieve tests');
     }
   }
   ```

3. **Error Handling**:
   - Keep the existing error handling in place
   - Ensure proper logging for debugging purposes

### 2. Frontend Adjustments

1. **No API Client Changes Needed**:
   - The existing `testsApi.getAll()` method doesn't need to change
   - The backend will handle the filtering based on the authenticated user

2. **Update UI to Indicate Filtering (Optional Enhancement)**:
   - Add an indicator to show teachers they're viewing only their tests

   ```tsx
   // In TestsList.tsx (optional enhancement)
   <div className="flex justify-between items-center mb-6">
     <h1 className="text-2xl font-bold dark:text-white">{t('tests:list.title')}</h1>
     {user?.role === UserRole.TEACHER && (
       <span className="text-sm text-gray-500 dark:text-gray-400">
         {t('tests:list.showingYourTests')}
       </span>
     )}
     {/* Rest of the existing code */}
   </div>
   ```

3. **Translation Updates**:
   - Add new translation keys for the UI enhancements

   ```json
   // In tests locale file
   {
     "list": {
       "showingYourTests": "Showing only your tests",
       // existing keys...
     }
   }
   ```

## Testing Plan

1. **Backend Testing**:
   - Unit test the `findAll` method in `TestsService` with different user roles
   - Verify the correct filtering is applied based on user role
   - Test with both admin and teacher roles

   ```typescript
   // Example test for the findAll method
   describe('findAll', () => {
     it('should return all tests for admin users', async () => {
       // Test implementation
     });

     it('should return only tests created by the teacher for teacher users', async () => {
       // Test implementation
     });
   });
   ```

2. **API Testing**:
   - Test the `/tests` endpoint when authenticated as a teacher
   - Verify only tests created by that teacher are returned
   - Test the endpoint as an admin and verify all tests are returned

3. **Frontend Testing**:
   - Log in as a teacher and verify only their tests appear
   - Log in as an admin and verify all tests appear
   - Check handling of edge cases (no tests available, server errors)

## Completion Criteria

The feature will be considered complete when:
1. Teachers can only see tests they have created when accessing the tests page
2. Admins can see all tests in the system
3. The UI clearly indicates that teachers are seeing only their tests (if enhancement implemented)
4. All error cases are properly handled
5. Tests for both frontend and backend are passing

## Additional Considerations

1. **Performance**: The filter is applied at the database query level which is efficient
2. **Compatibility**: This change is compatible with the creator display fix in test-teacher.md
3. **Security**: The endpoint remains protected by existing guards (JwtAuthGuard, RolesGuard)
4. **User Experience**: Consider adding a filter toggle for admins to switch between "all tests" and "my tests" in a future enhancement