# Import Answer Sheet Feature

## Overview
As a teacher, I want to import answer sheets from pasted text so that I can save time when creating tests. This feature will allow teachers to quickly add multiple answers at once by pasting formatted text instead of manually entering each answer.

## User Flow

1. Teacher navigates to the test creation or edit page
2. In the answer sheet section, teacher sees two separate import areas:
   - One for multiple choice questions
   - One for fill-in-the-blank questions
3. Teacher pastes formatted text into the appropriate textarea
4. Teacher clicks the "Parse Answers" button
5. The system parses the text and populates the answer sheet
6. Teacher can review and make any necessary adjustments before saving

## Input Format Requirements

### Multiple Choice Format
```
1. A
2. B
3. C
4. D
```
- Each line should contain a question number followed by a period and the correct option (A, B, C, or D)
- The system should ignore empty lines and whitespace
- The system should handle variations like "1) A" or "1 - A"

### Fill in the Blank Format
```
1. TEST ANSWER
2. TEST ANSWER 2
3. TEST ANSWER 3, ALTERNATE ANSWER
4. TEST ANSWER 4
```
- Each line should contain a question number followed by a period and the correct answer
- Multiple acceptable answers for the same question can be separated by commas
- The system should ignore empty lines and whitespace

## Implementation Details

### Frontend Changes
1. Add a textarea and "Parse Answers" button above each answer sheet section
2. Implement parsing logic for both formats
3. Update the existing answer arrays with the parsed data
4. Provide visual feedback on successful parsing or errors

### Backend Changes
No backend changes are required as the existing API endpoints already support:
- Creating tests with answer sheets
- Updating tests with modified answer sheets

The parsing will happen entirely on the frontend.

## Error Handling
- Display clear error messages for improperly formatted input
- Allow partial parsing (successfully formatted lines should be processed even if some lines have errors)
- Provide guidance on the expected format

## Acceptance Criteria

- [ ] Teacher can paste text into the multiple choice answer sheet input
- [ ] Teacher can paste text into the fill-in-the-blank answer sheet input
- [ ] Teacher can click on the parse button to process the multiple choice answer text
- [ ] Teacher can click on the parse button to process the fill-in-the-blank answer text
- [ ] The system correctly parses and populates the answer sheet with the imported data
- [ ] The system provides appropriate error messages for improperly formatted input
- [ ] The system preserves existing answers that aren't overwritten by the import
- [ ] The imported answers are correctly saved to the database when the test is saved

## Technical Notes

- Use regular expressions to parse the input text
- Ensure compatibility with the existing `MultipleChoiceAnswer` and `TextAnswer` interfaces
- Maintain proper question numbering across both answer types
- Consider adding a confirmation dialog if the import would overwrite existing answers
