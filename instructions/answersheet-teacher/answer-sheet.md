# As IELTS Suite teacher I want to perform answer sheet section so that I can create the test.

## Description

Answersheet is a feature adding to the creating test section so that as a teacher:

- They will add the list of question in to the Content section of the test (they prepared themself)
- There's an answer sheet in the create test dialog:
    - The answersheet is multigrid similar to the TOEIC testing answer sheet for multiple choice questions
    - The answersheet also contains the fill in the blank section for questions requiring text input
- The teacher while creating the test can add the answer in the answersheet
- The student can answer on the answer sheet

## Detailed Requirements

### For Teachers:
1. **Test Creation Form**:
   - Test Name: Input field for the test title
   - Content (renamed from Description): Rich text editor for test content, including passages, questions, and instructions
   - Answer Sheet Section: Interface for providing correct answers

2. **Multiple Choice Grid**:
   - Table/grid with question numbers as rows (1, 2, 3, etc.)
   - Answer options as columns (A, B, C, D)
   - Teachers select the correct answer for each question
   - Option to add multiple question rows at once

3. **Fill-in-the-Blank Section**:
   - For questions requiring text input rather than selection
   - Question reference number (matching content numbering)
   - Text field(s) for correct answer(s)
   - Option to specify validation rules (case sensitivity, alternate answers)

### Implementation Notes:
- No automated link between content questions and answer sheet - teachers maintain consistent numbering
- Clear visual distinction between multiple choice and fill-in-the-blank sections
- Preview option to see how the test will appear to students

## Acceptance criteria
- Complete flow for answer sheet creation including backend and frontend
- Teachers can create a test with correct answers in the answer sheet
- The answer sheet accurately represents both multiple choice and fill-in-the-blank questions
- Students can access and complete the test using the answer sheet (future implementation)






