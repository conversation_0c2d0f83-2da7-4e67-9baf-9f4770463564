# As an IELTS Suite teacher, I want a page that allows me to review and edit tests after creation

## Description

After creating an IELTS test, there is a "View" button that allows me to see the details of the created test. I want to enhance this page to allow reviewing and editing the test if mistakes are found. This page should also allow me to publish the test, making it visible to students.

## Detailed Requirements

### Test Review Features
- Display all test content including:
  - Test title, type, difficulty, and time limit
  - Test instructions and content
  - Answer sheet with correct answers (multiple choice and text answers)
  - Category assignment

### Test Editing Features
- Allow editing of all test properties:
  - Basic information (title, type, difficulty, time limit)
  - Test content and instructions
  - Answer sheet modifications (add/remove/change answers)

### Test Publication
- Provide a "Publish" button to make the test visible to students
- Display current publication status
- Allow unpublishing a previously published test
- Show confirmation dialog before changing publication status

## Acceptance Criteria

- Teacher can view all details of a created test including answer sheet
- Teacher can edit all aspects of the test after initial creation
- Teacher can publish/unpublish the test to control student visibility
- Changes are saved correctly and persist upon reopening the test
- U<PERSON> provides clear feedback for all actions (success/error messages)
- View functions properly on both desktop and mobile devices

