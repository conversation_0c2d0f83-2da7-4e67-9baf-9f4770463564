# Student Group Management for IELTS Suite Teachers

## Overview

As an IELTS Suite teacher, I want to create and manage groups of students so that I can organize my students by proficiency level, assign tests to entire groups at once, and track group performance over time.

## Description

Teachers need the ability to organize students into groups based on their English proficiency levels (e.g., Beginner, Intermediate, Advanced) or other criteria. This grouping functionality will allow teachers to:

1. Efficiently assign tests to multiple students at once
2. Track and compare performance across different student groups
3. Move students between groups as their proficiency changes
4. Generate analytics and insights at both group and individual levels

Each group will have a name, level designation, optional description, and a list of associated students. The system should support adding/removing students from groups and assigning tests to entire groups or specific students within a group.

## Database Schema

The following database models have been implemented:

```
Group: ✅
- id (UUID)
- name (String)
- level (String) - e.g., "Beginner", "Intermediate", "Advanced"
- description (String, optional)
- createdAt (DateTime)
- updatedAt (DateTime)
- isActive (Boolean)
- createdBy (String, Teacher's ID)

StudentGroup: ✅
- id (UUID)
- groupId (UUID, reference to Group)
- studentId (UUID, reference to User)
- joinedAt (DateTime)

GroupAssignment: ✅
- id (UUID)
- groupId (UUID, reference to Group)
- testId (UUID, reference to Test)
- assignedAt (DateTime)
- dueDate (DateTime, optional)
```

All models have been added to the Prisma schema and the database has been migrated successfully.

## API Endpoints

All endpoints have been implemented in the TeacherController and are protected by JwtAuthGuard and RolesGuard to ensure only teachers can access them.

### Group Management ✅
- `GET /api/teacher/groups` - Get all groups created by the teacher
- `POST /api/teacher/groups` - Create a new group
- `GET /api/teacher/groups/:id` - Get a specific group
- `PATCH /api/teacher/groups/:id` - Update a group
- `DELETE /api/teacher/groups/:id` - Delete/archive a group

### Student Management ✅
- `GET /api/teacher/groups/:id/students` - Get all students in a group
- `POST /api/teacher/groups/:id/students` - Add students to a group
- `DELETE /api/teacher/groups/:id/students/:studentId` - Remove a student from a group

### Test Assignment ✅
- `GET /api/teacher/groups/:id/tests` - Get all tests assigned to a group
- `POST /api/teacher/groups/:id/tests` - Assign a test to a group
- `DELETE /api/teacher/groups/:id/tests/:testId` - Remove a test assignment from a group

### Results and Analytics ✅
- `GET /api/teacher/groups/:id/results` - Get summary of test results for a group
- `GET /api/teacher/groups/:id/students/:studentId/results` - Get results for a specific student

## Acceptance Criteria

### Group Management
- [x] Teacher can create a new student group with a name, level, and description
- [x] Teacher can edit group details (name, level, description)
- [x] Teacher can view a list of all their created groups
- [x] Teacher can deactivate/archive a group

### Student Management
- [x] Teacher can add students to a group
- [x] Teacher can remove students from a group
- [x] Teacher can view all students in a group
- [x] Teacher can move a student from one group to another (implemented through remove and add operations)

### Test Assignment
- [x] Teacher can assign a test to an entire group
- [x] Teacher can set due dates for group test assignments
- [x] Teacher can assign a test to specific students within a group (implemented through individual assignments)
- [x] Teacher can remove test assignments from a group

### Results and Analytics
- [x] Teacher can view a summary of test results for the entire group
- [x] Teacher can view detailed test results for individual students
- [x] Teacher can compare a student's performance against the group average (basic implementation)
- [x] Teacher can track progress of students over time (basic implementation)

## Frontend Requirements

### Group Management UI
- [x] A "Groups" section in the teacher dashboard
  - Accessible via the main navigation menu
  - Shows a list of all groups created by the teacher
  - Displays group name, level, and student count
  - Includes options to create, edit, and archive groups
- [x] A form to create/edit groups
  - Fields for name, level, and description
  - Validation for required fields
  - Cancel and submit buttons
- [x] A list view of all groups with filtering and sorting options
  - Filter by name, level, and active status
  - Sort by name, level, creation date, and student count
  - Pagination for large numbers of groups

### Student Management UI
- [x] A view to see all students in a group
  - Accessible from the group details page
  - Shows student name, email, and join date
  - Includes options to remove students from the group
- [x] UI for adding/removing students from groups
  - Multi-select interface for adding multiple students at once
  - Search functionality to find students by name or email
  - Confirmation dialog for removing students
- [x] A student profile view showing group memberships
  - Accessible from the student list
  - Shows all groups the student belongs to
  - Includes options to add/remove the student from groups

### Test Assignment UI
- [ ] Interface for selecting tests to assign to groups
  - List of available tests with search and filter options
  - Option to set due dates for assignments
  - Preview of test details before assignment
- [ ] Calendar view for scheduling test assignments
  - Monthly calendar showing all assignments
  - Color-coding for different test types
  - Drag-and-drop interface for rescheduling
- [ ] Batch assignment capabilities
  - Select multiple tests to assign at once
  - Set common due date or individual due dates
  - Progress indicator for batch operations

### Results and Analytics UI
- [ ] Group performance dashboard with charts and metrics
  - Overview of group performance across all tests
  - Charts showing score distribution
  - Metrics for average score, completion rate, and improvement
- [ ] Individual student performance views
  - Detailed view of a student's performance in the group
  - Comparison with group averages
  - Timeline of test scores and progress
- [ ] Comparative analytics between students and groups
  - Side-by-side comparison of students or groups
  - Highlight of strengths and weaknesses
  - Export options for reports

## Implementation Notes

1. [x] Ensure proper authorization checks so teachers can only access their own groups
   - Implemented in TeacherService with checks on group ownership
   - Added RolesGuard to ensure only teachers can access the endpoints
2. [x] Implement efficient database queries to handle large groups
   - Used Prisma's include functionality for optimized queries
   - Implemented pagination for large result sets
3. [x] Design the UI with usability in mind, especially for batch operations
   - Created intuitive interfaces for adding multiple students
   - Added progress indicators for batch operations
4. [x] Consider future extensibility for additional group features
   - Designed database schema to support future enhancements
   - Implemented service methods that can be extended

## Testing Requirements

1. [x] Write comprehensive unit tests for all backend services
   - Created tests for TeacherService methods
   - Covered success cases and error handling
2. [x] Create integration tests for the API endpoints
   - Tested API endpoints with various inputs
   - Verified proper response formats
3. [x] Test edge cases like:
   - [x] Adding a student to multiple groups
   - [x] Removing a student who has pending assignments
   - [x] Group performance calculations with partial test completions

✅ Implementation completed and verified with unit tests and integration tests in the backend.
✅ Summary of the implementation created in the teacher-group-summary.md file.
