# Teacher Group Test Report Feature

## Overview

As a teacher, I want to view a detailed report of test results for my students in a group so that I can track their progress and identify areas for improvement. This report should focus on question-level analysis to help me understand which questions are most challenging for my students.

## Description

The teacher group test report feature will allow teachers to view a summary of test results for all students in a group, with a focus on question-by-question analysis. The primary goal is to provide a reporting chart showing the distribution of wrong answers by students for each question.

When a teacher accesses the group results page (e.g., http://localhost:3000/teacher/groups/{groupId}/results), they will see a list of tests assigned to the group. When they click on a specific test, the system will display a detailed report with charts showing the wrong answers by question.

## User Flow

1. Teacher logs in to the application
2. Teacher navigates to the Groups section
3. Teacher selects a specific group
4. Teacher clicks on the "Results" tab/button
5. Teacher sees a list of tests assigned to the group
6. Teacher clicks on a specific test
7. Teacher views the detailed question-by-question report for that test

## Detailed Requirements

### Backend Requirements

#### API Endpoints
- [ ] Enhance the existing `/api/teacher/groups/:id/results` endpoint to include test-specific data
- [ ] Create a new endpoint `/api/teacher/groups/:groupId/tests/:testId/results` to fetch detailed test results for a specific test in a group
- [ ] The endpoint should return:
  - Basic test information (title, description, type, etc.)
  - List of questions with their details
  - For each question:
    - Question number and text
    - Correct answer
    - Count of students who answered correctly
    - Count of students who answered incorrectly
    - Distribution of incorrect answers (what wrong answers were given and how many students gave each)

#### Data Processing
- [ ] Aggregate test results from all students in the group
- [ ] Calculate statistics for each question:
  - Percentage of correct answers
  - Percentage of incorrect answers
  - Distribution of specific wrong answers
- [ ] Handle both multiple-choice and fill-in-the-blank question types

### Frontend Requirements

#### Test List View
- [ ] Display a list of tests assigned to the group
- [ ] Show basic statistics for each test (average score, completion rate)
- [ ] Provide a clear way to select a test to view detailed results

#### Question Analysis View
- [ ] Create a new component `TestQuestionAnalysis` to display the question-by-question report
- [ ] For each question, display:
  - Question text and number
  - Correct answer
  - Chart showing the distribution of student answers
  - Percentage of students who answered correctly vs. incorrectly

#### Visualization Components
- [ ] Implement bar charts to show the number of students who answered each question incorrectly
- [ ] Use pie or donut charts to show the percentage breakdown of different wrong answers
- [ ] Include a summary chart showing the most problematic questions across the test
- [ ] Ensure all charts are responsive and work in both light and dark modes

## Technical Implementation Details

### Data Structure
The API should return data in the following format:
```typescript
interface TestQuestionAnalysis {
  testId: string;
  testTitle: string;
  groupId: string;
  totalStudents: number;
  completedCount: number;
  averageScore: number;
  questions: QuestionAnalysis[];
}

interface QuestionAnalysis {
  questionNumber: number;
  questionText: string;
  questionType: 'multipleChoice' | 'fillInTheBlank';
  correctAnswer: string | string[];
  correctCount: number;
  incorrectCount: number;
  incorrectAnswers: {
    answer: string;
    count: number;
  }[];
  skippedCount: number;
}
```

### Component Structure
- Create a new route `/teacher/groups/:groupId/tests/:testId/results`
- Implement a new page component `TestQuestionReport`
- Use existing chart libraries (or implement custom visualization components)
- Ensure proper error handling and loading states

### Integration with Existing Code
- Leverage the existing authentication and authorization mechanisms
- Use the existing API client structure
- Follow the established UI design patterns and components

## Acceptance Criteria

The feature will be considered complete when:

- [ ] Teachers can navigate to a specific test's results from the group results page
- [ ] Teachers can view a chart showing the number and percentage of students who answered each question incorrectly
- [ ] For each question, teachers can see the correct answer and the distribution of incorrect answers
- [ ] The charts are visually clear and provide actionable insights
- [ ] The feature works correctly for both multiple-choice and fill-in-the-blank questions
- [ ] The UI is responsive and works well on different screen sizes
- [ ] The feature is properly integrated with the existing dark mode support
- [ ] All data is properly loaded and displayed without errors

## Testing Plan

### Unit Tests
- Test the data aggregation and statistics calculation functions
- Test the chart components with various data scenarios

### Integration Tests
- Test the API endpoints with different group and test combinations
- Test the frontend components with mock data

### End-to-End Tests
- Test the complete user flow from logging in to viewing the test question report
- Verify that the charts display correctly and accurately reflect the test data

## Future Enhancements (Post-MVP)

- Add the ability to export the report as PDF or CSV
- Implement time-based analysis to track improvement over time
- Add comparative analysis between different groups taking the same test
- Provide AI-powered insights and recommendations based on the test results
