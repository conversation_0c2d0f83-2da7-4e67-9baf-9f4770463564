# Story 2: Teacher Managing Invitation Links

**As a teacher, I want to see active invitation links and be able to revoke them if needed.**

## Workflow
- Teacher can see all active invitation links for a group
- Teacher can revoke (disable) any invitation link
- Links automatically expire after their configured time period
- Each link tracks usage statistics (used count, last used)

## Implementation Requirements

### Backend (Controller)
```typescript
// In teacher.controller.ts
@Get('groups/:id/invites')
@Roles(Role.TEACHER)
getGroupInvites(@Param('id') id: string, @GetUser() user: User) {
  return this.teacherService.getGroupInvites(id, user.id);
}

@Patch('groups/:id/invites/:inviteId/revoke')
@Roles(Role.TEACHER)
revokeGroupInvite(
  @Param('id') id: string,
  @Param('inviteId') inviteId: string,
  @GetUser() user: User
) {
  return this.teacherService.revokeGroupInvite(id, inviteId, user.id);
}
```

### Backend (Service)
```typescript
// In teacher.service.ts
async getGroupInvites(groupId: string, teacherId: string): Promise<GroupInviteDto[]> {
  // Verify teacher owns the group
  await this.verifyGroupOwnership(groupId, teacherId);
  
  const invites = await this.prisma.groupInvite.findMany({
    where: { groupId },
    orderBy: { createdAt: 'desc' },
  });
  
  return invites.map(invite => ({
    ...invite,
    inviteUrl: `${process.env.FRONTEND_URL}/invites/groups/${invite.token}`,
  }));
}

async revokeGroupInvite(groupId: string, inviteId: string, teacherId: string): Promise<void> {
  // Verify teacher owns the group
  await this.verifyGroupOwnership(groupId, teacherId);
  
  await this.prisma.groupInvite.update({
    where: {
      id: inviteId,
      groupId,
    },
    data: {
      isActive: false,
    },
  });
}
```

### Frontend
- Create a tab or section in the group detail page showing all active invites
- Display each invite with expiration date, usage count, creation date
- Add a "Revoke" button next to each active invitation
- Implement confirmation dialog for revocation

## API Client
```typescript
// Add to src/api/teacherApi.ts
const teacherApi = {
  // Existing methods...
  
  getGroupInvites: async (groupId: string): Promise<GroupInviteDto[]> => {
    const response = await axiosClient.get(`/teacher/groups/${groupId}/invites`);
    return response.data;
  },
  
  revokeGroupInvite: async (groupId: string, inviteId: string): Promise<void> => {
    await axiosClient.patch(`/teacher/groups/${groupId}/invites/${inviteId}/revoke`);
  },
};
```
