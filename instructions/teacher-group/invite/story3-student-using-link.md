# Story 3: Student Using Invitation Link

**As a student, I want to use an invitation link to join a group without requiring manual addition by the teacher.**

## Workflow
- Student clicks on the invitation link
- If student is logged in, they're added to the group automatically
- Student is redirected to the group page

## Implementation Requirements

### Backend (Controller)
```typescript
// src/invites/invites.controller.ts
@Controller('invites')
export class InvitesController {
  constructor(private readonly invitesService: InvitesService) {}

  @Get('groups/:token')
  @Public() // This endpoint should be accessible without auth
  getInviteInfo(@Param('token') token: string) {
    return this.invitesService.getInviteInfo(token);
  }

  @Post('groups/:token/accept')
  @UseGuards(JwtAuthGuard) // Must be authenticated to accept
  acceptGroupInvite(
    @Param('token') token: string,
    @GetUser() user: User
  ) {
    return this.invitesService.acceptGroupInvite(token, user.id);
  }
}
```

### Backend (Service)
```typescript
// src/invites/invites.service.ts
@Injectable()
export class InvitesService {
  constructor(private readonly prisma: PrismaService) {}

  async getInviteInfo(token: string) {
    const invite = await this.prisma.groupInvite.findUnique({
      where: { token },
      include: {
        group: {
          select: {
            name: true,
            level: true,
            teacher: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    });

    if (!invite || !invite.isActive) {
      throw new NotFoundException('Invitation not found or has been revoked');
    }

    if (invite.expiresAt < new Date()) {
      throw new BadRequestException('Invitation has expired');
    }

    if (invite.maxUses !== null && invite.useCount >= invite.maxUses) {
      throw new BadRequestException('Invitation has reached maximum usage');
    }

    return {
      groupName: invite.group.name,
      groupLevel: invite.group.level,
      teacherName: `${invite.group.teacher.firstName} ${invite.group.teacher.lastName}`,
      expiresAt: invite.expiresAt,
    };
  }

  async acceptGroupInvite(token: string, userId: string) {
    // Start a transaction
    return this.prisma.$transaction(async (tx) => {
      // Get the invite
      const invite = await tx.groupInvite.findUnique({
        where: { token },
      });

      if (!invite || !invite.isActive) {
        throw new NotFoundException('Invitation not found or has been revoked');
      }

      if (invite.expiresAt < new Date()) {
        throw new BadRequestException('Invitation has expired');
      }

      if (invite.maxUses !== null && invite.useCount >= invite.maxUses) {
        throw new BadRequestException('Invitation has reached maximum usage');
      }

      // Check if user is already a member of the group
      const existingMembership = await tx.studentGroup.findUnique({
        where: {
          groupId_studentId: {
            groupId: invite.groupId,
            studentId: userId,
          },
        },
      });

      if (existingMembership) {
        throw new ConflictException('You are already a member of this group');
      }

      // Add user to the group
      await tx.studentGroup.create({
        data: {
          groupId: invite.groupId,
          studentId: userId,
        },
      });

      // Update the invite usage
      await tx.groupInvite.update({
        where: { id: invite.id },
        data: {
          useCount: { increment: 1 },
          lastUsedAt: new Date(),
        },
      });

      // Get group details to return
      const group = await tx.group.findUnique({
        where: { id: invite.groupId },
        select: {
          id: true,
          name: true,
          level: true,
        },
      });

      return {
        success: true,
        message: `You have been added to the group: ${group.name}`,
        groupId: invite.groupId,
      };
    });
  }
}
```

### Frontend
- Create an invitation landing page at `/invites/groups/:token`
- Display group information (name, teacher, level)
- Add a "Join Group" button for logged-in users
- Implement success and error message display
- Add redirection to group page after successful joining

## API Client
```typescript
// Add to src/api/invitesApi.ts
const invitesApi = {
  getInviteInfo: async (token: string): Promise<any> => {
    const response = await axiosClient.get(`/invites/groups/${token}`);
    return response.data;
  },
  
  acceptGroupInvite: async (token: string): Promise<any> => {
    const response = await axiosClient.post(`/invites/groups/${token}/accept`);
    return response.data;
  },
};
```
