# Teacher Group Invitation Feature

## Overview

As a teacher, I want to invite students to join my group so that they can take the test I created.

This feature allows teachers to generate invitation links for students to join their groups. Students can use these links to join groups without requiring the teacher to manually add them.

## Database Changes

Add a new model to the Prisma schema:

```prisma
model GroupInvite {
  id          String    @id @default(uuid())
  groupId     String    @map("group_id")
  token       String    @unique
  createdById String    @map("created_by_id")
  createdAt   DateTime  @default(now()) @map("created_at")
  expiresAt   DateTime  @map("expires_at")
  maxUses     Int?      @map("max_uses") // null means unlimited
  useCount    Int       @default(0) @map("use_count")
  isActive    Boolean   @default(true)
  lastUsedAt  DateTime? @map("last_used_at")

  // Relations
  group       Group     @relation(fields: [groupId], references: [id], onDelete: Cascade)
  createdBy   User      @relation(fields: [createdById], references: [id])

  @@map("group_invites")
}
```

Update the Group model to include the relation:

```prisma
model Group {
  // Existing fields
  ...

  // Add this line
  invites     GroupInvite[]
}
```

## Implementation Plan

1. **Backend:**
   - Update Prisma schema with GroupInvite model
   - Run migrations
   - Create DTOs and entities
   - Implement TeacherService methods for invite management
   - Create InvitesModule with controller and service
   - Implement invite acceptance logic
   - Add proper validation and error handling

2. **Frontend:**
   - Create API client methods
   - Add invitation management to group details page
   - Create invitation modal component
   - Create invitation landing page
   - Implement redirect logic for authentication
   - Add success/error notifications

## Security Considerations

1. Use cryptographically secure random tokens
2. Implement proper expiration handling
3. Add rate limiting to prevent invite abuse
4. Always verify teacher ownership before invite operations
5. Validate invite state before allowing acceptance
