# Story 1: Teacher Generating Invitation Links

**As a teacher, I want to generate an invitation link for my group so that I can share it with students.**

## Workflow
- Teacher navigates to group details page
- Teacher clicks "Generate Invitation Link" button
- System generates a unique link with configurable expiration (default: 7 days)
- Teacher can copy the link and share it

## Implementation Requirements

### Backend (DTO)
```typescript
// src/teacher/dto/create-group-invite.dto.ts
export class CreateGroupInviteDto {
  expiresInDays?: number; // Default to 7 if not provided
  maxUses?: number; // Optional, unlimited if not provided
}

// src/teacher/dto/group-invite.dto.ts
export class GroupInviteDto {
  id: string;
  token: string;
  createdAt: Date;
  expiresAt: Date;
  maxUses?: number;
  useCount: number;
  isActive: boolean;
  lastUsedAt?: Date;
  inviteUrl: string; // Full URL for invitation
}
```

### Backend (Controller)
```typescript
// In teacher.controller.ts
@Post('groups/:id/invites')
@Roles(Role.TEACHER)
createGroupInvite(
  @Param('id') id: string,
  @Body() createGroupInviteDto: CreateGroupInviteDto,
  @GetUser() user: User
) {
  return this.teacherService.createGroupInvite(id, createGroupInviteDto, user.id);
}
```

### Backend (Service)
```typescript
// In teacher.service.ts
async createGroupInvite(
  groupId: string,
  createGroupInviteDto: CreateGroupInviteDto,
  teacherId: string
): Promise<GroupInviteDto> {
  // Verify teacher owns the group
  await this.verifyGroupOwnership(groupId, teacherId);
  
  const { expiresInDays = 7, maxUses } = createGroupInviteDto;
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + expiresInDays);
  
  // Generate a secure random token
  const token = crypto.randomBytes(32).toString('hex');
  
  const invite = await this.prisma.groupInvite.create({
    data: {
      token,
      expiresAt,
      maxUses,
      groupId,
      createdById: teacherId,
    },
  });
  
  return {
    ...invite,
    inviteUrl: `${process.env.FRONTEND_URL}/invites/groups/${token}`,
  };
}
```

### Frontend
- Create an "Invitation" button on the group details page
- Implement a modal for configuring invitation settings
- Add a copy-to-clipboard function for the generated link
- Optional: Generate QR code for the invitation link

## API Client
```typescript
// Add to src/api/teacherApi.ts
const teacherApi = {
  // Existing methods...
  
  createGroupInvite: async (groupId: string, data: CreateGroupInviteDto): Promise<GroupInviteDto> => {
    const response = await axiosClient.post(`/teacher/groups/${groupId}/invites`, data);
    return response.data;
  }
};
```

Trigger build
