# Story 4: Non-Registered Student Signup Flow

**As a student without an account, I want to be redirected to sign up when clicking an invitation link.**

## Workflow
- Student clicks on the invitation link
- If student is not logged in, they're prompted to log in or sign up
- After authentication, student is automatically added to the group
- Student is redirected to the group page

## Implementation Requirements

### Frontend Components
- Detect authentication status on the invitation landing page
- If not authenticated, show login/signup buttons
- Store the invitation token in session/local storage or URL parameters
- After successful authentication, automatically process the invitation
- Create a "Join after signup" flow in the authentication process

### Example Code for Landing Page
```typescript
// Example frontend code for the landing page component
const InvitationLandingPage = () => {
  const { token } = useParams();
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const [groupInfo, setGroupInfo] = useState(null);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    // Get invitation info
    invitesApi.getInviteInfo(token)
      .then(data => setGroupInfo(data))
      .catch(err => setError(err.message));
  }, [token]);
  
  const handleAcceptInvite = async () => {
    if (!isAuthenticated) {
      // Store token and redirect to login
      sessionStorage.setItem('pendingInvite', token);
      navigate('/auth/login?redirect=invite');
      return;
    }
    
    try {
      const result = await invitesApi.acceptGroupInvite(token);
      toast.success(result.message);
      navigate(`/student/groups/${result.groupId}`);
    } catch (err) {
      setError(err.message);
    }
  };
  
  // Render invitation details and action buttons
  return (
    <div className="invitation-page">
      {error ? (
        <div className="error-container">
          <h2>Unable to process invitation</h2>
          <p className="error-message">{error}</p>
          <Button onClick={() => navigate('/dashboard')}>Go to Dashboard</Button>
        </div>
      ) : !groupInfo ? (
        <div className="loading">Loading invitation details...</div>
      ) : (
        <div className="invitation-card">
          <h2>You've been invited to join a group</h2>
          <div className="group-info">
            <p><strong>Group Name:</strong> {groupInfo.groupName}</p>
            <p><strong>Level:</strong> {groupInfo.groupLevel}</p>
            <p><strong>Teacher:</strong> {groupInfo.teacherName}</p>
            <p><strong>Expires:</strong> {new Date(groupInfo.expiresAt).toLocaleDateString()}</p>
          </div>
          
          {isAuthenticated ? (
            <Button onClick={handleAcceptInvite} variant="primary">
              Join Group
            </Button>
          ) : (
            <div className="auth-buttons">
              <p>You need to log in or sign up to join this group</p>
              <Button 
                onClick={() => {
                  sessionStorage.setItem('pendingInvite', token);
                  navigate('/auth/login?redirect=invite');
                }}
                variant="primary"
                className="mr-2"
              >
                Log in
              </Button>
              <Button 
                onClick={() => {
                  sessionStorage.setItem('pendingInvite', token);
                  navigate('/auth/signup?redirect=invite');
                }}
              >
                Sign up
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
```

### Authentication Flow Modifications
```typescript
// Add to auth service or login/signup component
const handleSuccessfulAuth = () => {
  const pendingInvite = sessionStorage.getItem('pendingInvite');
  
  if (pendingInvite) {
    sessionStorage.removeItem('pendingInvite');
    invitesApi.acceptGroupInvite(pendingInvite)
      .then(result => {
        toast.success(result.message);
        navigate(`/student/groups/${result.groupId}`);
      })
      .catch(err => {
        toast.error(`Failed to join group: ${err.message}`);
        navigate('/student/dashboard');
      });
  } else {
    navigate('/student/dashboard');
  }
};
```

## UI Design Considerations
- Design a welcoming landing page that clearly explains the invitation
- Use clear call-to-action buttons for login/signup
- Provide helpful error messages for expired or invalid invitations
- Consider using a branded background or illustration to make the page appealing
- Ensure mobile-responsive design for sharing links on mobile devices
