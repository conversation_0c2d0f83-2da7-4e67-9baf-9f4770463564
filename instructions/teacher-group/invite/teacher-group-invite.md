# Teacher Group Invitation Feature

## Overview

As a teacher, I want to invite students to join my group so that they can take the test I created.

This feature allows teachers to generate invitation links for students to join their groups. Students can use these links to join groups without requiring the teacher to manually add them.

## User Stories and Implementation

### Story 1: Teacher Generating Invitation Links
**As a teacher, I want to generate an invitation link for my group so that I can share it with students.**

#### Workflow
- Teacher navigates to group details page
- Teacher clicks "Generate Invitation Link" button
- System generates a unique link with configurable expiration (default: 7 days)
- Teacher can copy the link and share it

#### Implementation Requirements

**Database:**
```prisma
model GroupInvite {
  id          String    @id @default(uuid())
  groupId     String    @map("group_id")
  token       String    @unique
  createdById String    @map("created_by_id")
  createdAt   DateTime  @default(now()) @map("created_at")
  expiresAt   DateTime  @map("expires_at")
  maxUses     Int?      @map("max_uses") // null means unlimited
  useCount    Int       @default(0) @map("use_count")
  isActive    Boolean   @default(true)
  lastUsedAt  DateTime? @map("last_used_at")

  // Relations
  group       Group     @relation(fields: [groupId], references: [id], onDelete: Cascade)
  createdBy   User      @relation(fields: [createdById], references: [id])

  @@map("group_invites")
}
```

**Backend (DTO):**
```typescript
// src/teacher/dto/create-group-invite.dto.ts
export class CreateGroupInviteDto {
  expiresInDays?: number; // Default to 7 if not provided
  maxUses?: number; // Optional, unlimited if not provided
}
```

**Backend (Controller):**
```typescript
// In teacher.controller.ts
@Post('groups/:id/invites')
@Roles(Role.TEACHER)
createGroupInvite(
  @Param('id') id: string,
  @Body() createGroupInviteDto: CreateGroupInviteDto,
  @GetUser() user: User
) {
  return this.teacherService.createGroupInvite(id, createGroupInviteDto, user.id);
}
```

**Backend (Service):**
```typescript
// In teacher.service.ts
async createGroupInvite(
  groupId: string,
  createGroupInviteDto: CreateGroupInviteDto,
  teacherId: string
): Promise<GroupInviteDto> {
  // Verify teacher owns the group
  await this.verifyGroupOwnership(groupId, teacherId);
  
  const { expiresInDays = 7, maxUses } = createGroupInviteDto;
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + expiresInDays);
  
  // Generate a secure random token
  const token = crypto.randomBytes(32).toString('hex');
  
  const invite = await this.prisma.groupInvite.create({
    data: {
      token,
      expiresAt,
      maxUses,
      groupId,
      createdById: teacherId,
    },
  });
  
  return {
    ...invite,
    inviteUrl: `${process.env.FRONTEND_URL}/invites/groups/${token}`,
  };
}
```

**Frontend:**
- Create an "Invitation" button on the group details page
- Implement a modal for configuring invitation settings
- Add a copy-to-clipboard function for the generated link
- Optional: Generate QR code for the invitation link

### Story 2: Teacher Managing Invitation Links
**As a teacher, I want to see active invitation links and be able to revoke them if needed.**

#### Workflow
- Teacher can see all active invitation links for a group
- Teacher can revoke (disable) any invitation link
- Links automatically expire after their configured time period
- Each link tracks usage statistics (used count, last used)

#### Implementation Requirements

**Backend (Controller):**
```typescript
// In teacher.controller.ts
@Get('groups/:id/invites')
@Roles(Role.TEACHER)
getGroupInvites(@Param('id') id: string, @GetUser() user: User) {
  return this.teacherService.getGroupInvites(id, user.id);
}

@Patch('groups/:id/invites/:inviteId/revoke')
@Roles(Role.TEACHER)
revokeGroupInvite(
  @Param('id') id: string,
  @Param('inviteId') inviteId: string,
  @GetUser() user: User
) {
  return this.teacherService.revokeGroupInvite(id, inviteId, user.id);
}
```

**Backend (Service):**
```typescript
// In teacher.service.ts
async getGroupInvites(groupId: string, teacherId: string): Promise<GroupInviteDto[]> {
  // Verify teacher owns the group
  await this.verifyGroupOwnership(groupId, teacherId);
  
  const invites = await this.prisma.groupInvite.findMany({
    where: { groupId },
    orderBy: { createdAt: 'desc' },
  });
  
  return invites.map(invite => ({
    ...invite,
    inviteUrl: `${process.env.FRONTEND_URL}/invites/groups/${invite.token}`,
  }));
}

async revokeGroupInvite(groupId: string, inviteId: string, teacherId: string): Promise<void> {
  // Verify teacher owns the group
  await this.verifyGroupOwnership(groupId, teacherId);
  
  await this.prisma.groupInvite.update({
    where: {
      id: inviteId,
      groupId,
    },
    data: {
      isActive: false,
    },
  });
}
```

**Frontend:**
- Create a tab or section in the group detail page showing all active invites
- Display each invite with expiration date, usage count, creation date
- Add a "Revoke" button next to each active invitation
- Implement confirmation dialog for revocation

### Story 3: Student Using Invitation Link
**As a student, I want to use an invitation link to join a group without requiring manual addition by the teacher.**

#### Workflow
- Student clicks on the invitation link
- If student is logged in, they're added to the group automatically
- Student is redirected to the group page

#### Implementation Requirements

**Backend (Controller):**
```typescript
// src/invites/invites.controller.ts
@Controller('invites')
export class InvitesController {
  constructor(private readonly invitesService: InvitesService) {}

  @Get('groups/:token')
  @Public() // This endpoint should be accessible without auth
  getInviteInfo(@Param('token') token: string) {
    return this.invitesService.getInviteInfo(token);
  }

  @Post('groups/:token/accept')
  @UseGuards(JwtAuthGuard) // Must be authenticated to accept
  acceptGroupInvite(
    @Param('token') token: string,
    @GetUser() user: User
  ) {
    return this.invitesService.acceptGroupInvite(token, user.id);
  }
}
```

**Backend (Service):**
```typescript
// src/invites/invites.service.ts
@Injectable()
export class InvitesService {
  constructor(private readonly prisma: PrismaService) {}

  async getInviteInfo(token: string) {
    const invite = await this.prisma.groupInvite.findUnique({
      where: { token },
      include: {
        group: {
          select: {
            name: true,
            level: true,
            teacher: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    });

    if (!invite || !invite.isActive) {
      throw new NotFoundException('Invitation not found or has been revoked');
    }

    if (invite.expiresAt < new Date()) {
      throw new BadRequestException('Invitation has expired');
    }

    if (invite.maxUses !== null && invite.useCount >= invite.maxUses) {
      throw new BadRequestException('Invitation has reached maximum usage');
    }

    return {
      groupName: invite.group.name,
      groupLevel: invite.group.level,
      teacherName: `${invite.group.teacher.firstName} ${invite.group.teacher.lastName}`,
      expiresAt: invite.expiresAt,
    };
  }

  async acceptGroupInvite(token: string, userId: string) {
    // Start a transaction
    return this.prisma.$transaction(async (tx) => {
      // Get the invite
      const invite = await tx.groupInvite.findUnique({
        where: { token },
      });

      if (!invite || !invite.isActive) {
        throw new NotFoundException('Invitation not found or has been revoked');
      }

      if (invite.expiresAt < new Date()) {
        throw new BadRequestException('Invitation has expired');
      }

      if (invite.maxUses !== null && invite.useCount >= invite.maxUses) {
        throw new BadRequestException('Invitation has reached maximum usage');
      }

      // Check if user is already a member of the group
      const existingMembership = await tx.studentGroup.findUnique({
        where: {
          groupId_studentId: {
            groupId: invite.groupId,
            studentId: userId,
          },
        },
      });

      if (existingMembership) {
        throw new ConflictException('You are already a member of this group');
      }

      // Add user to the group
      await tx.studentGroup.create({
        data: {
          groupId: invite.groupId,
          studentId: userId,
        },
      });

      // Update the invite usage
      await tx.groupInvite.update({
        where: { id: invite.id },
        data: {
          useCount: { increment: 1 },
          lastUsedAt: new Date(),
        },
      });

      return {
        success: true,
        message: `You have been added to the group: ${invite.group.name}`,
        groupId: invite.groupId,
      };
    });
  }
}
```

**Frontend:**
- Create an invitation landing page at `/invites/groups/:token`
- Display group information (name, teacher, level)
- Add a "Join Group" button for logged-in users
- Implement success and error message display
- Add redirection to group page after successful joining

### Story 4: Non-Registered Student Signup Flow
**As a student without an account, I want to be redirected to sign up when clicking an invitation link.**

#### Workflow
- Student clicks on the invitation link
- If student is not logged in, they're prompted to log in or sign up
- After authentication, student is automatically added to the group
- Student is redirected to the group page

#### Implementation Requirements

**Frontend:**
- Detect authentication status on the invitation landing page
- If not authenticated, show login/signup buttons
- Store the invitation token in session/local storage or URL parameters
- After successful authentication, automatically process the invitation
- Create a "Join after signup" flow in the authentication process

```typescript
// Example frontend code for the landing page component
const InvitationLandingPage = () => {
  const { token } = useParams();
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const [groupInfo, setGroupInfo] = useState(null);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    // Get invitation info
    invitesApi.getInviteInfo(token)
      .then(data => setGroupInfo(data))
      .catch(err => setError(err.message));
  }, [token]);
  
  const handleAcceptInvite = async () => {
    if (!isAuthenticated) {
      // Store token and redirect to login
      sessionStorage.setItem('pendingInvite', token);
      navigate('/auth/login?redirect=invite');
      return;
    }
    
    try {
      const result = await invitesApi.acceptGroupInvite(token);
      toast.success(result.message);
      navigate(`/student/groups/${result.groupId}`);
    } catch (err) {
      setError(err.message);
    }
  };
  
  // Render invitation details and action buttons
};
```

**Authentication Flow Modifications:**
- Check for pending invites after successful login/signup
- Process any pending invites automatically
- Redirect to appropriate page based on invitation status

```typescript
// Add to auth service or login/signup component
const handleSuccessfulAuth = () => {
  const pendingInvite = sessionStorage.getItem('pendingInvite');
  
  if (pendingInvite) {
    sessionStorage.removeItem('pendingInvite');
    invitesApi.acceptGroupInvite(pendingInvite)
      .then(result => {
        toast.success(result.message);
        navigate(`/student/groups/${result.groupId}`);
      })
      .catch(err => {
        toast.error(`Failed to join group: ${err.message}`);
        navigate('/student/dashboard');
      });
  } else {
    navigate('/student/dashboard');
  }
};
```

## Technical Resources

### Database Changes

Add a new model to the Prisma schema:

```prisma
model GroupInvite {
  id          String    @id @default(uuid())
  groupId     String    @map("group_id")
  token       String    @unique
  createdById String    @map("created_by_id")
  createdAt   DateTime  @default(now()) @map("created_at")
  expiresAt   DateTime  @map("expires_at")
  maxUses     Int?      @map("max_uses") // null means unlimited
  useCount    Int       @default(0) @map("use_count")
  isActive    Boolean   @default(true)
  lastUsedAt  DateTime? @map("last_used_at")

  // Relations
  group       Group     @relation(fields: [groupId], references: [id], onDelete: Cascade)
  createdBy   User      @relation(fields: [createdById], references: [id])

  @@map("group_invites")
}
```

Update the Group model to include the relation:

```prisma
model Group {
  // Existing fields
  ...

  // Add this line
  invites     GroupInvite[]
}
```

### Backend Implementation

1. **Create DTOs:**

```typescript
// src/teacher/dto/create-group-invite.dto.ts
export class CreateGroupInviteDto {
  expiresInDays?: number; // Default to 7 if not provided
  maxUses?: number; // Optional, unlimited if not provided
}

// src/teacher/dto/group-invite.dto.ts
export class GroupInviteDto {
  id: string;
  token: string;
  createdAt: Date;
  expiresAt: Date;
  maxUses?: number;
  useCount: number;
  isActive: boolean;
  lastUsedAt?: Date;
  inviteUrl: string; // Full URL for invitation
}
```

2. **Add Controller Endpoints:**

```typescript
// In teacher.controller.ts
@Post('groups/:id/invites')
@Roles(Role.TEACHER)
createGroupInvite(
  @Param('id') id: string,
  @Body() createGroupInviteDto: CreateGroupInviteDto,
  @GetUser() user: User
) {
  return this.teacherService.createGroupInvite(id, createGroupInviteDto, user.id);
}

@Get('groups/:id/invites')
@Roles(Role.TEACHER)
getGroupInvites(@Param('id') id: string, @GetUser() user: User) {
  return this.teacherService.getGroupInvites(id, user.id);
}

@Patch('groups/:id/invites/:inviteId/revoke')
@Roles(Role.TEACHER)
revokeGroupInvite(
  @Param('id') id: string,
  @Param('inviteId') inviteId: string,
  @GetUser() user: User
) {
  return this.teacherService.revokeGroupInvite(id, inviteId, user.id);
}
```

3. **Add a new controller for processing invites:**

```typescript
// src/invites/invites.controller.ts
@Controller('invites')
export class InvitesController {
  constructor(private readonly invitesService: InvitesService) {}

  @Get('groups/:token')
  @Public() // This endpoint should be accessible without auth
  getInviteInfo(@Param('token') token: string) {
    return this.invitesService.getInviteInfo(token);
  }

  @Post('groups/:token/accept')
  @UseGuards(JwtAuthGuard) // Must be authenticated to accept
  acceptGroupInvite(
    @Param('token') token: string,
    @GetUser() user: User
  ) {
    return this.invitesService.acceptGroupInvite(token, user.id);
  }
}
```

4. **Service Methods:**

```typescript
// In teacher.service.ts
async createGroupInvite(
  groupId: string,
  createGroupInviteDto: CreateGroupInviteDto,
  teacherId: string
): Promise<GroupInviteDto> {
  // Verify teacher owns the group
  await this.verifyGroupOwnership(groupId, teacherId);
  
  const { expiresInDays = 7, maxUses } = createGroupInviteDto;
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + expiresInDays);
  
  // Generate a secure random token
  const token = crypto.randomBytes(32).toString('hex');
  
  const invite = await this.prisma.groupInvite.create({
    data: {
      token,
      expiresAt,
      maxUses,
      groupId,
      createdById: teacherId,
    },
  });
  
  return {
    ...invite,
    inviteUrl: `${process.env.FRONTEND_URL}/invites/groups/${token}`,
  };
}

async getGroupInvites(groupId: string, teacherId: string): Promise<GroupInviteDto[]> {
  // Verify teacher owns the group
  await this.verifyGroupOwnership(groupId, teacherId);
  
  const invites = await this.prisma.groupInvite.findMany({
    where: { groupId },
    orderBy: { createdAt: 'desc' },
  });
  
  return invites.map(invite => ({
    ...invite,
    inviteUrl: `${process.env.FRONTEND_URL}/invites/groups/${invite.token}`,
  }));
}

async revokeGroupInvite(groupId: string, inviteId: string, teacherId: string): Promise<void> {
  // Verify teacher owns the group
  await this.verifyGroupOwnership(groupId, teacherId);
  
  await this.prisma.groupInvite.update({
    where: {
      id: inviteId,
      groupId,
    },
    data: {
      isActive: false,
    },
  });
}
```

5. **Create a new Invites Service:**

```typescript
// src/invites/invites.service.ts
@Injectable()
export class InvitesService {
  constructor(private readonly prisma: PrismaService) {}

  async getInviteInfo(token: string) {
    const invite = await this.prisma.groupInvite.findUnique({
      where: { token },
      include: {
        group: {
          select: {
            name: true,
            level: true,
            teacher: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    });

    if (!invite || !invite.isActive) {
      throw new NotFoundException('Invitation not found or has been revoked');
    }

    if (invite.expiresAt < new Date()) {
      throw new BadRequestException('Invitation has expired');
    }

    if (invite.maxUses !== null && invite.useCount >= invite.maxUses) {
      throw new BadRequestException('Invitation has reached maximum usage');
    }

    return {
      groupName: invite.group.name,
      groupLevel: invite.group.level,
      teacherName: `${invite.group.teacher.firstName} ${invite.group.teacher.lastName}`,
      expiresAt: invite.expiresAt,
    };
  }

  async acceptGroupInvite(token: string, userId: string) {
    // Start a transaction
    return this.prisma.$transaction(async (tx) => {
      // Get the invite
      const invite = await tx.groupInvite.findUnique({
        where: { token },
      });

      if (!invite || !invite.isActive) {
        throw new NotFoundException('Invitation not found or has been revoked');
      }

      if (invite.expiresAt < new Date()) {
        throw new BadRequestException('Invitation has expired');
      }

      if (invite.maxUses !== null && invite.useCount >= invite.maxUses) {
        throw new BadRequestException('Invitation has reached maximum usage');
      }

      // Check if user is already a member of the group
      const existingMembership = await tx.studentGroup.findUnique({
        where: {
          groupId_studentId: {
            groupId: invite.groupId,
            studentId: userId,
          },
        },
      });

      if (existingMembership) {
        throw new ConflictException('You are already a member of this group');
      }

      // Add user to the group
      await tx.studentGroup.create({
        data: {
          groupId: invite.groupId,
          studentId: userId,
        },
      });

      // Update the invite usage
      await tx.groupInvite.update({
        where: { id: invite.id },
        data: {
          useCount: { increment: 1 },
          lastUsedAt: new Date(),
        },
      });

      // Get group details to return
      const group = await tx.group.findUnique({
        where: { id: invite.groupId },
        select: {
          id: true,
          name: true,
          level: true,
        },
      });

      return {
        success: true,
        message: `You have been added to the group: ${group.name}`,
        group,
      };
    });
  }
}
```

### Frontend Implementation

1. **Group Management Page Updates:**
   - Add a new tab or section for "Invitations"
   - Show a list of active invitations with expiration dates and usage stats
   - Add a button to generate new invitation links
   - Add a button to revoke each invitation

2. **Generate Invitation Modal:**
   - Fields for expiration days (default: 7)
   - Optional field for maximum uses
   - Generate button that calls the API
   - Copy button that copies the link to clipboard
   - QR code display option for easy sharing

3. **Invitation Landing Page:**
   - Route: `/invites/groups/:token`
   - Displays group info: name, teacher, level
   - Shows a "Join Group" button
   - If not logged in, offers login/signup buttons
   - After login/signup, automatically processes the invitation

4. **Invitation API Client:**

```typescript
// Add to src/api/teacherApi.ts
const teacherApi = {
  // Existing methods...
  
  createGroupInvite: async (groupId: string, data: CreateGroupInviteDto): Promise<GroupInviteDto> => {
    const response = await axiosClient.post(`/teacher/groups/${groupId}/invites`, data);
    return response.data;
  },
  
  getGroupInvites: async (groupId: string): Promise<GroupInviteDto[]> => {
    const response = await axiosClient.get(`/teacher/groups/${groupId}/invites`);
    return response.data;
  },
  
  revokeGroupInvite: async (groupId: string, inviteId: string): Promise<void> => {
    await axiosClient.patch(`/teacher/groups/${groupId}/invites/${inviteId}/revoke`);
  },
};

// Add to src/api/invitesApi.ts
const invitesApi = {
  getInviteInfo: async (token: string): Promise<any> => {
    const response = await axiosClient.get(`/invites/groups/${token}`);
    return response.data;
  },
  
  acceptGroupInvite: async (token: string): Promise<any> => {
    const response = await axiosClient.post(`/invites/groups/${token}/accept`);
    return response.data;
  },
};
```

## Implementation Plan

1. **Backend:**
   - Update Prisma schema with GroupInvite model
   - Run migrations
   - Create DTOs and entities
   - Implement TeacherService methods for invite management
   - Create InvitesModule with controller and service
   - Implement invite acceptance logic
   - Add proper validation and error handling

2. **Frontend:**
   - Create API client methods
   - Add invitation management to group details page
   - Create invitation modal component
   - Create invitation landing page
   - Implement redirect logic for authentication
   - Add success/error notifications

## Testing Plan

1. **Backend Tests:**
   - Test invite generation with various parameters
   - Test invite revocation
   - Test invite acceptance (happy path)
   - Test invitation validation (expired, revoked, max uses)
   - Test error cases (invalid token, already member)

2. **Frontend Tests:**
   - Test invitation UI components 
   - Test invitation flow for logged in users
   - Test invitation flow for non-logged in users
   - Test expired/invalid invitation handling

## Security Considerations

1. Use cryptographically secure random tokens
2. Implement proper expiration handling
3. Add rate limiting to prevent invite abuse
4. Always verify teacher ownership before invite operations
5. Validate invite state before allowing acceptance
