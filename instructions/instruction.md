# IELTS Toolkit

## Application Description

IELTS Toolkit is an IELTS management and practice application with the following features:

### For Teachers
- **Test Management**:
  - Create tests for students in various categories (Grammar, Writing, and can be extended)
  - Edit created tests
  - Delete created tests
- **Category Management**:
  - Create new categories
  - Assign tests to categories
- **Test Types**:
  - Grammar tests
  - Reading comprehension tests
  - Can be extended to include more test types in the future

### For Students
- Access and take shared tests
- View results and assessments

### For Administrators
- Manage users (teachers and students)
- Disable accounts when necessary
- View and export summary reports

## Technical Requirements
- User-friendly interface, easy to use
- Clear security and permission system
- Scalability to add new test types and features

## Tech Stack

### Frontend
- **React**: JavaScript library for building user interfaces
- **Tailwind CSS**: Utility CSS framework for creating beautiful and responsive interfaces
- **shadcn/ui**: Component library based on Radix UI and Tailwind CSS, providing highly customizable interface components

### Backend
- **Node.js**: JavaScript runtime environment for server-side
- **NestJS**: Web framework for Node.js
- **PostgreSQL**: SQL database for data storage
- **Prisma**: Modern ORM with type-safety and effective migration capabilities

### Tools and Other Libraries
- **TypeScript**: Programming language extending JavaScript with static types
- **Axios**: HTTP client library for making API requests
- **JWT**: JSON Web Tokens for user authentication
- **React Query**: Library for state management and data fetching


