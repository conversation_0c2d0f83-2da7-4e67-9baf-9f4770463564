# Railway Deployment Troubleshooting

## "Project Token not found" Error

This error occurs when Railway CLI can't identify which project to deploy to. Here are the solutions:

### Solution 1: Use Railway Dashboard (Recommended)

Instead of using GitHub Actions initially, set up Railway through the web dashboard:

1. **Create Railway Account**: Go to https://railway.app and sign up

2. **Create New Project**: 
   - Click "New Project"
   - Select "Deploy from GitHub repo"
   - Connect your GitHub account
   - Select your repository

3. **Configure Services**:
   
   **For Backend (server-nest)**:
   - Root Directory: `server-nest`
   - Build Command: `npm ci && npm run prisma:generate && npm run build`
   - Start Command: `npm run start:prod`
   - Environment Variables:
     ```
     NODE_ENV=production
     PORT=8080
     DATABASE_URL=${{Postgres.DATABASE_URL}} (Railway will auto-populate)
     JWT_SECRET=your-secret-key
     ```

   **For Frontend (client)**:
   - Root Directory: `client`
   - Build Command: `npm ci && npm run build`
   - Start Command: `npx serve -s dist -l 8080`
   - Environment Variables:
     ```
     VITE_API_URL=https://your-backend-url.railway.app/api
     ```

4. **Add PostgreSQL Database**:
   - In your project, click "New Service"
   - Select "Database" → "PostgreSQL"
   - Railway will automatically create DATABASE_URL

5. **Deploy**:
   - Railway will automatically deploy when you push to main branch
   - No GitHub Actions needed!

### Solution 2: Fix GitHub Actions

If you want to use GitHub Actions, you need these secrets:

1. **Get Railway Token**:
   - Go to https://railway.app/account/tokens
   - Create new token
   - Add as `RAILWAY_TOKEN` in GitHub secrets

2. **Get Project IDs**:
   - Go to each Railway project
   - Settings → General → Project ID
   - Add as `RAILWAY_BACKEND_PROJECT_ID` and `RAILWAY_FRONTEND_PROJECT_ID`

3. **Required GitHub Secrets**:
   ```
   RAILWAY_TOKEN=your-account-token
   RAILWAY_BACKEND_PROJECT_ID=your-backend-project-id
   RAILWAY_FRONTEND_PROJECT_ID=your-frontend-project-id
   PAT_TOKEN=your-github-personal-access-token
   ```

### Solution 3: Manual CLI Setup

If you want to use CLI locally:

1. **Install Railway CLI**:
   ```bash
   npm install -g @railway/cli
   ```

2. **Login**:
   ```bash
   railway login
   ```

3. **Link Projects**:
   ```bash
   # In server-nest directory
   cd server-nest
   railway link [your-backend-project-id]
   
   # In client directory  
   cd ../client
   railway link [your-frontend-project-id]
   ```

4. **Deploy**:
   ```bash
   # Deploy backend
   cd server-nest
   railway up
   
   # Deploy frontend
   cd ../client
   railway up
   ```

## Recommended Approach

**Use Railway Dashboard (Solution 1)** - it's the easiest and most reliable method:

1. Connect GitHub repo to Railway
2. Configure services through web interface
3. Let Railway handle automatic deployments
4. No GitHub Actions or CLI setup needed

This eliminates all token and project ID issues while providing the same functionality.

## Cost Estimate

- PostgreSQL: $5/month
- 2 Services: $0-10/month (depending on usage)
- **Total: $5-15/month**

## Support

If you still have issues:
- Railway Discord: https://discord.gg/railway
- Railway Docs: https://docs.railway.app
- GitHub Issues: Create an issue in this repository
