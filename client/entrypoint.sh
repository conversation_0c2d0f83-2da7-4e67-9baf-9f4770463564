#!/bin/sh
set -e

# Print all environment variables for debugging
echo "Environment variables:"
env

# Get the PORT environment variable or default to 8080
PORT="${PORT:-8080}"
echo "Using PORT: $PORT"

# Get the backend URL from environment variable or use a default for Cloud Run
# The VITE_API_URL is set by Terraform in the Cloud Run configuration
BACKEND_URL="${VITE_API_URL:-https://ielts-server-967241671465.asia-southeast1.run.app/api}"
echo "Using BACKEND_URL: $BACKEND_URL"

# Replace the __PORT__ placeholder in the nginx.conf file
sed -i.bak "s/__PORT__/$PORT/g" /etc/nginx/conf.d/default.conf

# Replace the __BACKEND_URL__ placeholder in the nginx.conf file
# Use different delimiters to handle URLs with slashes
sed -i.bak "s|__BACKEND_URL__|$BACKEND_URL|g" /etc/nginx/conf.d/default.conf

# Debug - print the nginx configuration
echo "Nginx configuration:"
cat /etc/nginx/conf.d/default.conf

# Start Nginx
exec nginx -g "daemon off;"
