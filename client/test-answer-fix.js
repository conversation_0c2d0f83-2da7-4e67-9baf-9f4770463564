// <PERSON>ript to check and fix the answer sheet for test 14445e0a-0177-4e0b-af3a-f0c5c7413af8
const testId = '14445e0a-0177-4e0b-af3a-f0c5c7413af8';
const API_BASE_URL = 'http://localhost:8080/api';

// Function to fetch the current test
async function fetchTest() {
  try {
    console.log(`Fetching test with ID: ${testId}`);
    const response = await fetch(`${API_BASE_URL}/teacher/tests/${testId}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch test: ${response.status} ${response.statusText}`);
    }
    
    const testData = await response.json();
    console.log('Current test data:', testData);
    
    // Check if answerSheet exists and parse it if needed
    let answerSheet = testData.answerSheet || {};
    
    if (typeof answerSheet === 'string') {
      try {
        answerSheet = JSON.parse(answerSheet);
      } catch (e) {
        console.error('Error parsing answerSheet:', e);
        answerSheet = { multipleChoiceAnswers: [], textAnswers: [] };
      }
    }
    
    // Ensure the structure exists
    answerSheet.multipleChoiceAnswers = answerSheet.multipleChoiceAnswers || [];
    answerSheet.textAnswers = answerSheet.textAnswers || [];
    
    console.log('Current answer sheet:', answerSheet);
    
    return { testData, answerSheet };
  } catch (error) {
    console.error('Error fetching test:', error);
    return null;
  }
}

// Function to update the test with the fixed answer sheet
async function updateTest(testData, updatedAnswerSheet) {
  try {
    console.log('Preparing update with answer sheet:', updatedAnswerSheet);
    
    // Create update payload
    const updateData = {
      ...testData,
      answerSheet: updatedAnswerSheet
    };
    
    console.log('Sending update with data:', updateData);
    
    const response = await fetch(`${API_BASE_URL}/teacher/tests/${testId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    });
    
    if (!response.ok) {
      throw new Error(`Failed to update test: ${response.status} ${response.statusText}`);
    }
    
    const result = await response.json();
    console.log('Update successful:', result);
    return result;
  } catch (error) {
    console.error('Error updating test:', error);
    return null;
  }
}

// Main function to check and fix the test
async function checkAndFixTest() {
  // First, fetch the current test
  const fetchResult = await fetchTest();
  
  if (!fetchResult) {
    console.error('Unable to fetch test data. Fix aborted.');
    return;
  }
  
  const { testData, answerSheet } = fetchResult;
  
  // Check if question 3 already has an answer defined
  const q3Answer = answerSheet.multipleChoiceAnswers.find(a => a.questionNumber === 3);
  
  if (q3Answer) {
    console.log(`Question 3 already has an answer defined: ${q3Answer.answer}`);
    return;
  }
  
  // Add the correct answer for question 3
  const updatedAnswerSheet = {
    ...answerSheet,
    multipleChoiceAnswers: [
      ...answerSheet.multipleChoiceAnswers,
      { questionNumber: 3, answer: 'Option C' }
    ]
  };
  
  console.log('Updated answer sheet:', updatedAnswerSheet);
  
  // Update the test with the fixed answer sheet
  const updateResult = await updateTest(testData, updatedAnswerSheet);
  
  if (updateResult) {
    console.log('Test answer sheet fixed successfully!');
  } else {
    console.error('Failed to fix test answer sheet.');
  }
}

// Execute the script
checkAndFixTest(); 