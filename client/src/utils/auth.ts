/**
 * Get the authentication token from localStorage
 */
export const getToken = (): string => {
  return localStorage.getItem('token') || '';
};

/**
 * Set the authentication token in localStorage
 */
export const setToken = (token: string): void => {
  localStorage.setItem('token', token);
};

/**
 * Remove the authentication token from localStorage
 */
export const removeToken = (): void => {
  localStorage.removeItem('token');
};

/**
 * Check if the user is authenticated
 */
export const isAuthenticated = (): boolean => {
  return !!getToken();
}; 