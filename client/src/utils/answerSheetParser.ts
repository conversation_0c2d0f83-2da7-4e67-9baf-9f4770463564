import { AnswerOption, MultipleChoiceAnswer, TextAnswer } from '../types/test';

/**
 * Parses multiple choice answer text into an array of MultipleChoiceAnswer objects
 * @param text The text to parse in format like "1. A\n2. B\n3. C"
 * @returns Array of parsed MultipleChoiceAnswer objects
 */
export const parseMultipleChoiceAnswers = (text: string): {
  answers: MultipleChoiceAnswer[],
  errors: string[]
} => {
  const answers: MultipleChoiceAnswer[] = [];
  const errors: string[] = [];

  // Split the text into lines and process each line
  const lines = text.split('\n');

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Skip empty lines
    if (!line) continue;

    // Try different parsing strategies
    let questionNumber: number | null = null;
    let option: string | null = null;

    // Strategy 1: Standard format like "1. A", "1) A", "1 - A", etc.
    const standardMatch = line.match(/^(\d+)[\.\)\-\s]*\s*([A-Da-d])$/);
    if (standardMatch) {
      questionNumber = parseInt(standardMatch[1], 10);
      option = standardMatch[2].toUpperCase();
    }

    // Strategy 2: No separator like "1A", "2B", etc.
    if (!questionNumber || !option) {
      const noSeparatorMatch = line.match(/^(\d+)([A-Da-d])$/i);
      if (noSeparatorMatch) {
        questionNumber = parseInt(noSeparatorMatch[1], 10);
        option = noSeparatorMatch[2].toUpperCase();
      }
    }

    // Strategy 3: Answer with "option" prefix like "1. option A", "2. Option B", etc.
    if (!questionNumber || !option) {
      const optionPrefixMatch = line.match(/^(\d+)[\.\)\-\s]*\s*(?:option|answer|choice)?\s*([A-Da-d])$/i);
      if (optionPrefixMatch) {
        questionNumber = parseInt(optionPrefixMatch[1], 10);
        option = optionPrefixMatch[2].toUpperCase();
      }
    }

    // Strategy 4: Just try to extract any number and letter
    if (!questionNumber || !option) {
      // Look for a number followed by any text and then a letter A-D
      const looseMatch = line.match(/(\d+).*?([A-Da-d])/i);
      if (looseMatch) {
        questionNumber = parseInt(looseMatch[1], 10);
        option = looseMatch[2].toUpperCase();
      }
    }

    // If we found a question number and option, add it to the answers
    if (questionNumber && option) {
      // Validate the option
      if (!Object.values(AnswerOption).includes(option as AnswerOption)) {
        errors.push(`Line ${i + 1}: Invalid option "${option}". Must be A, B, C, or D.`);
        continue;
      }

      answers.push({
        questionNumber,
        correctOption: option as AnswerOption
      });
    } else {
      errors.push(`Line ${i + 1}: Could not parse answer. Expected format is "1. A" or similar.`);
    }
  }

  return { answers, errors };
};

/**
 * Parses fill-in-the-blank answer text into an array of TextAnswer objects
 * @param text The text to parse in format like "1. Answer\n2. Answer 2, Alt Answer"
 * @returns Array of parsed TextAnswer objects
 */
export const parseTextAnswers = (text: string): {
  answers: TextAnswer[],
  errors: string[]
} => {
  const answers: TextAnswer[] = [];
  const errors: string[] = [];

  // Split the text into lines and process each line
  const lines = text.split('\n');

  // Keep track of the last question number for handling continuation lines
  let lastQuestionNumber: number | null = null;
  let currentAnswers: string[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Skip empty lines
    if (!line) continue;

    // Try different parsing strategies
    let questionNumber: number | null = null;
    let answerText: string | null = null;

    // Strategy 1: Standard format like "1. Answer", "1) Answer", "1 - Answer", etc.
    const standardMatch = line.match(/^(\d+)[\.\)\-\s]*\s*(.+)$/);
    if (standardMatch) {
      questionNumber = parseInt(standardMatch[1], 10);
      answerText = standardMatch[2].trim();
    }

    // Strategy 2: No separator like "1Answer", "2Text", etc.
    if (!questionNumber || !answerText) {
      const noSeparatorMatch = line.match(/^(\d+)([^\d].+)$/);
      if (noSeparatorMatch) {
        questionNumber = parseInt(noSeparatorMatch[1], 10);
        answerText = noSeparatorMatch[2].trim();
      }
    }

    // Strategy 3: Just a number at the beginning (answer might be on the next line)
    if (!questionNumber && !answerText) {
      const justNumberMatch = line.match(/^(\d+)$/);
      if (justNumberMatch) {
        questionNumber = parseInt(justNumberMatch[1], 10);
        answerText = ""; // Empty for now, might be filled in from the next line
      }
    }

    // If we found a new question number, save the previous one if it exists
    if (questionNumber) {
      // Save the previous question if it exists
      if (lastQuestionNumber !== null && currentAnswers.length > 0) {
        answers.push({
          questionNumber: lastQuestionNumber,
          correctAnswers: currentAnswers,
          caseSensitive: false
        });
        currentAnswers = [];
      }

      lastQuestionNumber = questionNumber;

      // Process the answer text if it exists
      if (answerText) {
        // Split by commas for multiple acceptable answers
        const newAnswers = answerText.split(',').map(a => a.trim()).filter(a => a);
        currentAnswers = [...currentAnswers, ...newAnswers];
      }
    }
    // If no question number but we have text and a previous question, treat as continuation
    else if (lastQuestionNumber !== null) {
      // This line is probably a continuation of the previous answer
      const continuationAnswers = line.split(',').map(a => a.trim()).filter(a => a);
      currentAnswers = [...currentAnswers, ...continuationAnswers];
    }
    // Can't determine what this line is
    else {
      errors.push(`Line ${i + 1}: Could not parse answer. Expected format is "1. Answer" or similar.`);
    }
  }

  // Don't forget to add the last question
  if (lastQuestionNumber !== null && currentAnswers.length > 0) {
    answers.push({
      questionNumber: lastQuestionNumber,
      correctAnswers: currentAnswers,
      caseSensitive: false
    });
  }

  // Check for empty answers
  for (let i = 0; i < answers.length; i++) {
    if (answers[i].correctAnswers.length === 0) {
      errors.push(`Question ${answers[i].questionNumber}: No valid answer provided.`);
    }
  }

  return { answers, errors };
};
