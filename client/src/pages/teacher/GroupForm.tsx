import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { teacherGroupApi, CreateGroupDto, UpdateGroupDto } from '../../api/teacherGroupApi';
import { showNotification } from '../../components/ui/Notification';

const GroupForm: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const queryClient = useQueryClient();
  const isEditMode = Boolean(id);

  // Form state
  const [name, setName] = useState('');
  const [level, setLevel] = useState('');
  const [description, setDescription] = useState('');
  const [isActive, setIsActive] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch group data if in edit mode
  const { data: group, isLoading: isLoadingGroup } = useQuery({
    queryKey: ['group', id],
    queryFn: () => teacherGroupApi.getGroupById(id!),
    enabled: isEditMode,
  });

  // Set form values when group data is loaded
  useEffect(() => {
    if (group) {
      setName(group.name);
      setLevel(group.level);
      setDescription(group.description || '');
      setIsActive(group.isActive);
    }
  }, [group]);

  // Create group mutation
  const createGroupMutation = useMutation({
    mutationFn: teacherGroupApi.createGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teacher-groups'] });
      showNotification(t('teacher:groups.createSuccess'), 'success');
      navigate('/teacher/groups');
    },
    onError: (error: any) => {
      showNotification(error.response?.data?.message || t('common:errors.unexpected'), 'error');
    },
  });

  // Update group mutation
  const updateGroupMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateGroupDto }) =>
      teacherGroupApi.updateGroup(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teacher-groups'] });
      queryClient.invalidateQueries({ queryKey: ['group', id] });
      showNotification(t('teacher:groups.updateSuccess'), 'success');
      navigate('/teacher/groups');
    },
    onError: (error: any) => {
      showNotification(error.response?.data?.message || t('common:errors.unexpected'), 'error');
    },
  });

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = t('validation:required');
    }

    if (!level.trim()) {
      newErrors.level = t('validation:required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (isEditMode && id) {
      const updateData: UpdateGroupDto = {
        name,
        level,
        description: description || undefined,
        isActive,
      };

      updateGroupMutation.mutate({ id, data: updateData });
    } else {
      const createData: CreateGroupDto = {
        name,
        level,
        description: description || undefined,
      };

      createGroupMutation.mutate(createData);
    }
  };

  // Predefined levels
  const predefinedLevels = ['Beginner', 'Elementary', 'Intermediate', 'Upper Intermediate', 'Advanced', 'Proficient'];

  if (isEditMode && isLoadingGroup) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <h1 className="text-2xl font-bold mb-4 dark:text-white">{t('teacher:groups.editGroup')}</h1>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <h1 className="text-2xl font-bold mb-6 dark:text-white">
        {isEditMode ? t('teacher:groups.editGroup') : t('teacher:groups.createGroup')}
      </h1>

      <div className="bg-white dark:bg-slate-800 p-6 rounded-md shadow">
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('teacher:groups.name')} *
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className={`w-full px-3 py-2 border ${
                errors.name ? 'border-red-500 dark:border-red-700' : 'border-gray-300 dark:border-slate-600'
              } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-slate-700 dark:text-white`}
            />
            {errors.name && <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.name}</p>}
          </div>

          <div className="mb-4">
            <label htmlFor="level" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('teacher:groups.level')} *
            </label>
            <select
              id="level"
              value={level}
              onChange={(e) => setLevel(e.target.value)}
              className={`w-full px-3 py-2 border ${
                errors.level ? 'border-red-500 dark:border-red-700' : 'border-gray-300 dark:border-slate-600'
              } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-slate-700 dark:text-white`}
            >
              <option value="">{t('common:select')}</option>
              {predefinedLevels.map((lvl) => (
                <option key={lvl} value={lvl}>
                  {lvl}
                </option>
              ))}
            </select>
            {errors.level && <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.level}</p>}
          </div>

          <div className="mb-4">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('teacher:groups.description')}
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-slate-700 dark:text-white"
            />
          </div>

          {isEditMode && (
            <div className="mb-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={isActive}
                  onChange={(e) => setIsActive(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-700"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{t('teacher:groups.isActive')}</span>
              </label>
            </div>
          )}

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => navigate('/teacher/groups')}
              className="px-4 py-2 border border-gray-300 dark:border-slate-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:focus:ring-offset-slate-800"
            >
              {t('common:buttons.cancel')}
            </button>
            <button
              type="submit"
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:focus:ring-offset-slate-800"
              disabled={createGroupMutation.isPending || updateGroupMutation.isPending}
            >
              {(createGroupMutation.isPending || updateGroupMutation.isPending) ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {t('common:saving')}
                </span>
              ) : (
                t('common:buttons.save')
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default GroupForm;
