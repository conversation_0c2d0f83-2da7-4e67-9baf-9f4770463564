import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { teacherGroupApi, Group } from '../../api/teacherGroupApi';
import { showNotification } from '../../components/ui/Notification';

const Groups: React.FC = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState('');
  const [showInactive, setShowInactive] = useState(false);
  
  // Sorting and pagination states
  const [sortField, setSortField] = useState<'name' | 'level' | 'createdAt' | 'studentCount'>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10); // We're setting a fixed value for now

  // Toggle sort direction or change sort field
  const handleSort = (field: 'name' | 'level' | 'createdAt' | 'studentCount') => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Render sort indicator
  const renderSortIndicator = (field: 'name' | 'level' | 'createdAt' | 'studentCount') => {
    if (sortField !== field) return null;
    return (
      <span className="ml-1" aria-label={sortDirection === 'asc' ? t('common:sort.ascending', { defaultValue: 'sorted ascending' }) : t('common:sort.descending', { defaultValue: 'sorted descending' })}>
        {sortDirection === 'asc' ? '↑' : '↓'}
      </span>
    );
  };

  // Fetch groups
  const { data: groups, isLoading, error } = useQuery({
    queryKey: ['teacher-groups'],
    queryFn: teacherGroupApi.getAllGroups,
  });

  // Delete group mutation
  const deleteGroupMutation = useMutation({
    mutationFn: teacherGroupApi.deleteGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teacher-groups'] });
      showNotification(t('teacher:groups.deleteSuccess'), 'success');
    },
    onError: (error: any) => {
      showNotification(error.response?.data?.message || t('common:errors.unexpected'), 'error');
    },
  });

  // Filter groups
  const filteredGroups = groups?.filter((group) => {
    const matchesSearch = group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (group.description && group.description.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesLevel = levelFilter ? group.level === levelFilter : true;
    const matchesActive = showInactive ? true : group.isActive;
    return matchesSearch && matchesLevel && matchesActive;
  });

  // Sort groups
  const sortedGroups = filteredGroups ? [...filteredGroups].sort((a, b) => {
    if (sortField === 'name') {
      return sortDirection === 'asc' 
        ? a.name.localeCompare(b.name)
        : b.name.localeCompare(a.name);
    } 
    else if (sortField === 'level') {
      return sortDirection === 'asc'
        ? a.level.localeCompare(b.level)
        : b.level.localeCompare(a.level);
    }
    else if (sortField === 'createdAt') {
      return sortDirection === 'asc'
        ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
    else if (sortField === 'studentCount') {
      const countA = a.students?.length || 0;
      const countB = b.students?.length || 0;
      return sortDirection === 'asc' ? countA - countB : countB - countA;
    }
    return 0;
  }) : [];

  // Pagination
  const totalPages = sortedGroups ? Math.ceil(sortedGroups.length / itemsPerPage) : 0;
  const paginatedGroups = sortedGroups ? sortedGroups.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  ) : [];

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Get unique levels for filter
  const levels = groups ? [...new Set(groups.map(group => group.level))].sort() : [];

  const handleDeleteGroup = (id: string) => {
    if (window.confirm(t('teacher:groups.confirmDelete'))) {
      deleteGroupMutation.mutate(id);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <h1 className="text-2xl font-bold mb-4">{t('teacher:groups.title')}</h1>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <h1 className="text-2xl font-bold mb-4">{t('teacher:groups.title')}</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded dark:bg-red-900/30 dark:border-red-800 dark:text-red-400">
          <p>{t('common:errors.loadingFailed')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('teacher:groups.title')}</h1>
        <Link
          to="/teacher/groups/new"
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors dark:bg-blue-700 dark:hover:bg-blue-800"
        >
          {t('teacher:groups.createNew')}
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-md shadow mb-6 dark:bg-slate-800 dark:border dark:border-slate-700">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1 dark:text-gray-300">
              {t('common:buttons.search')}
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:border-slate-600 dark:text-white dark:focus:ring-blue-400"
              placeholder={t('teacher:groups.searchPlaceholder')}
            />
          </div>
          <div>
            <label htmlFor="level" className="block text-sm font-medium text-gray-700 mb-1 dark:text-gray-300">
              {t('teacher:groups.level')}
            </label>
            <select
              id="level"
              value={levelFilter}
              onChange={(e) => setLevelFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:border-slate-600 dark:text-white dark:focus:ring-blue-400"
            >
              <option value="">{t('common:all')}</option>
              {levels.map((level) => (
                <option key={level} value={level}>
                  {level}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-end">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showInactive}
                onChange={(e) => setShowInactive(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-700"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{t('teacher:groups.showInactive')}</span>
            </label>
          </div>
        </div>
      </div>

      {/* Groups List */}
      {filteredGroups && filteredGroups.length > 0 ? (
        <div className="bg-white rounded-md shadow overflow-hidden dark:bg-slate-800 dark:border dark:border-slate-700">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
            <thead className="bg-gray-50 dark:bg-slate-700">
              <tr>
                <th 
                  scope="col" 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-slate-600"
                  onClick={() => handleSort('name')}
                >
                  {t('teacher:groups.name')}
                  {renderSortIndicator('name')}
                </th>
                <th 
                  scope="col" 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-slate-600"
                  onClick={() => handleSort('level')}
                >
                  {t('teacher:groups.level')}
                  {renderSortIndicator('level')}
                </th>
                <th 
                  scope="col" 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-slate-600"
                  onClick={() => handleSort('studentCount')}
                >
                  {t('teacher:groups.students')}
                  {renderSortIndicator('studentCount')}
                </th>
                <th 
                  scope="col" 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-slate-600"
                  onClick={() => handleSort('createdAt')}
                >
                  {t('teacher:groups.createdAt', { defaultValue: 'Created Date' })}
                  {renderSortIndicator('createdAt')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  {t('teacher:groups.status')}
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('common:actionColumn')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200 dark:bg-slate-800 dark:divide-slate-700">
              {paginatedGroups.map((group) => (
                <tr key={group.id} className={!group.isActive ? 'bg-gray-50 dark:bg-slate-700/50' : 'dark:hover:bg-slate-700/30'}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">{group.name}</div>
                    {group.description && (
                      <div className="text-sm text-gray-500 truncate max-w-xs dark:text-gray-400">{group.description}</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">{group.level}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">{group.students?.length || 0}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">{new Date(group.createdAt).toLocaleDateString(navigator.language || 'en')}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        group.isActive
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                      }`}
                    >
                      {group.isActive ? t('common:status.active') : t('common:status.inactive')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link
                      to={`/teacher/groups/${group.id}`}
                      className="text-blue-600 hover:text-blue-900 mr-4 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      {t('common:actions.view')}
                    </Link>
                    <Link
                      to={`/teacher/groups/${group.id}/edit`}
                      className="text-indigo-600 hover:text-indigo-900 mr-4 dark:text-indigo-400 dark:hover:text-indigo-300"
                    >
                      {t('common:buttons.edit')}
                    </Link>
                    <button
                      onClick={() => handleDeleteGroup(group.id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                    >
                      {t('common:buttons.delete')}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 bg-white border-t border-gray-200 flex items-center justify-between dark:bg-slate-800 dark:border-slate-700">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {t('common:pagination.showing', { defaultValue: 'Showing' })} <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span> {t('common:pagination.to', { defaultValue: 'to' })}{' '}
                  <span className="font-medium">
                    {Math.min(currentPage * itemsPerPage, filteredGroups ? filteredGroups.length : 0)}
                  </span>{' '}
                  {t('common:pagination.of', { defaultValue: 'of' })} <span className="font-medium">{filteredGroups ? filteredGroups.length : 0}</span>{' '}
                  {t('common:pagination.results', { defaultValue: 'results' })}
                </p>
              </div>
              <div>
                <nav className="inline-flex rounded-md shadow-sm -space-x-px dark:shadow-slate-900/30" aria-label={t('common:pagination.navigation', { defaultValue: 'Pagination' })}>
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium dark:bg-slate-800 dark:border-slate-600 ${
                      currentPage === 1
                        ? 'text-gray-300 cursor-not-allowed dark:text-gray-500'
                        : 'text-gray-500 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-slate-700'
                    }`}
                  >
                    <span className="sr-only">{t('common:pagination.previous')}</span>
                    &laquo;
                  </button>
                  {Array.from({ length: totalPages }).map((_, index) => (
                    <button
                      key={index}
                      onClick={() => handlePageChange(index + 1)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        currentPage === index + 1
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600 dark:bg-blue-900/30 dark:border-blue-500 dark:text-blue-400'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50 dark:bg-slate-800 dark:border-slate-600 dark:text-gray-300 dark:hover:bg-slate-700'
                      }`}
                    >
                      {index + 1}
                    </button>
                  ))}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium dark:bg-slate-800 dark:border-slate-600 ${
                      currentPage === totalPages
                        ? 'text-gray-300 cursor-not-allowed dark:text-gray-500'
                        : 'text-gray-500 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-slate-700'
                    }`}
                  >
                    <span className="sr-only">{t('common:pagination.next')}</span>
                    &raquo;
                  </button>
                </nav>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="bg-yellow-50 border border-yellow-100 p-4 rounded-md text-center text-yellow-700 dark:bg-yellow-900/20 dark:border-yellow-900/30 dark:text-yellow-300">
          <p className="text-gray-500 dark:text-gray-400 mb-4">{t('teacher:groups.noGroups')}</p>
          <Link
            to="/teacher/groups/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800 dark:ring-offset-slate-800"
          >
            {t('teacher:groups.createFirst')}
          </Link>
        </div>
      )}
    </div>
  );
};

export default Groups;
