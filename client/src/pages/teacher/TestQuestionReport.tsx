import React from 'react';
import { useTranslation } from 'react-i18next';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { teacherGroupApi } from '../../api/teacherGroupApi';
import TestQuestionAnalysis from '../../components/teacher/TestQuestionAnalysis';
import { QuestionType } from '../../types/teacher-group';

const TestQuestionReport: React.FC = () => {
  const { t } = useTranslation();
  const { groupId, testId } = useParams<{ groupId: string; testId: string }>();

  // Fetch group data
  const { data: group, isLoading: isLoadingGroup } = useQuery({
    queryKey: ['group', groupId],
    queryFn: () => teacherGroupApi.getGroupById(groupId!),
    enabled: Boolean(groupId),
  });

  // Fetch test question analysis
  const { data: analysis, isLoading: isLoadingAnalysis } = useQuery({
    queryKey: ['test-question-analysis', groupId, testId],
    queryFn: () => teacherGroupApi.getTestQuestionAnalysis(groupId!, testId!),
    enabled: Boolean(groupId) && Boolean(testId),
  });

  if (isLoadingGroup || isLoadingAnalysis) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-4 xl:max-w-7xl py-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
        </div>
      </div>
    );
  }

  if (!group || !analysis) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-4 xl:max-w-7xl py-4">
        <div className="bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-900 text-red-700 dark:text-red-400 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">{t('common:error')}:</strong>
          <span className="block sm:inline"> {t('teacher:questionAnalysis.dataNotFound')}</span>
        </div>
      </div>
    );
  }

  // Separate questions by type for better organization
  const multipleChoiceQuestions = analysis.questions.filter((q: any) => q.questionType === QuestionType.MULTIPLE_CHOICE);
  const fillInBlankQuestions = analysis.questions.filter((q: any) => q.questionType === QuestionType.FILL_IN_THE_BLANK);

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-4 xl:max-w-7xl py-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold dark:text-white">
            {analysis.testTitle} - {t('teacher:questionAnalysis.title')}
          </h1>
          <div className="text-gray-600 dark:text-gray-300 mt-1">
            {t('teacher:groups.group')}: {group.name}
          </div>
        </div>
        <Link
          to={`/teacher/groups/${groupId}/results`}
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          {t('teacher:groups.backToResults')}
        </Link>
      </div>

      {/* Test summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-2 dark:text-white">{t('teacher:groups.studentCount')}</h3>
          <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{analysis.totalStudents}</p>
        </div>
        <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-2 dark:text-white">{t('teacher:questionAnalysis.completedCount')}</h3>
          <p className="text-3xl font-bold text-green-600 dark:text-green-400">{analysis.completedCount}</p>
        </div>
        <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-2 dark:text-white">{t('teacher:groups.averageScore')}</h3>
          <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">{analysis.averageScore}{t('teacher:questionAnalysis.percentSymbol')}</p>
        </div>
        <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-2 dark:text-white">{t('teacher:questionAnalysis.completionRate')}</h3>
          <p className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">
            {analysis.totalStudents > 0 ? Math.round((analysis.completedCount / analysis.totalStudents) * 100) : 0}{t('teacher:questionAnalysis.percentSymbol')}
          </p>
        </div>
      </div>

      {/* Most problematic questions */}
      {analysis.questions.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 dark:text-white">
            {t('teacher:questionAnalysis.mostProblematicQuestions')}
          </h2>
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-gray-50 dark:bg-slate-700">
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('teacher:questionAnalysis.question')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('teacher:questionAnalysis.type')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('teacher:questionAnalysis.incorrectRate')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                  {[...analysis.questions]
                    .sort((a, b) => {
                      const aIncorrectRate = a.incorrectCount / (a.incorrectCount + a.correctCount + a.skippedCount);
                      const bIncorrectRate = b.incorrectCount / (b.incorrectCount + b.correctCount + b.skippedCount);
                      return bIncorrectRate - aIncorrectRate;
                    })
                    .slice(0, 5)
                    .map((question) => {
                      const totalAnswered = question.correctCount + question.incorrectCount + question.skippedCount;
                      const incorrectRate = totalAnswered > 0 ? Math.round((question.incorrectCount / totalAnswered) * 100) : 0;

                      return (
                        <tr key={question.questionNumber} className="hover:bg-gray-50 dark:hover:bg-slate-700">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {question.questionText.startsWith('Question')
                              ? t('teacher:questionAnalysis.questionPrefix', { number: question.questionNumber })
                              : question.questionText}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                            {question.questionType === QuestionType.MULTIPLE_CHOICE
                              ? t('teacher:questionAnalysis.multipleChoice')
                              : t('teacher:questionAnalysis.fillInBlank')
                            }
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mr-2 max-w-xs">
                                <div
                                  className="bg-red-500 dark:bg-red-600 h-2.5 rounded-full"
                                  style={{ width: `${incorrectRate}%` }}
                                ></div>
                              </div>
                              <span className="text-sm text-gray-700 dark:text-gray-300">
                                {incorrectRate}{t('teacher:questionAnalysis.percentSymbol')}
                              </span>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Multiple choice questions */}
      {multipleChoiceQuestions.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 dark:text-white">
            {t('teacher:questionAnalysis.multipleChoiceQuestions')}
          </h2>
          <div className="max-h-[500px] overflow-y-auto pr-2 scrollbar scrollbar-thin scrollbar-thumb-gray-400 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-200 dark:scrollbar-track-gray-800 rounded-lg shadow-inner bg-gray-50 dark:bg-slate-900/50 p-2">
            {multipleChoiceQuestions.map((question: any) => (
              <TestQuestionAnalysis
                key={question.questionNumber}
                question={question}
                totalStudents={analysis.totalStudents}
              />
            ))}
          </div>
        </div>
      )}

      {/* Fill in the blank questions */}
      {fillInBlankQuestions.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 dark:text-white">
            {t('teacher:questionAnalysis.fillInBlankQuestions')}
          </h2>
          <div className="max-h-[500px] overflow-y-auto pr-2 scrollbar scrollbar-thin scrollbar-thumb-gray-400 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-200 dark:scrollbar-track-gray-800 rounded-lg shadow-inner bg-gray-50 dark:bg-slate-900/50 p-2">
            {fillInBlankQuestions.map((question: any) => (
              <TestQuestionAnalysis
                key={question.questionNumber}
                question={question}
                totalStudents={analysis.totalStudents}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default TestQuestionReport;
