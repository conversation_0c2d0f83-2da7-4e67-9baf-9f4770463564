import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { teacherGroupApi } from '../../api/teacherGroupApi';

const GroupResults: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const [selectedStudentId, setSelectedStudentId] = useState<string | null>(null);

  // Fetch group data
  const { data: group, isLoading: isLoadingGroup } = useQuery({
    queryKey: ['group', id],
    queryFn: () => teacherGroupApi.getGroupById(id!),
    enabled: Boolean(id),
  });

  // Fetch group results
  const { data: groupResults, isLoading: isLoadingResults } = useQuery({
    queryKey: ['group-results', id],
    queryFn: () => teacherGroupApi.getGroupResults(id!),
    enabled: <PERSON><PERSON>an(id),
  });

  // Fetch student results if a student is selected
  const { data: studentResults, isLoading: isLoadingStudentResults } = useQuery({
    queryKey: ['student-results', id, selectedStudentId],
    queryFn: () => teacherGroupApi.getStudentResults(id!, selectedStudentId!),
    enabled: Boolean(id) && Boolean(selectedStudentId),
  });

  if (isLoadingGroup || isLoadingResults) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-4 xl:max-w-7xl py-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
        </div>
      </div>
    );
  }

  if (!group || !groupResults) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-4 xl:max-w-7xl py-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded dark:bg-red-900/30 dark:border-red-800 dark:text-red-400">
          <p>{t('teacher:groups.groupNotFound')}</p>
        </div>
        <div className="mt-4">
          <Link
            to="/teacher/groups"
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            {t('teacher:groups.backToGroups')}
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-4 xl:max-w-7xl py-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold dark:text-white">{group.name} - {t('teacher:groups.results')}</h1>
          <div className="text-gray-600 dark:text-gray-300 mt-1">{t('teacher:groups.level')}: {group.level}</div>
        </div>
        <Link
          to={`/teacher/groups/${id}`}
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          {t('teacher:groups.backToGroup')}
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-2 dark:text-white">{t('teacher:groups.studentCount')}</h3>
          <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{groupResults.studentCount}</p>
        </div>
        <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-2 dark:text-white">{t('teacher:groups.averageScore')}</h3>
          <p className="text-3xl font-bold text-green-600 dark:text-green-400">{groupResults.averageScore}%</p>
        </div>
        <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-2 dark:text-white">{t('teacher:groups.completionRate')}</h3>
          <p className="text-3xl font-bold text-indigo-600 dark:text-indigo-400">{groupResults.completionRate}%</p>
        </div>
      </div>

      {/* Tests assigned to the group */}
      <div className="mb-6">
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow overflow-hidden">
          <div className="px-4 py-5 sm:px-6 bg-gray-50 dark:bg-slate-700 flex justify-between items-center">
            <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">
              {t('teacher:groups.assignedTests')}
            </h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full divide-y divide-gray-200 dark:divide-slate-600">
              <thead className="bg-gray-50 dark:bg-slate-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t('teacher:groups.testTitle')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t('teacher:groups.type')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t('teacher:groups.completionRate')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t('common:buttons.actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-600">
                {group.assignments && group.assignments.length > 0 ? (
                  group.assignments.map((assignment) => (
                    <tr key={assignment.id} className="hover:bg-gray-50 dark:hover:bg-slate-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">{assignment.test.title}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {t('teacher:groups.dueDate')}: {assignment.dueDate
                            ? new Date(assignment.dueDate).toLocaleDateString()
                            : '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                          {assignment.test.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                          <div
                            className="bg-green-500 dark:bg-green-600 h-2.5 rounded-full"
                            style={{ width: t('teacher:groups.placeholderCompletionRate') }} // This would be calculated from actual data
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {t('teacher:groups.placeholderCompletionRate')} {/* This would be calculated from actual data */}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <Link
                          to={`/teacher/groups/${id}/tests/${assignment.test.id}/results`}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          {t('teacher:groups.viewAnalysis')}
                        </Link>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                      {t('teacher:groups.noTestsAssigned')}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow overflow-hidden">
          <div className="px-4 py-5 sm:px-6 bg-gray-50 dark:bg-slate-700">
            <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">
              {t('teacher:groups.studentPerformance')}
            </h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full divide-y divide-gray-200 dark:divide-slate-600">
              <thead className="bg-gray-50 dark:bg-slate-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t('teacher:groups.name')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t('teacher:groups.testsCompleted')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t('teacher:groups.averageScore')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t('common:buttons.actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-600">
                {groupResults.students.map((student) => (
                  <tr key={student.id} className={selectedStudentId === student.id ? 'bg-blue-50 dark:bg-blue-900/20' : 'hover:bg-gray-50 dark:hover:bg-slate-700'}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">{student.name}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">{student.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">{student.testsCompleted}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">{student.averageScore}%</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => setSelectedStudentId(student.id === selectedStudentId ? null : student.id)}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        {student.id === selectedStudentId ? t('common:hide') : t('common:view')}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {selectedStudentId && (
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow overflow-hidden">
            <div className="px-4 py-5 sm:px-6 bg-gray-50 dark:bg-slate-700">
              <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                {t('teacher:groups.studentDetails')}
              </h3>
            </div>
            {isLoadingStudentResults ? (
              <div className="p-6 flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
              </div>
            ) : studentResults ? (
              <div className="p-4">
                <div className="mb-4">
                  <h4 className="text-lg font-semibold dark:text-white">{studentResults.studentName}</h4>
                  <p className="text-gray-600 dark:text-gray-400">{studentResults.email}</p>
                </div>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="bg-gray-50 dark:bg-slate-700 p-3 rounded">
                    <div className="text-sm text-gray-500 dark:text-gray-400">{t('teacher:groups.testsAssigned')}</div>
                    <div className="text-lg font-semibold dark:text-white">{studentResults.testsAssigned}</div>
                  </div>
                  <div className="bg-gray-50 dark:bg-slate-700 p-3 rounded">
                    <div className="text-sm text-gray-500 dark:text-gray-400">{t('teacher:groups.testsCompleted')}</div>
                    <div className="text-lg font-semibold dark:text-white">{studentResults.testsCompleted}</div>
                  </div>
                  <div className="bg-gray-50 dark:bg-slate-700 p-3 rounded">
                    <div className="text-sm text-gray-500 dark:text-gray-400">{t('teacher:groups.averageScore')}</div>
                    <div className="text-lg font-semibold dark:text-white">{studentResults.averageScore}%</div>
                  </div>
                  <div className="bg-gray-50 dark:bg-slate-700 p-3 rounded">
                    <div className="text-sm text-gray-500 dark:text-gray-400">{t('teacher:groups.comparisonToGroup')}</div>
                    <div className="text-lg font-semibold dark:text-white">
                      {studentResults.averageScore > groupResults.averageScore
                        ? `+${(studentResults.averageScore - groupResults.averageScore).toFixed(1)}%`
                        : `${(studentResults.averageScore - groupResults.averageScore).toFixed(1)}%`}
                    </div>
                  </div>
                </div>
                <h5 className="font-semibold mb-2 dark:text-white">{t('teacher:groups.testResults')}</h5>
                {studentResults.testResults.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="w-full divide-y divide-gray-200 dark:divide-slate-600">
                      <thead className="bg-gray-50 dark:bg-slate-700">
                        <tr>
                          <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            {t('teacher:groups.testTitle')}
                          </th>
                          <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            {t('teacher:groups.status')}
                          </th>
                          <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            {t('teacher:groups.score')}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-600">
                        {studentResults.testResults.map((result) => (
                          <tr key={result.testId} className="hover:bg-gray-50 dark:hover:bg-slate-700">
                            <td className="px-4 py-2 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900 dark:text-white">{result.testTitle}</div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {t('teacher:groups.dueDate')}: {result.dueDate
                                  ? new Date(result.dueDate).toLocaleDateString()
                                  : '-'}
                              </div>
                            </td>
                            <td className="px-4 py-2 whitespace-nowrap">
                              <span
                                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  result.completed
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                                    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                                }`}
                              >
                                {result.completed ? t('teacher:groups.completed') : t('teacher:groups.pending')}
                              </span>
                            </td>
                            <td className="px-4 py-2 whitespace-nowrap">
                              <div className="text-sm text-gray-900 dark:text-white">
                                {result.completed ? `${result.score}%` : '-'}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-gray-500 dark:text-gray-400 text-center py-4">
                    {t('teacher:groups.noTestResults')}
                  </div>
                )}
              </div>
            ) : (
              <div className="p-6 text-center text-gray-500 dark:text-gray-400">
                {t('teacher:groups.failedToLoadStudentResults')}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default GroupResults;
