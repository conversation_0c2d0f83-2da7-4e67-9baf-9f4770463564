import React, { useState } from 'react';
import ConfirmModal from '../../components/ui/ConfirmModal';
import { useTranslation } from 'react-i18next';
import { useParams, Link } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { teacherGroupApi } from '../../api/teacherGroupApi';
import { showNotification } from '../../components/ui/Notification';

// We need to extend the teacherGroupApi with a new method to get student details and group memberships
// This would be implemented on the backend, but for now we'll simulate it

const StudentProfile: React.FC = () => {
  const { t } = useTranslation();
  const { studentId } = useParams<{ studentId: string }>();
  const queryClient = useQueryClient();
  
  const [targetGroupId, setTargetGroupId] = useState('');
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [groupToRemove, setGroupToRemove] = useState<string | null>(null);

  // Fetch student data
  const { data: student, isLoading: isLoadingStudent } = useQuery({
    queryKey: ['student', studentId],
    queryFn: async () => {
      // This would be a real API call in production
      // For now, we'll simulate it
      return {
        id: studentId,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        joinedAt: new Date().toISOString(),
      };
    },
    enabled: Boolean(studentId),
  });

  // Fetch all groups where the student is a member
  const { data: studentGroups, isLoading: isLoadingGroups } = useQuery({
    queryKey: ['student-groups', studentId],
    queryFn: async () => {
      // This would be a real API call in production
      // For now, we'll simulate it
      return [
        {
          id: 'group1',
          name: 'Intermediate English',
          level: 'Intermediate',
          joinedAt: new Date().toISOString(),
          studentCount: 15
        },
        {
          id: 'group2',
          name: 'Advanced Writing',
          level: 'Advanced',
          joinedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          studentCount: 8
        }
      ];
    },
    enabled: Boolean(studentId),
  });

  // Fetch all groups where the student is NOT a member (for moving functionality)
  const { data: availableGroups } = useQuery({
    queryKey: ['available-groups', studentId],
    queryFn: async () => {
      // This would be a real API call in production that gets groups where student is not a member
      // For now, we'll simulate it
      return [
        {
          id: 'group3',
          name: 'Beginning Listening',
          level: 'Beginner',
          studentCount: 12
        },
        {
          id: 'group4',
          name: 'Speaking Practice',
          level: 'Intermediate',
          studentCount: 10
        }
      ];
    },
    enabled: Boolean(studentId),
  });

  // Mutation to remove student from a group
  const removeFromGroupMutation = useMutation({
    mutationFn: (groupId: string) => teacherGroupApi.removeStudentFromGroup(groupId, studentId!),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['student-groups', studentId] });
      queryClient.invalidateQueries({ queryKey: ['available-groups', studentId] });
      showNotification(t('teacher:students.removedFromGroup'), 'success');
    },
    onError: (error: any) => {
      showNotification(error.response?.data?.message || t('common:errors.unexpected'), 'error');
    },
  });

  // Mutation to add student to a group
  const addToGroupMutation = useMutation({
    mutationFn: (groupId: string) => 
      teacherGroupApi.addStudentsToGroup(groupId, { studentIds: [studentId!] }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['student-groups', studentId] });
      queryClient.invalidateQueries({ queryKey: ['available-groups', studentId] });
      setTargetGroupId('');
      showNotification(t('teacher:students.addedToGroup'), 'success');
    },
    onError: (error: any) => {
      showNotification(error.response?.data?.message || t('common:errors.unexpected'), 'error');
    },
  });

  // Handle removing student from a group
  const handleRemoveFromGroup = (groupId: string) => {
    setGroupToRemove(groupId);
    setConfirmModalOpen(true);
  };

  // Confirm and execute the removal
  const confirmRemoveFromGroup = () => {
    if (groupToRemove) {
      removeFromGroupMutation.mutate(groupToRemove);
      setConfirmModalOpen(false);
      setGroupToRemove(null);
    }
  };

  // Cancel the removal
  const cancelRemoveFromGroup = () => {
    setConfirmModalOpen(false);
    setGroupToRemove(null);
  };

  // Handle adding student to a new group
  const handleAddToGroup = () => {
    if (!targetGroupId) {
      showNotification(t('teacher:students.selectGroupFirst'), 'error');
      return;
    }
    addToGroupMutation.mutate(targetGroupId);
  };

  if (isLoadingStudent || isLoadingGroups) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
        </div>
      </div>
    );
  }

  if (!student) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded dark:bg-red-900/30 dark:border-red-800 dark:text-red-400">
          <p>{t('teacher:students.studentNotFound')}</p>
        </div>
        <div className="mt-4">
          <Link
            to="/teacher/groups"
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            {t('teacher:groups.backToGroups')}
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold dark:text-white">{student.firstName} {student.lastName}</h1>
          <div className="text-gray-600 dark:text-gray-300 mt-1">{student.email}</div>
        </div>
        <Link
          to="/teacher/groups"
          className="text-blue-600 hover:text-blue-800"
        >
          {t('teacher:groups.backToGroups')}
        </Link>
      </div>

      {/* Student Group Memberships */}
      <div className="bg-white dark:bg-slate-800 rounded-md shadow overflow-hidden mb-6">
        <div className="px-4 py-5 sm:px-6 bg-gray-50 dark:bg-slate-700 flex justify-between items-center">
          <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">
            {t('teacher:students.groupMemberships')} ({studentGroups?.length || 0})
          </h3>
        </div>
        
        {studentGroups && studentGroups.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-600">
              <thead className="bg-gray-50 dark:bg-slate-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('teacher:groups.name')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('teacher:groups.level')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('teacher:students.joinedAt')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('teacher:groups.students')}
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t('common:buttons.actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                {studentGroups.map((group) => (
                  <tr key={group.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        <Link to={`/teacher/groups/${group.id}`} className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                          {group.name}
                        </Link>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500 dark:text-gray-400">{group.level}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {new Date(group.joinedAt).toLocaleDateString(navigator.language || 'en')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{group.studentCount}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleRemoveFromGroup(group.id)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        disabled={removeFromGroupMutation.isPending}
                      >
                        {t('common:buttons.remove')}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="p-6 text-center text-gray-500 dark:text-gray-400">
            {t('teacher:students.noGroupMemberships')}
          </div>
        )}
      </div>

      {/* Add to Group Section */}
      {availableGroups && availableGroups.length > 0 && (
        <div className="bg-white dark:bg-slate-800 rounded-md shadow p-6 mb-6">
          <h3 className="text-lg font-medium mb-4 dark:text-white">{t('teacher:students.addToGroup')}</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="col-span-2">
              <select
                value={targetGroupId}
                onChange={(e) => setTargetGroupId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-white"
              >
                <option value="">{t('teacher:students.selectGroup')}</option>
                {availableGroups.map((group) => (
                  <option key={group.id} value={group.id}>
                    {group.name} ({group.level}) - {group.studentCount} {t('teacher:groups.students').toLowerCase()}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <button
                onClick={handleAddToGroup}
                disabled={!targetGroupId || addToGroupMutation.isPending}
                className="w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300 dark:disabled:bg-blue-900 disabled:cursor-not-allowed"
              >
                {addToGroupMutation.isPending ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t('common:adding')}
                  </span>
                ) : (
                  t('teacher:students.addToSelectedGroup')
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Test Results (To be implemented in future) */}
      <div className="bg-white dark:bg-slate-800 rounded-md shadow p-6">
        <h3 className="text-lg font-medium mb-4 dark:text-white">{t('teacher:students.testResults')}</h3>
        <p className="text-gray-500 dark:text-gray-400 text-center py-4">
          {t('teacher:students.testResultsComingSoon')}
        </p>
      </div>
      
      {/* Confirmation Modal for removing student from group */}
      <ConfirmModal
        isOpen={confirmModalOpen}
        title={t('teacher:students.removeFromGroup')}
        message={t('teacher:students.confirmRemoveFromGroup')}
        confirmLabel={t('common:buttons.remove')}
        cancelLabel={t('common:buttons.cancel')}
        onConfirm={confirmRemoveFromGroup}
        onCancel={cancelRemoveFromGroup}
        type="danger"
      />
    </div>
  );
};

export default StudentProfile;
