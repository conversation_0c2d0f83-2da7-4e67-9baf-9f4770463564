import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { teacherGroupApi } from '../../api/teacherGroupApi';
import { showNotification } from '../../components/ui/Notification';
import { testsApi } from '../../api/testsApi';
import GroupInviteModal from '../../components/teacher/GroupInviteModal';
import InviteLinkDisplay from '../../components/teacher/InviteLinkDisplay';
import BatchAssignmentModal from '../../components/teacher/BatchAssignmentModal';
import TestAssignmentCalendar from '../../components/teacher/TestAssignmentCalendar';

// Define interfaces for the data types
interface Student {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

interface Test {
  id: string;
  title: string;
  type: string;
  difficulty: string;
}

interface StudentGroup {
  id: string;
  studentId: string;
  joinedAt: string;
  student?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

interface TestAssignment {
  id: string;
  testId: string;
  dueDate?: string;
  test?: {
    id: string;
    title: string;
    type: string;
    difficulty: string;
  };
}

interface Group {
  id: string;
  name: string;
  level: string;
  description?: string;
  isActive: boolean;
  students: StudentGroup[];
  assignments: TestAssignment[];
}

// Use the types from the API directly
import type {
  GroupInvite as ApiGroupInvite,
  CreateGroupInviteDto as ApiCreateGroupInviteDto,
  AddStudentsDto as ApiAddStudentsDto,
  AssignTestDto as ApiAssignTestDto
} from '../../api/teacherGroupApi';

// Local interface that extends the API interface
interface GroupInvite extends ApiGroupInvite {
  // Add any additional properties needed for the UI
  code?: string;
  url?: string;
}

interface AddStudentsDto extends ApiAddStudentsDto {
  // Match the API interface
}

interface AssignTestDto extends ApiAssignTestDto {
  // Match the API interface
}

interface CreateGroupInviteDto extends ApiCreateGroupInviteDto {
  // Match the API interface
}

const GroupDetail: React.FC = () => {
  const { t } = useTranslation(['common', 'teacher']);
  const { id } = useParams<{ id: string }>();
  const queryClient = useQueryClient();

  const [selectedStudentIds, setSelectedStudentIds] = useState<string[]>([]);
  const [selectedTestId, setSelectedTestId] = useState('');
  const [dueDate, setDueDate] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'students' | 'tests'>('students');
  const [showBatchAssignModal, setShowBatchAssignModal] = useState(false);
  // Calendar view state management will be implemented in future updates
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list');

  // Invitation link states
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showInviteLinkDisplay, setShowInviteLinkDisplay] = useState(false);
  const [currentInviteLink, setCurrentInviteLink] = useState<GroupInvite | null>(null);

  // Fetch group data
  const { data: group, isLoading, refetch: refetchGroup } = useQuery({
    queryKey: ['group', id],
    queryFn: () => teacherGroupApi.getGroupById(id!),
    enabled: Boolean(id),
  });

  // Fetch all students for adding to group
  const { data: allStudents } = useQuery({
    queryKey: ['all-students'],
    queryFn: teacherGroupApi.getAllStudents,
  });

  // Fetch all tests for assigning to group
  const { data: allTests } = useQuery({
    queryKey: ['all-tests'],
    queryFn: testsApi.getAll,
  });

  // Add students mutation
  const addStudentsMutation = useMutation({
    mutationFn: (data: AddStudentsDto) => teacherGroupApi.addStudentsToGroup(id!, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['group', id] });
      setSelectedStudentIds([]);
      showNotification(t('teacher:groups.studentsAdded'), 'success');
    },
    onError: (error: any) => {
      showNotification(error.response?.data?.message || t('common:errors.unexpected'), 'error');
    },
  });

  // Remove student mutation
  const removeStudentMutation = useMutation({
    mutationFn: (studentId: string) => teacherGroupApi.removeStudentFromGroup(id!, studentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['group', id] });
      showNotification(t('teacher:groups.studentRemoved'), 'success');
    },
    onError: (error: any) => {
      showNotification(error.response?.data?.message || t('common:errors.unexpected'), 'error');
    },
  });

  // Assign test mutation
  const assignTestMutation = useMutation({
    mutationFn: (data: AssignTestDto) => teacherGroupApi.assignTestToGroup(id!, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['group', id] });
      setSelectedTestId('');
      setDueDate('');
      showNotification(t('teacher:groups.testAssigned'), 'success');
    },
    onError: (error: any) => {
      showNotification(error.response?.data?.message || t('common:errors.unexpected'), 'error');
    },
  });

  // Batch assign tests mutation
  const batchAssignTestsMutation = useMutation({
    mutationFn: async (dataArray: AssignTestDto[]) => {
      // Process each assignment sequentially to avoid race conditions
      for (const data of dataArray) {
        await teacherGroupApi.assignTestToGroup(id!, data);
      }
      return dataArray.length; // Return number of successful assignments
    },
    onSuccess: (count) => {
      queryClient.invalidateQueries({ queryKey: ['group', id] });
      setShowBatchAssignModal(false);
      showNotification(t('teacher:tests.batchAssignSuccess', { count }), 'success');
    },
    onError: (error: any) => {
      showNotification(error.response?.data?.message || t('common:errors.unexpected'), 'error');
    },
  });

  // Remove test mutation
  const removeTestMutation = useMutation({
    mutationFn: (testId: string) => teacherGroupApi.removeTestFromGroup(id!, testId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['group', id] });
      showNotification(t('teacher:groups.testRemoved'), 'success');
    },
    onError: (error: any) => {
      showNotification(error.response?.data?.message || t('common:errors.unexpected'), 'error');
    },
  });

  // Create invitation link mutation
  const createInviteMutation = useMutation({
    mutationFn: (data: CreateGroupInviteDto) => teacherGroupApi.createGroupInvite(id!, data),
    onSuccess: (data) => {
      setCurrentInviteLink(data);
      setShowInviteModal(false);
      setShowInviteLinkDisplay(true);
      showNotification(t('teacher:groups.inviteLinkGenerated'), 'success');
      queryClient.invalidateQueries({ queryKey: ['group', id] });
      refetchGroup();
    },
    onError: (error) => {
      console.error('Error creating invite link:', error);
      showNotification(t('common:errors.somethingWentWrong'), 'error');
    }
  });

  // Handle adding students
  const handleAddStudents = () => {
    if (selectedStudentIds.length === 0) {
      showNotification(t('teacher:groups.noStudentsSelected'), 'error');
      return;
    }

    addStudentsMutation.mutate({ studentIds: selectedStudentIds });
  };

  // Handle removing a student
  const handleRemoveStudent = (studentId: string) => {
    // Use toast notification with action buttons instead of window.confirm
    showNotification(
      t('teacher:groups.confirmRemoveStudent'),
      'warning',
      [
        {
          label: t('common:buttons.cancel'),
          onClick: () => {/* Do nothing */},
          style: 'secondary'
        },
        {
          label: t('common:buttons.remove'),
          onClick: () => removeStudentMutation.mutate(studentId),
          style: 'danger'
        }
      ],
      6000 // 6 seconds timeout
    );
  };

  // Handle assigning a test
  const handleAssignTest = () => {
    if (!selectedTestId) {
      showNotification(t('teacher:groups.noTestSelected'), 'error');
      return;
    }

    assignTestMutation.mutate({
      testId: selectedTestId,
      dueDate: dueDate || undefined,
    });
  };

  // Handle batch assignment of tests
  const handleBatchAssign = (assignments: AssignTestDto[]) => {
    if (assignments.length === 0) {
      showNotification(t('teacher:tests.noTestsSelected'), 'error');
      return;
    }

    batchAssignTestsMutation.mutate(assignments);
  };

  // Handle calendar date click
  const handleCalendarDateClick = (date: Date) => {
    // Set the due date in the single assignment form when a calendar date is clicked
    setDueDate(date.toISOString().split('T')[0]);
  };

  // Handle calendar assignment click
  const handleCalendarAssignmentClick = (assignmentId: string) => {
    // Calendar view event handling will be implemented in future updates
    console.log('Assignment clicked:', assignmentId);
  };

  // Handle removing a test
  const handleRemoveTest = (testId: string) => {
    // Use toast notification with action buttons instead of window.confirm
    showNotification(
      t('teacher:groups.confirmRemoveTest'),
      'warning',
      [
        {
          label: t('common:buttons.cancel'),
          onClick: () => {/* Do nothing */},
          style: 'secondary'
        },
        {
          label: t('common:buttons.remove'),
          onClick: () => removeTestMutation.mutate(testId),
          style: 'danger'
        }
      ],
      6000 // 6 seconds timeout
    );
  };

  // Handle invite creation
  const handleCreateInvite = (data: CreateGroupInviteDto) => {
    createInviteMutation.mutate(data);
  };

  // Handle closing the invite link display and reset state
  const handleCloseInviteLinkDisplay = () => {
    setShowInviteLinkDisplay(false);
    setCurrentInviteLink(null);
  };

  // Helper function for formatting dates consistently
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(
      navigator.language,
      { year: 'numeric', month: 'short', day: 'numeric' }
    );
  };

  // Filter students not in the group
  const availableStudents = allStudents?.filter(
    (student) => !group?.students?.some((s) => s.student?.id === student.id)
  );

  // Filter students by search term
  const filteredAvailableStudents = availableStudents?.filter(
    (student) =>
      student.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
        </div>
      </div>
    );
  }

  if (!group) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded dark:bg-red-900/30 dark:border-red-800 dark:text-red-400">
          <p>{t('teacher:groups.groupNotFound')}</p>
        </div>
        <div className="mt-4">
          <Link
            to="/teacher/groups"
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            {t('teacher:groups.backToGroups')}
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
      {/* Group Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-2xl font-bold dark:text-white">{group.name}</h1>
            <div className="flex items-center mt-1">
              <span className="text-gray-600 dark:text-gray-300 mr-4">{t('teacher:groups.level')}: {group.level}</span>
              <span
                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  group.isActive
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                }`}
              >
                {group.isActive ? t('common:active') : t('common:inactive')}
              </span>
            </div>
            {group.description && (
              <p className="text-gray-600 dark:text-gray-400 mt-2">{group.description}</p>
            )}
          </div>
          <div className="flex space-x-3">
            <Link
              to={`/teacher/groups/${id}/edit`}
              className="bg-amber-500 hover:bg-amber-600 text-white px-4 py-2 rounded-md transition-colors dark:bg-amber-600 dark:hover:bg-amber-700 font-medium flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              {t('common:buttons.edit')}
            </Link>
            <Link
              to={`/teacher/groups/${id}/results`}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md transition-colors dark:bg-purple-700 dark:hover:bg-purple-800 font-medium flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              {t('teacher:groups.viewResults')}
            </Link>
            <button
              onClick={() => setShowInviteModal(true)}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md transition-colors dark:bg-green-700 dark:hover:bg-green-800 font-medium flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              {t('teacher:groups.generateInvitationLink')}
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-slate-700 mb-6">
          <nav className="-mb-px flex w-full" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('students')}
              className={`flex-1 py-4 text-center border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'students'
                  ? 'border-blue-500 text-blue-600 dark:border-blue-400 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-800'
              }`}
              aria-current={activeTab === 'students' ? 'page' : undefined}
            >
              {t('teacher:groups.students')}
            </button>
            <button
              onClick={() => setActiveTab('tests')}
              className={`flex-1 py-4 text-center border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'tests'
                  ? 'border-blue-500 text-blue-600 dark:border-blue-400 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-800'
              }`}
              aria-current={activeTab === 'tests' ? 'page' : undefined}
            >
              {t('teacher:groups.tests')}
            </button>
          </nav>
        </div>
      </div>

      <div>
        {/* Students Tab */}
        {activeTab === 'students' && (
          <>
            <div className="bg-white dark:bg-slate-800 rounded-md shadow p-4 mb-6">
              <h2 className="text-lg font-semibold mb-4 dark:text-white">{t('teacher:groups.addStudents')}</h2>
              <div className="mb-4">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder={t('teacher:groups.searchStudents')}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-slate-700 dark:text-white"
                />
              </div>
              <div className="max-h-60 overflow-y-auto mb-4 border border-gray-200 dark:border-slate-600 rounded-md">
                {filteredAvailableStudents && filteredAvailableStudents.length > 0 ? (
                  <div className="divide-y divide-gray-200 dark:divide-slate-600">
                    {filteredAvailableStudents.map((student) => (
                      <div
                        key={student.id}
                        className="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-slate-700"
                      >
                        <input
                          type="checkbox"
                          id={`student-${student.id}`}
                          checked={selectedStudentIds.includes(student.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedStudentIds([...selectedStudentIds, student.id]);
                            } else {
                              setSelectedStudentIds(
                                selectedStudentIds.filter((id) => id !== student.id)
                              );
                            }
                          }}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-700"
                        />
                        <label
                          htmlFor={`student-${student.id}`}
                          className="ml-3 block text-sm font-medium text-gray-700 dark:text-slate-300"
                        >
                          {student.firstName} {student.lastName} ({student.email})
                        </label>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-4 text-center text-gray-500 dark:text-slate-400">
                    {searchTerm
                      ? t('teacher:groups.noStudentsFound')
                      : t('teacher:groups.noStudentsAvailable')}
                  </div>
                )}
              </div>
              <div className="flex justify-end">
                <button
                  onClick={handleAddStudents}
                  disabled={selectedStudentIds.length === 0 || addStudentsMutation.isPending}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:bg-green-700 dark:hover:bg-green-800 dark:focus:ring-green-400 dark:focus:ring-offset-slate-800 disabled:bg-green-300 dark:disabled:bg-green-900/50 disabled:cursor-not-allowed transition-colors"
                >
                  {addStudentsMutation.isPending ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {t('common:adding')}
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      {t('teacher:groups.addSelected')}
                    </span>
                  )}
                </button>
              </div>
            </div>

            <div className="bg-white dark:bg-slate-800 rounded-md shadow overflow-hidden mb-6">
              <div className="px-4 py-5 sm:px-6 bg-gray-50 dark:bg-slate-700">
                <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                  {t('teacher:groups.studentsInGroup')} ({group.students?.length || 0})
                </h3>
              </div>
              {group.students && group.students.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-600">
                    <thead className="bg-gray-50 dark:bg-slate-700">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          {t('teacher:groups.name')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          {t('teacher:groups.email')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          {t('teacher:groups.joinedAt')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          {t('common:buttons.actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-600">
                      {group.students.map((studentGroup) => (
                        <tr key={studentGroup.id} className="hover:bg-gray-50 dark:hover:bg-slate-700">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              <Link
                                to={`/teacher/students/${studentGroup.studentId}`}
                                className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                              >
                                {studentGroup.student?.firstName} {studentGroup.student?.lastName}
                              </Link>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500 dark:text-gray-400">{studentGroup.student?.email}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {formatDate(studentGroup.joinedAt)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Link
                              to={`/teacher/students/${studentGroup.studentId}`}
                              className="inline-flex items-center text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3 font-medium"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                              {t('common:actions.view')}
                            </Link>
                            <button
                              onClick={() => handleRemoveStudent(studentGroup.studentId)}
                              className="inline-flex items-center text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 font-medium"
                              disabled={removeStudentMutation.isPending}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                              {t('common:buttons.remove')}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="p-6 text-center text-gray-500 dark:text-slate-400">
                  {t('teacher:groups.noStudentsInGroup')}
                </div>
              )}
            </div>
          </>
        )}

        {/* Tests display section - Only shown in students tab */}
        {activeTab === 'students' && (
          <div className="bg-white dark:bg-slate-800 rounded-md shadow overflow-hidden mb-6">
            <div className="px-4 py-5 sm:px-6 bg-gray-50 dark:bg-slate-700 flex justify-between items-center">
              <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                {t('teacher:groups.assignedTests')} ({group.assignments?.length || 0})
              </h3>
              <button
                onClick={() => setActiveTab('tests')}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                {t('teacher:groups.manageTests', { defaultValue: 'Manage Tests' })}
              </button>
            </div>
            {group.assignments && group.assignments.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-600">
                  <thead className="bg-gray-50 dark:bg-slate-700">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('teacher:groups.testTitle')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('teacher:groups.type')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('teacher:groups.difficulty')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('teacher:groups.dueDate')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('common:buttons.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-600">
                    {group.assignments.map((assignment) => (
                      <tr key={assignment.id} className="hover:bg-gray-50 dark:hover:bg-slate-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {assignment.test?.title}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {assignment.test?.type && t(`teacher:tests.types.${assignment.test.type.toLowerCase()}`, {defaultValue: assignment.test.type})}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {assignment.test?.difficulty && t(`teacher:tests.difficulty.${assignment.test.difficulty.toLowerCase()}`, {defaultValue: assignment.test.difficulty})}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {assignment.dueDate
                              ? formatDate(assignment.dueDate)
                              : t('teacher:groups.noDeadline')}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleRemoveTest(assignment.id)}
                            className="inline-flex items-center text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 font-medium"
                            disabled={removeTestMutation.isPending}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            {t('common:buttons.remove')}
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="p-6 text-center text-gray-500 dark:text-slate-400">
                {t('teacher:groups.noTestsAssigned')}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Tests Tab */}
      {activeTab === 'tests' && (
        <>
          <div className="bg-white dark:bg-slate-800 rounded-md shadow mb-6 overflow-hidden">
            {/* Sub-tab bar for list/calendar views */}
            <div className="flex justify-between items-center border-b border-gray-200 dark:border-slate-700">
              <div className="flex">
                <button
                  className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                    viewMode === 'list'
                      ? 'border-blue-500 text-blue-600 dark:border-blue-400 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-800'
                  }`}
                  onClick={() => setViewMode('list')}
                >
                  {t('teacher:tests.listView')}
                </button>
                <button
                  className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                    viewMode === 'calendar'
                      ? 'border-blue-500 text-blue-600 dark:border-blue-400 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-800'
                  }`}
                  onClick={() => setViewMode('calendar')}
                >
                  {t('teacher:tests.calendarView')}
                </button>
              </div>
              <div>
                <button
                  className="mx-4 my-1.5 bg-green-600 hover:bg-green-700 text-white px-4 py-2 text-sm rounded-md transition-colors dark:bg-green-700 dark:hover:bg-green-800 font-medium"
                  onClick={() => setShowBatchAssignModal(true)}
                >
                  {t('teacher:tests.batchAssign')}
                </button>
              </div>
            </div>

            {/* Calendar View */}
            {viewMode === 'calendar' && group?.assignments && (
              <div className="p-4">
                <h3 className="text-lg font-medium mb-4 text-gray-900 dark:text-white">
                  {t('teacher:tests.assignmentCalendar')}
                </h3>
                <TestAssignmentCalendar
                  assignments={group.assignments}
                  onAssignmentClick={handleCalendarAssignmentClick}
                  onDateClick={handleCalendarDateClick}
                  locale={navigator.language}
                  translations={{
                    today: t('teacher:tests.today'),
                    viewByMonth: t('teacher:tests.viewByMonth'),
                    noTests: t('teacher:groups.noTestsAssigned')
                  }}
                />
              </div>
            )}

            {/* List View */}
            {viewMode === 'list' && (
              <div className="p-4">
                <h2 className="text-lg font-semibold mb-4 dark:text-white">{t('teacher:groups.assignTest')}</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label htmlFor="test" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {t('teacher:groups.selectTest')} *
                    </label>
                    <select
                      id="test"
                      value={selectedTestId}
                      onChange={(e) => setSelectedTestId(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-slate-700 dark:text-white"
                    >
                      <option value="">{t('common:select')}</option>
                      {allTests?.map((test) => (
                        <option key={test.id} value={test.id}>
                          {test.title} ({t(`teacher:tests.types.${test.type.toLowerCase()}`, {defaultValue: test.type})},
                          {t(`teacher:tests.difficulty.${test.difficulty.toLowerCase()}`, {defaultValue: test.difficulty})})
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label htmlFor="due-date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {t('teacher:groups.dueDate')}
                    </label>
                    <input
                      type="date"
                      id="due-date"
                      value={dueDate}
                      onChange={(e) => setDueDate(e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-slate-700 dark:text-white"
                    />
                  </div>
                </div>
                <div className="flex justify-end">
                  <button
                    onClick={handleAssignTest}
                    disabled={!selectedTestId || assignTestMutation.isPending}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:bg-purple-700 dark:hover:bg-purple-800 dark:focus:ring-purple-400 dark:focus:ring-offset-slate-800 disabled:bg-purple-300 dark:disabled:bg-purple-900/50 disabled:cursor-not-allowed transition-colors"
                  >
                    {assignTestMutation.isPending ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {t('common:assigning')}
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        {t('teacher:groups.assignTest')}
                      </span>
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </>
      )}

      {/* Batch Assignment Modal */}
      {showBatchAssignModal && (
        <BatchAssignmentModal
          isOpen={showBatchAssignModal}
          onClose={() => setShowBatchAssignModal(false)}
          onSubmit={handleBatchAssign}
          availableTests={allTests || []}
        />
      )}

      {/* Invitation Link Modal */}
      {showInviteModal && (
        <GroupInviteModal
          isOpen={showInviteModal}
          onClose={() => setShowInviteModal(false)}
          onSubmit={handleCreateInvite}
          isLoading={createInviteMutation.isPending}
        />
      )}

      {showInviteLinkDisplay && currentInviteLink && (
        <InviteLinkDisplay
          isOpen={showInviteLinkDisplay}
          onClose={handleCloseInviteLinkDisplay}
          inviteLink={currentInviteLink}
        />
      )}
    </div>
  );
};

export default GroupDetail;
