import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../types/user';

const Dashboard = () => {
  const { user } = useAuth();
  const { t } = useTranslation(['dashboard', 'common']);

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6 dark:text-white">
        {t('dashboard:welcome', { name: user ? `${user.firstName} ${user.lastName}` : t('dashboard:student') })}
      </h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4 dark:text-white">{t('dashboard:cards.tests.title')}</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">{t('dashboard:cards.tests.description')}</p>
          <Link
            to="/tests"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
          >
            {t('dashboard:cards.tests.action')}
          </Link>
        </div>

        {(user?.role === UserRole.TEACHER || user?.role === UserRole.ADMIN) && (
          <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4 dark:text-white">{t('dashboard:cards.categories.title')}</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">{t('dashboard:cards.categories.description')}</p>
            <Link
              to="/categories"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              {t('dashboard:cards.categories.action')}
            </Link>
          </div>
        )}

        {user?.role === UserRole.TEACHER && (
          <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4 dark:text-white">{t('dashboard:cards.createTest.title')}</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">{t('dashboard:cards.createTest.description')}</p>
            <Link
              to="/tests/create"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              {t('dashboard:cards.createTest.action')}
            </Link>
          </div>
        )}

        {user?.role === UserRole.ADMIN && (
          <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4 dark:text-white">{t('dashboard:cards.userManagement.title')}</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">{t('dashboard:cards.userManagement.description')}</p>
            <Link
              to="/admin/users"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              {t('dashboard:cards.userManagement.action')}
            </Link>
          </div>
        )}

        {user?.role === UserRole.STUDENT && (
          <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4 dark:text-white">{t('dashboard:cards.results.title')}</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">{t('dashboard:cards.results.description')}</p>
            <Link
              to="/student/results"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
              data-discover="true"
            >
              {t('dashboard:cards.results.action')}
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;