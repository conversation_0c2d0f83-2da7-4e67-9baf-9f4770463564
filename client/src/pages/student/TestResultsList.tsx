import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { studentTestApi } from '../../api/studentTestApi';
import { TestResult } from '../../types/student-test';
import { Test } from '../../types/test';
import { useTheme } from '../../contexts/ThemeContext';

const TestResultsList: React.FC = () => {
  const { testId } = useParams<{ testId: string }>();
  const { t, i18n } = useTranslation(['tests', 'common']);

  // Log current language and loaded namespaces for debugging
  console.log('Current language in TestResultsList:', i18n.language);
  console.log('Loaded namespaces:', i18n.options.ns);

  // Check if translations are loaded correctly
  useEffect(() => {
    console.log('Translation for "result.score":', t('result.score', { ns: 'tests' }));
    console.log('Translation for "result.timeTaken":', t('result.timeTaken', { ns: 'tests' }));
    console.log('Translation for "common:actions.view":', t('common:actions.view'));

    // Force language to be set correctly if needed
    const storedLang = localStorage.getItem('i18nextLng');
    if (storedLang && i18n.language !== storedLang) {
      console.log('Forcing language to:', storedLang);
      i18n.changeLanguage(storedLang);
    }
  }, [t, i18n]);
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [test, setTest] = useState<Test | null>(null);

  // Fetch test details if testId is provided
  const { data: testDetails } = useQuery({
    queryKey: ['test', testId],
    queryFn: () => studentTestApi.getTestWithProgress(testId!),
    enabled: !!testId,
  });

  // Fetch test results
  const { data: results, isLoading, error } = useQuery({
    queryKey: ['testResults', testId],
    queryFn: () => studentTestApi.getTestResults(testId),
    retry: 1,
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (testDetails?.test) {
      setTest(testDetails.test);
    }
  }, [testDetails]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50 dark:bg-slate-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400"
          aria-label={t('common:messages.loading', { defaultValue: 'Loading...' })}>
        </div>
        <span className="sr-only">{t('common:messages.loading', { defaultValue: 'Loading...' })}</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 min-h-screen bg-gray-50 dark:bg-slate-900">
        <div className="mb-8">
          <Link to={testId ? "/student/tests" : "/"} className="text-blue-600 hover:underline dark:text-blue-400 dark:hover:text-blue-300">
            &larr; {testId ? t('common:navigation.backToTests') : t('common:navigation.backToDashboard', { defaultValue: 'Back to Dashboard' })}
          </Link>
          <h1 className="text-2xl font-bold mt-4 text-gray-900 dark:text-white">
            {testId ? (test?.title || t('result.testResults', { ns: 'tests' })) : t('result.allTestResults', { ns: 'tests', defaultValue: 'All Test Results' })}
          </h1>
        </div>
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
          <div className="text-center text-red-600 dark:text-red-400 p-4">
            <p className="font-medium mb-2">{t('common:messages.error')}</p>
            <p className="text-gray-600 dark:text-gray-400">
              {t('common:messages.tryAgain', { defaultValue: 'Please try again later.' })}
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!results || results.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8 min-h-screen bg-gray-50 dark:bg-slate-900">
        <div className="mb-8">
          <Link to={testId ? "/student/tests" : "/"} className="text-blue-600 hover:underline dark:text-blue-400 dark:hover:text-blue-300">
            &larr; {testId ? t('common:navigation.backToTests') : t('common:navigation.backToDashboard', { defaultValue: 'Back to Dashboard' })}
          </Link>
          <h1 className="text-2xl font-bold mt-4 text-gray-900 dark:text-white">
            {testId ? (test?.title || t('result.testResults', { ns: 'tests' })) : t('result.allTestResults', { ns: 'tests', defaultValue: 'All Test Results' })}
          </h1>
        </div>
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
          <p className="text-gray-600 dark:text-gray-300 text-center py-8">
            {t('result.noResults', { ns: 'tests', defaultValue: 'No test results found.' })}
          </p>
        </div>
      </div>
    );
  }

  const handleResultClick = (resultId: string, resultTestId?: string) => {
    const targetTestId = resultTestId || testId;
    if (!targetTestId) {
      console.error('No test ID available for navigation');
      return;
    }
    navigate(`/student/tests/${targetTestId}/result/${resultId}`);
  };

  // Process content for dark mode if needed
  const getThemeClasses = (lightClass: string, darkClass: string): string => {
    return theme === 'dark' ? darkClass : lightClass;
  };

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen bg-gray-50 dark:bg-slate-900">
      <div className="mb-8">
        <Link to={testId ? "/student/tests" : "/"} className="text-blue-600 hover:underline dark:text-blue-400 dark:hover:text-blue-300">
          &larr; {testId ? t('common:navigation.backToTests') : t('common:navigation.backToDashboard', { defaultValue: 'Back to Dashboard' })}
        </Link>
        <h1 className="text-2xl font-bold mt-4 text-gray-900 dark:text-white">
          {testId ? (test?.title || t('result.testResults', { ns: 'tests' })) : t('result.allTestResults', { ns: 'tests', defaultValue: 'All Test Results' })}
        </h1>
      </div>

      <div className="bg-white dark:bg-slate-800 rounded-lg shadow overflow-hidden">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('result.history', { ns: 'tests', defaultValue: 'Test Result History' })}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {testId
              ? t('result.historyDescription', { ns: 'tests', defaultValue: 'View your previous attempts for this test.' })
              : t('result.allHistoryDescription', { ns: 'tests', defaultValue: 'View all your test results.' })}
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-slate-700">
              <tr>
                {!testId && (
                  <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${getThemeClasses('text-gray-500', 'text-gray-300')} uppercase tracking-wider`}>
                    {t('result.testName', { ns: 'tests', defaultValue: 'Test Name' })}
                  </th>
                )}
                <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${getThemeClasses('text-gray-500', 'text-gray-300')} uppercase tracking-wider`}>
                  {t('result.date', { ns: 'tests', defaultValue: 'Date' })}
                </th>
                <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${getThemeClasses('text-gray-500', 'text-gray-300')} uppercase tracking-wider`}>
                  {t('result.score', { ns: 'tests', defaultValue: 'Score' })}
                </th>
                <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${getThemeClasses('text-gray-500', 'text-gray-300')} uppercase tracking-wider`}>
                  {t('result.timeTaken', { ns: 'tests', defaultValue: 'Time Taken' })}
                </th>
                <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${getThemeClasses('text-gray-500', 'text-gray-300')} uppercase tracking-wider`}>
                  {t('result.correctAnswers', { ns: 'tests', defaultValue: 'Correct Answers' })}
                </th>
                <th scope="col" className={`px-6 py-3 text-right text-xs font-medium ${getThemeClasses('text-gray-500', 'text-gray-300')} uppercase tracking-wider`}>
                  {t('common:actions.view', { defaultValue: 'View' })}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-gray-700">
              {results && results.length > 0 ? (
                results.map((result: TestResult) => {
                  const scorePercentage = result.maxScore > 0
                    ? Math.round((result.score / result.maxScore) * 100)
                    : 0;

                  const minutes = Math.floor(result.timeSpent / 60);
                  const seconds = result.timeSpent % 60;
                  const timeTaken = t('result.timeFormat', {
                    minutes,
                    seconds,
                    defaultValue: '{{minutes}}m {{seconds}}s'
                  });

                  return (
                    <tr
                      key={result.id}
                      className="hover:bg-gray-50 dark:hover:bg-slate-700 cursor-pointer"
                      onClick={() => handleResultClick(result.id, result.testId)}
                    >
                      {!testId && (
                        <td className={`px-6 py-4 whitespace-nowrap text-sm ${getThemeClasses('text-gray-900', 'text-white')}`}>
                          {result.testTitle || t('result.unknownTest', { ns: 'tests', defaultValue: 'Unknown Test' })}
                        </td>
                      )}
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${getThemeClasses('text-gray-900', 'text-white')}`}>
                        {new Date(result.submittedAt).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className={`text-sm font-medium ${
                            scorePercentage >= 70
                              ? getThemeClasses('text-green-600', 'text-green-400')
                              : scorePercentage >= 40
                                ? getThemeClasses('text-yellow-600', 'text-yellow-400')
                                : getThemeClasses('text-red-600', 'text-red-400')
                          }`}>
                            {scorePercentage}%
                          </div>
                          <div className={`text-sm ${getThemeClasses('text-gray-500', 'text-gray-400')} ml-2`}>
                            ({result.score}/{result.maxScore})
                          </div>
                        </div>
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${getThemeClasses('text-gray-500', 'text-gray-400')}`}>
                        {timeTaken}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${getThemeClasses('text-gray-500', 'text-gray-400')}`}>
                        {result.correctAnswers}/{result.totalQuestions}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          className={`${getThemeClasses('text-blue-600 hover:text-blue-900', 'text-blue-400 hover:text-blue-300')}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleResultClick(result.id, result.testId);
                          }}
                        >
                          {t('common:actions.view', { defaultValue: 'View' })}
                        </button>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={testId ? 5 : 6} className="px-6 py-10 text-center">
                    <div className={`${getThemeClasses('text-gray-500', 'text-gray-400')}`}>
                      {testId
                        ? t('result.notAnswered', { ns: 'tests', defaultValue: 'No results found for this test.' })
                        : t('result.noResults', { ns: 'tests', defaultValue: 'You have not taken any tests yet.' })}
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TestResultsList;
