import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { studentTestApi } from '../../api/studentTestApi';
import { TestResult } from '../../types/student-test';

const TestResultPage = () => {
  const { testId } = useParams<{ testId: string }>();
  const { data: result, isLoading, error } = useQuery<TestResult>({
    queryKey: ['testResult', testId],
    queryFn: () => studentTestApi.getTestResult(testId!),
    enabled: !!testId,
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-600 p-4">
        An error occurred while loading the test result. Please try again later.
      </div>
    );
  }

  if (!result) {
    return (
      <div className="text-center text-gray-600 p-4">
        No result found.
      </div>
    );
  }

  const scorePercentage = (result.score / result.totalQuestions) * 100;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Header */}
          <div className="bg-blue-600 text-white p-6">
            <h1 className="text-2xl font-bold mb-2">Test Results</h1>
            <div className="flex justify-between items-center">
              <div>
                <p className="text-blue-100">Completed on: {new Date(result.submittedAt).toLocaleDateString()}</p>
                <p className="text-blue-100">Time taken: {Math.floor(result.timeSpent / 60)} minutes</p>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold">{scorePercentage.toFixed(1)}%</p>
                <p className="text-blue-100">Score: {result.score} / {result.totalQuestions}</p>
              </div>
            </div>
          </div>

          {/* Score Summary */}
          <div className="p-6 border-b">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="text-green-800 font-semibold mb-1">Correct Answers</h3>
                <p className="text-2xl font-bold text-green-600">{result.correctAnswers}</p>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <h3 className="text-red-800 font-semibold mb-1">Incorrect Answers</h3>
                <p className="text-2xl font-bold text-red-600">{result.incorrectAnswers}</p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-blue-800 font-semibold mb-1">Total Questions</h3>
                <p className="text-2xl font-bold text-blue-600">{result.totalQuestions}</p>
              </div>
            </div>
          </div>

          {/* Detailed Results */}
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4">Question Details</h2>
            <div className="space-y-6">
              {Array.isArray(result.answers) ? result.answers.map((answer) => (
                <div
                  key={answer.questionNumber}
                  className={`p-4 rounded-lg border ${
                    answer.isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold">Question {answer.questionNumber}</h3>
                    <span
                      className={`px-2 py-1 rounded text-sm font-medium ${
                        answer.isCorrect
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {answer.isCorrect ? 'Correct' : 'Incorrect'}
                    </span>
                  </div>
                  
                  <div className="mt-2">
                    <p className="text-gray-600 mb-2">Your Answer:</p>
                    <p className="font-medium">
                      {Array.isArray(answer.answer)
                        ? answer.answer.join(', ')
                        : answer.answer}
                    </p>
                  </div>

                  {!answer.isCorrect && answer.feedback && (
                    <div className="mt-4 p-3 bg-white rounded border border-red-100">
                      <p className="text-gray-600 mb-1">Feedback:</p>
                      <p className="text-red-700">{answer.feedback}</p>
                    </div>
                  )}
                </div>
              )) : <p className="text-gray-600">No detailed answers available</p>}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestResultPage;