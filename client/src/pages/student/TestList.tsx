import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { studentTestApi, PaginatedResponse } from '../../api/studentTestApi';
import { Test, TestType, TestDifficulty } from '../../types/test';

const difficultyColors: Record<TestDifficulty, string> = {
  [TestDifficulty.EASY]: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
  [TestDifficulty.MEDIUM]: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
  [TestDifficulty.HARD]: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
};

const testTypeLabels: Record<TestType, string> = {
  [TestType.READING]: 'Reading',
  [TestType.LISTENING]: 'Listening',
  [TestType.WRITING]: 'Writing',
  [TestType.SPEAKING]: 'Speaking',
  [TestType.FULL_TEST]: 'Full Test',
};

// Helper function to truncate and strip HTML
const truncateDescription = (htmlString: string, maxLength = 150): string => {
  if (!htmlString) return 'No description provided.';

  // Create a temporary div to parse HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlString;

  // Remove any script tags for security
  const scripts = tempDiv.getElementsByTagName('script');
  for (let i = scripts.length - 1; i >= 0; i--) {
    scripts[i].parentNode?.removeChild(scripts[i]);
  }

  // Extract text content
  const textContent = tempDiv.textContent || tempDiv.innerText || '';
  const trimmedText = textContent.trim().replace(/\s+/g, ' ');

  if (trimmedText.length <= maxLength) {
    return trimmedText;
  }

  return trimmedText.substring(0, maxLength) + '...';
};

const TestList = () => {
  const [page, setPage] = useState(1);
  const [limit] = useState(6); // Number of tests per page

  const { data, isLoading, error } = useQuery({
    queryKey: ['publishedTests', page, limit],
    queryFn: () => studentTestApi.getPublishedTests(page, limit),
  });

  // Add logging for debugging
  React.useEffect(() => {
    console.log('Tests data:', data);
  }, [data]);

  // Extract tests array and pagination metadata
  const tests = data?.tests || [];
  const meta = data?.meta || {
    totalItems: 0,
    itemsPerPage: limit,
    totalPages: 0,
    currentPage: page
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen dark:bg-slate-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-600 dark:text-red-400 p-4 dark:bg-slate-900">
        An error occurred while loading tests. Please try again later.
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 dark:bg-slate-900">
      <h1 className="text-3xl font-bold mb-8 dark:text-white">Available Tests</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {tests.map((test: Test) => (
          <div
            key={test.id}
            className="bg-white dark:bg-slate-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 h-full flex flex-col"
          >
            <div className="p-6 flex flex-col flex-grow">
              <div className="flex justify-between items-start mb-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white line-clamp-2">{test.title}</h2>
                <span
                  className={`px-2 py-1 rounded text-sm font-medium whitespace-nowrap ml-2 ${
                    difficultyColors[test.difficulty]
                  }`}
                >
                  {test.difficulty}
                </span>
              </div>

              <div className="space-y-3 flex-grow">
                <p className="text-gray-600 dark:text-slate-300 line-clamp-2">
                  {test.description ? truncateDescription(test.description) : 'No description provided.'}
                </p>

                <div className="flex items-center text-sm text-gray-500 dark:text-slate-400">
                  <svg
                    className="w-5 h-5 mr-2 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span>{test.timeLimit} minutes</span>
                </div>

                <div className="flex items-center text-sm text-gray-500 dark:text-slate-400">
                  <svg
                    className="w-5 h-5 mr-2 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                    />
                  </svg>
                  <span>{testTypeLabels[test.type]}</span>
                </div>

                {test.category && (
                  <div className="flex items-center text-sm text-gray-500 dark:text-slate-400">
                    <svg
                      className="w-5 h-5 mr-2 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                      />
                    </svg>
                    <span>{test.category.name}</span>
                  </div>
                )}
              </div>

              <div className="mt-6">
                <Link
                  to={`/student/tests/${test.id}`}
                  className="block w-full text-center bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors duration-300"
                >
                  Start Test
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      {(!tests || tests.length === 0) && (
        <div className="text-center text-gray-600 dark:text-slate-400 py-8">
          No tests are currently available. Please check back later.
        </div>
      )}

      {/* Pagination Controls */}
      {meta.totalPages > 1 && (
        <div className="flex justify-center items-center mt-10 space-x-2">
          <button
            onClick={() => setPage(prev => Math.max(prev - 1, 1))}
            disabled={page === 1}
            className={`px-4 py-2 rounded-md ${
              page === 1
                ? 'bg-gray-200 text-gray-500 dark:bg-slate-700 dark:text-slate-400 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800'
            }`}
          >
            Previous
          </button>

          <div className="flex items-center space-x-1">
            {[...Array(meta.totalPages)].map((_, index) => {
              const pageNumber = index + 1;
              return (
                <button
                  key={pageNumber}
                  onClick={() => setPage(pageNumber)}
                  className={`w-10 h-10 rounded-md ${
                    pageNumber === page
                      ? 'bg-blue-600 text-white dark:bg-blue-700'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-slate-700 dark:text-slate-300 dark:hover:bg-slate-600'
                  }`}
                >
                  {pageNumber}
                </button>
              );
            })}
          </div>

          <button
            onClick={() => setPage(prev => Math.min(prev + 1, meta.totalPages))}
            disabled={page === meta.totalPages}
            className={`px-4 py-2 rounded-md ${
              page === meta.totalPages
                ? 'bg-gray-200 text-gray-500 dark:bg-slate-700 dark:text-slate-400 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800'
            }`}
          >
            Next
          </button>
        </div>
      )}

      {/* Pagination Info */}
      <div className="text-center text-gray-600 dark:text-slate-400 mt-4">
        {meta.totalItems > 0 && (
          <p>
            Showing {(page - 1) * limit + 1} to {Math.min(page * limit, meta.totalItems)} of {meta.totalItems} tests
          </p>
        )}
      </div>
    </div>
  );
};

export default TestList;