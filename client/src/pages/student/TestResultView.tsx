import { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { studentTestApi } from '../../api/studentTestApi';
import { TestResult, StudentAnswer } from '../../types/student-test';
import { Test, QuestionType } from '../../types/test';
import { useTheme } from '../../contexts/ThemeContext';
import { FaHistory, FaChevronRight } from 'react-icons/fa';

const TestResultView = () => {
  const { testId, resultId } = useParams<{ testId: string; resultId?: string }>();
  const { t } = useTranslation(['tests', 'common']);
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [result, setResult] = useState<TestResult | null>(null);
  const [test, setTest] = useState<Test | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [otherResults, setOtherResults] = useState<TestResult[]>([]);
  const [showSidebar, setShowSidebar] = useState(false);

  // Fetch test details
  const { data: testData } = useQuery({
    queryKey: ['test', testId],
    queryFn: () => studentTestApi.getTestWithProgress(testId!),
    enabled: !!testId,
  });

  // Fetch all results for this test
  const { data: resultsData } = useQuery({
    queryKey: ['testResults', testId],
    queryFn: () => studentTestApi.getTestResults(testId),
    enabled: !!testId,
  });

  useEffect(() => {
    const fetchResult = async () => {
      try {
        setLoading(true);
        if (testId) {
          console.log(`Fetching test result for testId: ${testId}, resultId: ${resultId || 'latest'}`);
          const testResult = await studentTestApi.getTestResult(testId, resultId);
          console.log('Received test result:', testResult);
          setResult(testResult);
        }
      } catch (err: any) {
        console.error('Error loading test result:', err);
        setError(err.message || 'Failed to load test result');
      } finally {
        setLoading(false);
      }
    };

    fetchResult();
  }, [testId, resultId]);

  // Set test data when available
  useEffect(() => {
    if (testData?.test) {
      setTest(testData.test);
    }
  }, [testData]);

  // Set other results when available
  useEffect(() => {
    if (resultsData && result) {
      // Filter out the current result
      const filteredResults = resultsData.filter(r => r.id && result.id && r.id !== result.id);
      setOtherResults(filteredResults);
    }
  }, [resultsData, result]);

  const handleResultClick = (resultId: string) => {
    navigate(`/student/tests/${testId}/result/${resultId}`);
    setShowSidebar(false);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50 dark:bg-slate-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400"></div>
      </div>
    );
  }

  if (error || !result || !test) {
    return (
      <div className="text-center p-4 bg-gray-50 dark:bg-slate-900 min-h-screen">
        <div className="text-red-600 dark:text-red-400 mb-4">
          {error || t('common:messages.error')}
        </div>
        <div className="bg-gray-100 dark:bg-slate-800 p-4 rounded border border-gray-200 dark:border-slate-700">
          <pre className="whitespace-pre-wrap text-left text-gray-800 dark:text-gray-200">
            {result ? JSON.stringify(result, null, 2) : t('common:messages.noData')}
          </pre>
          <pre className="whitespace-pre-wrap text-left mt-4 text-gray-800 dark:text-gray-200">
            {test ? JSON.stringify(test, null, 2) : t('common:messages.noData')}
          </pre>
        </div>
      </div>
    );
  }

  // Fix the percentage score calculation to handle zero or undefined values
  const calculatePercentage = (numerator: number, denominator: number): number => {
    if (!denominator || denominator === 0) return 0;
    return Math.round((numerator / denominator) * 100);
  };

  const percentageScore = calculatePercentage(result.score, result.maxScore || test.maxScore || 100);
  const correctPercentage = calculatePercentage(result.correctAnswers, result.totalQuestions || 1);
  const incorrectPercentage = calculatePercentage(result.incorrectAnswers, result.totalQuestions || 1);

  // Format answer display based on question type
  const formatAnswer = (answer: any): string => {
    if (!answer && answer !== 0 && answer !== false) return t('take.notAnswered', { ns: 'tests' });

    if (Array.isArray(answer)) {
      return answer.length > 0 ? answer.join(', ') : t('take.notAnswered', { ns: 'tests' });
    }

    return answer.toString();
  };

  // Get the correct answer for a question
  const getCorrectAnswer = (questionNumber: number, questionType: string): string => {
    if (!test.answerSheet) return t('result.notAvailable', { ns: 'tests', defaultValue: 'Not available' });

    // Parse answerSheet if it's a string
    let answerSheet;
    if (typeof test.answerSheet === 'string') {
      try {
        answerSheet = JSON.parse(test.answerSheet);
      } catch (e) {
        console.error('Failed to parse answerSheet:', e);
        return t('result.notAvailable', { ns: 'tests', defaultValue: 'Not available' });
      }
    } else {
      answerSheet = test.answerSheet;
    }

    // Check if answerSheet has the expected properties
    if (!answerSheet || (typeof answerSheet !== 'object')) {
      return t('result.notAvailable', { ns: 'tests', defaultValue: 'Not available' });
    }

    // For multiple choice questions
    if (questionType === 'multipleChoice' || questionType === QuestionType.MultipleChoice) {
      let mcAnswers = answerSheet.multipleChoiceAnswers || [];

      // Parse multipleChoiceAnswers if it's a string
      if (typeof mcAnswers === 'string') {
        try {
          mcAnswers = JSON.parse(mcAnswers);
        } catch (e) {
          console.error('Failed to parse multipleChoiceAnswers:', e);
          mcAnswers = [];
        }
      }

      const mcAnswer = Array.isArray(mcAnswers)
        ? mcAnswers.find(a => Number(a.questionNumber) === questionNumber)
        : null;

      if (mcAnswer) {
        return `Option ${mcAnswer.correctOption}`;
      }
    }

    // For fill-in-the-blank questions
    if (questionType === 'fillInTheBlank' || questionType === QuestionType.FillInTheBlank) {
      let textAnswers = answerSheet.textAnswers || [];

      // Parse textAnswers if it's a string
      if (typeof textAnswers === 'string') {
        try {
          textAnswers = JSON.parse(textAnswers);
        } catch (e) {
          console.error('Failed to parse textAnswers:', e);
          textAnswers = [];
        }
      }

      const textAnswer = Array.isArray(textAnswers)
        ? textAnswers.find(a => Number(a.questionNumber) === questionNumber)
        : null;

      if (textAnswer && textAnswer.correctAnswers && textAnswer.correctAnswers.length > 0) {
        return textAnswer.correctAnswers.join(' / ');
      }
    }

    return t('result.notAvailable', { ns: 'tests', defaultValue: 'Not available' });
  };

  // Get question type-specific styling
  const getQuestionTypeLabel = (type: string | undefined): string => {
    switch (type) {
      case QuestionType.MultipleChoice:
        return t('take.multipleChoice', { ns: 'tests' });
      case QuestionType.FillInTheBlank:
        return t('take.fillInTheBlank', { ns: 'tests' });
      case QuestionType.Essay:
        return t('take.essay', { ns: 'tests' });
      case QuestionType.TrueFalse:
        return t('take.trueFalse', { ns: 'tests' });
      case QuestionType.Matching:
        return t('take.matching', { ns: 'tests' });
      default:
        return t('take.question', { ns: 'tests' });
    }
  };

  // Process answers from the result or generate placeholders if empty
  const getAnswersToDisplay = () => {
    // Log for debugging
    console.log("Raw result data:", result);

    // Check if we have structured answers in the result
    if (result.answers && Array.isArray(result.answers) && result.answers.length > 0) {
      console.log("Using structured answers from result:", result.answers);
      return result.answers;
    }

    // Try to parse answers if they exist as a string (JSON)
    if (typeof result.answers === 'string' && result.answers) {
      try {
        const parsedAnswers = JSON.parse(result.answers);
        if (Array.isArray(parsedAnswers) && parsedAnswers.length > 0) {
          console.log("Using parsed JSON answers:", parsedAnswers);
          return parsedAnswers;
        }
      } catch (err) {
        console.error("Failed to parse answers JSON:", err);
      }
    }

    // If no answers but we have questions, create placeholders
    if (test.questions && test.questions.length > 0) {
      console.log("Generating placeholder answers from test questions:", test.questions.length);
      return test.questions.map((_, index) => ({
        questionNumber: index + 1,
        answer: t('take.notAnswered', { ns: 'tests' }),
        isCorrect: false
      }));
    }

    // As a last resort, create at least 5 placeholder answers
    console.log("Creating default placeholder answers");
    return Array.from({ length: 5 }, (_, i) => ({
      questionNumber: i + 1,
      answer: t('take.notAnswered', { ns: 'tests' }),
      isCorrect: false
    }));
  };

  // Process HTML content for dark mode
  const processContentForDarkMode = (content: string): string => {
    if (theme !== 'dark' || !content) return content;

    return content
      // Replace black text colors with light colors for dark mode
      .replace(/color:\s*rgb\(0,\s*0,\s*0\)/g, 'color: rgb(229, 231, 235)')
      .replace(/color:\s*black/g, 'color: rgb(229, 231, 235)')
      .replace(/color:\s*#000000/g, 'color: rgb(229, 231, 235)')
      .replace(/color:\s*#000/g, 'color: rgb(229, 231, 235)')
      // Replace dark colors with lighter colors
      .replace(/color:\s*rgb\(51,\s*51,\s*51\)/g, 'color: rgb(209, 213, 219)')
      .replace(/color:\s*rgb\(68,\s*68,\s*68\)/g, 'color: rgb(209, 213, 219)')
      .replace(/color:\s*#333/g, 'color: rgb(209, 213, 219)')
      .replace(/color:\s*#444/g, 'color: rgb(209, 213, 219)')
      // Fix specific span elements with inline styles
      .replace(/<span style="([^"]*)color: rgb\(0, 0, 0\)([^"]*)">([^<]+)<\/span>/g,
               '<span style="$1color: rgb(229, 231, 235)$2">$3</span>')
      // Fix the specific span in the test content
      .replace(/<span style="color: rgb\(0, 0, 0\); font-size: medium; white-space-collapse: preserve;">([^<]+)<\/span>/g,
               '<span style="color: rgb(229, 231, 235); font-size: medium; white-space-collapse: preserve;">$1</span>')
      // Process inline style attributes
      .replace(/style="([^"]*)"/g, (_, styles) => {
        // Replace background colors with dark mode equivalents
        const newStyles = styles
          .replace(/background-color:\s*white/g, 'background-color: rgb(30, 41, 59)')
          .replace(/background-color:\s*#ffffff/g, 'background-color: rgb(30, 41, 59)')
          .replace(/background-color:\s*#fff/g, 'background-color: rgb(30, 41, 59)')
          .replace(/background-color:\s*rgb\(255,\s*255,\s*255\)/g, 'background-color: rgb(30, 41, 59)')
          // Replace light background colors
          .replace(/background-color:\s*rgb\(245,\s*245,\s*245\)/g, 'background-color: rgb(51, 65, 85)')
          .replace(/background-color:\s*rgb\(249,\s*249,\s*249\)/g, 'background-color: rgb(51, 65, 85)')
          .replace(/background-color:\s*rgb\(240,\s*240,\s*240\)/g, 'background-color: rgb(51, 65, 85)')
          .replace(/background-color:\s*#f5f5f5/g, 'background-color: rgb(51, 65, 85)')
          .replace(/background-color:\s*#f9f9f9/g, 'background-color: rgb(51, 65, 85)')
          .replace(/background-color:\s*#f0f0f0/g, 'background-color: rgb(51, 65, 85)');
        return `style="${newStyles}"`;
      });
  };

  const answersToDisplay = getAnswersToDisplay();
  console.log("Final answers to display:", answersToDisplay);

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen bg-gray-50 dark:bg-slate-900">
      <div className="mb-8 flex flex-col md:flex-row md:justify-between md:items-center">
        <div>
          <Link to="/student/tests" className="text-blue-600 hover:underline dark:text-blue-400 dark:hover:text-blue-300">
            &larr; {t('common:navigation.backToTests')}
          </Link>
          <h1 className="text-2xl font-bold mt-4 text-gray-900 dark:text-white">{test.title} - {t('result.title', { ns: 'tests' })}</h1>
        </div>
        
        {/* Results History Button */}
        <div className="flex space-x-4 mt-4 md:mt-0">
          <Link 
            to={`/student/tests/${testId}/results`}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <FaHistory className="mr-2" />
            {t('result.viewHistory', { ns: 'tests', defaultValue: 'View History' })}
          </Link>
          
          {otherResults.length > 0 && (
            <button
              onClick={() => setShowSidebar(!showSidebar)}
              className="inline-flex items-center px-4 py-2 bg-gray-200 dark:bg-slate-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-slate-600 transition-colors"
            >
              {showSidebar ? t('common:actions.hide', { defaultValue: 'Hide' }) : t('common:actions.show', { defaultValue: 'Show' })} {t('result.otherResults', { ns: 'tests', defaultValue: 'Other Results' })}
            </button>
          )}
        </div>
      </div>

      {/* Main content with optional sidebar */}
      <div className="flex flex-col lg:flex-row">
        {/* Sidebar for other results */}
        {showSidebar && otherResults.length > 0 && (
          <div className="lg:w-1/4 bg-white dark:bg-slate-800 rounded-lg shadow p-4 mb-6 lg:mb-0 lg:mr-6">
            <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              {t('result.otherResults', { ns: 'tests', defaultValue: 'Other Results' })}
            </h2>
            <div className="space-y-2">
              {otherResults.map((otherResult) => {
                const date = new Date(otherResult.submittedAt).toLocaleDateString();
                const scorePercentage = Math.round((otherResult.score / otherResult.maxScore) * 100);
                
                return (
                  <button
                    key={otherResult.id}
                    onClick={() => handleResultClick(otherResult.id)}
                    className="w-full text-left p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors flex justify-between items-center"
                  >
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">{date}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {t('result.score', { ns: 'tests', defaultValue: 'Score' })}: {otherResult.score}/{otherResult.maxScore}
                      </div>
                    </div>
                    <div className={`text-sm font-medium px-2 py-1 rounded-full ${
                      scorePercentage >= 70 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' 
                        : scorePercentage >= 40 
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' 
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                    }`}>
                      {scorePercentage}%
                    </div>
                    <FaChevronRight className="text-gray-400" />
                  </button>
                );
              })}
            </div>
          </div>
        )}
        
        {/* Main content */}
        <div className={`${showSidebar && otherResults.length > 0 ? 'lg:w-3/4' : 'w-full'}`}>
          {/* Test Summary */}
          <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow mb-8 border-t-4 border-blue-500 dark:border-blue-600">
            <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">{t('result.summary', { ns: 'tests', defaultValue: 'Test Summary' })}</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg text-center border-2 border-blue-300 dark:border-blue-700">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">{t('result.yourScore', { ns: 'tests', defaultValue: 'Your Score' })}</h3>
                <p className="text-3xl font-bold text-blue-700 dark:text-blue-400">{result.score}/{result.maxScore}</p>
                <p className="text-lg font-medium text-blue-600 dark:text-blue-300">{percentageScore}%</p>
              </div>

              <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg text-center border-2 border-green-300 dark:border-green-700">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">{t('result.passedQuestions', { ns: 'tests' })}</h3>
                <p className="text-3xl font-bold text-green-700 dark:text-green-400">{result.correctAnswers || 0}</p>
                <p className="text-lg font-medium text-green-600 dark:text-green-300">{correctPercentage}%</p>
              </div>

              <div className="bg-red-50 dark:bg-red-900/30 p-4 rounded-lg text-center border-2 border-red-300 dark:border-red-700">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">{t('result.incorrect', { ns: 'tests', defaultValue: 'Incorrect Answers' })}</h3>
                <p className="text-3xl font-bold text-red-700 dark:text-red-400">{result.incorrectAnswers || 0}</p>
                <p className="text-lg font-medium text-red-600 dark:text-red-300">{incorrectPercentage}%</p>
              </div>
            </div>

            <div className="mt-6 bg-gray-50 dark:bg-slate-700 p-4 rounded-lg border-2 border-gray-200 dark:border-slate-600">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">{t('result.timeTaken', { ns: 'tests' })}</h3>
              <p className="text-lg font-medium text-gray-800 dark:text-gray-200">
                {Math.floor(result.timeSpent / 60)} {t('fields.minutes', { ns: 'tests' })} {result.timeSpent % 60} {t('common:time.seconds')}
              </p>
            </div>
          </div>

          {/* Two column layout for test content and answers */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            {/* Test Content - Left Panel */}
            <div className="lg:col-span-2 bg-white dark:bg-slate-800 p-6 rounded-lg shadow border-t-4 border-purple-500 dark:border-purple-600">
              <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">{t('result.testContent', { ns: 'tests', defaultValue: 'Test Content' })}</h2>
              <div className="prose dark:prose-invert max-w-none overflow-auto max-h-[70vh]">
                {/* Display the complete HTML content of the test */}
                {test.content ? (
                  <div
                    dangerouslySetInnerHTML={{
                      __html: processContentForDarkMode(test.content)
                    }}
                    className="prose prose-base md:prose-lg prose-headings:font-bold prose-p:my-2 prose-li:my-1 prose-headings:my-3 prose-pre:bg-gray-100 dark:prose-pre:bg-slate-700 prose-pre:p-2 prose-pre:rounded max-w-none whitespace-pre-wrap dark:prose-invert dark:text-gray-200"
                    style={{ lineHeight: 1.6 }}
                  />
                ) : (
                  <div className="italic text-gray-500 dark:text-gray-400">{t('common:messages.noContent')}</div>
                )}
              </div>
            </div>

            {/* Answer Summary - Right Panel */}
            <div className="lg:col-span-1 bg-white dark:bg-slate-800 p-6 rounded-lg shadow border-t-4 border-amber-500 dark:border-amber-600">
              <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">{t('result.reviewAnswers', { ns: 'tests' })}</h2>
              {answersToDisplay.length === 0 ? (
                <div className="text-center p-4 bg-gray-50 dark:bg-slate-700 rounded-lg text-gray-500 dark:text-gray-400 italic">
                  {t('result.notAnswered', { ns: 'tests' })}
                </div>
              ) : (
                <div className="space-y-4 overflow-auto max-h-[70vh] pr-2">
                  {answersToDisplay.map((userAnswer: StudentAnswer, index: number) => {
                    const questionNumber = userAnswer.questionNumber || index + 1;
                    const isCorrect = userAnswer.isCorrect === true;

                    // Try to find the question in the test
                    const question = test.questions?.find((q, idx) => {
                      // Try by _id if it exists
                      if (q._id) {
                        if (typeof q._id === 'string' && q._id.includes(questionNumber.toString())) return true;
                        if (typeof q._id === 'number' && q._id === questionNumber) return true;
                      }

                      // Then try by array index
                      return idx + 1 === questionNumber;
                    });

                    // Determine question type
                    const questionType = question?.type || 'multipleChoice';

                    return (
                      <div
                        key={`answer-${questionNumber}-${index}`}
                        className={`p-3 border-2 rounded ${
                          isCorrect
                            ? 'border-green-500 bg-green-50 dark:border-green-700 dark:bg-green-900/30'
                            : 'border-red-500 bg-red-50 dark:border-red-700 dark:bg-red-900/30'
                        }`}
                      >
                        <div className="flex justify-between items-center mb-1">
                          <h3 className="font-medium text-sm text-gray-800 dark:text-gray-200">{t('fields.questionNumber', { ns: 'tests' })} {questionNumber}</h3>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs bg-gray-200 dark:bg-slate-700 px-2 py-0.5 rounded-full text-gray-800 dark:text-gray-200">
                              {getQuestionTypeLabel(questionType)}
                            </span>
                            <span className={`text-xs px-2 py-0.5 rounded-full font-bold ${
                              isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                            }`}>
                              {isCorrect ? t('result.correct', { ns: 'tests' }) : t('result.incorrect', { ns: 'tests' })}
                            </span>
                          </div>
                        </div>

                        <div className="text-sm mt-1">
                          <span className="font-medium text-gray-700 dark:text-gray-300">{t('result.yourAnswer', { ns: 'tests' })}: </span>
                          <span className={isCorrect
                            ? 'text-green-700 dark:text-green-400'
                            : 'text-red-700 dark:text-red-400'
                          }>
                            {formatAnswer(userAnswer.answer)}
                            {isCorrect && <span className="ml-1 text-green-600 dark:text-green-400 font-bold">✓</span>}
                            {!isCorrect && <span className="ml-1 text-red-600 dark:text-red-400 font-bold">✗</span>}
                          </span>
                        </div>

                        {/* Display correct answer if the user's answer is incorrect */}
                        {!isCorrect && (
                          <div className="text-sm mt-1">
                            <span className="font-medium text-gray-700 dark:text-gray-300">{t('result.correctAnswer', { ns: 'tests', defaultValue: 'Correct answer' })}: </span>
                            <span className="text-green-700 dark:text-green-400 font-medium">
                              {userAnswer.correctAnswer ? formatAnswer(userAnswer.correctAnswer) : getCorrectAnswer(questionNumber, questionType)}
                            </span>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestResultView;
