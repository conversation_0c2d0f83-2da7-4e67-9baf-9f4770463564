import { useEffect, useState, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { useTestSession } from '../../contexts/TestSessionContext';
import { QuestionType, AnswerOption } from '../../types/test';
import { StudentAnswer } from '../../types/student-test';

// NOTE: These helper functions are kept for future reference but commented out as they're currently not used
/*
// Helper function to convert option to the corresponding letter
function getOptionLetter(option: string, allOptions: string[]): string {
  const index = allOptions.indexOf(option);
  if (index === -1) return '';
  // A, B, C, D, etc.
  return String.fromCharCode(65 + index);
}

// Helper function to convert letter to the corresponding option
function getOptionFromLetter(letter: string, allOptions: string[]): string {
  const index = letter.charCodeAt(0) - 65;
  if (index < 0 || index >= allOptions.length) return '';
  return allOptions[index];
}
*/

const TestTaking = () => {
  const { testId } = useParams<{ testId: string }>();
  const navigate = useNavigate();
  // Initialize with both namespaces
  const { t, i18n } = useTranslation(['tests', 'common']);
  const {
    session,
    isLoading,
    error,
    startTest,
    saveAnswer,
    toggleFlag,
    submitTest,
    pauseTest,
    navigateToQuestion,
  } = useTestSession();

  const [showConfirmSubmit, setShowConfirmSubmit] = useState(false);
  const [localAnswers, setLocalAnswers] = useState<Record<number, string>>({});
  const hasStartedTest = useRef(false);
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [activeTab, setActiveTab] = useState<'content' | 'answer'>('content');

  const formatTime = useCallback((seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  useEffect(() => {
    if (testId && !hasStartedTest.current) {
      hasStartedTest.current = true;
      startTest(testId);
    }
  }, [testId, startTest]);

  useEffect(() => {
    if (session?.progress.answers) {
      // Initialize localAnswers from all existing answers
      const answers: Record<number, string> = {};
      session.progress.answers.forEach(a => {
        answers[a.questionNumber] = Array.isArray(a.answer) 
          ? a.answer.join(', ')
          : a.answer;
      });
      setLocalAnswers(answers);
      
      // Enhanced debugging
      console.log('Session:', session);
      console.log('Session test questions:', session.test.questions);
      
      if (session.test.questions.length > 0) {
        // Log the entire first question to see all available properties
        console.log('First question:', session.test.questions[0]);
      }
      
      const mcQuestions = session.test.questions.filter(q => isMultipleChoice(q.type as string));
      console.log('Multiple choice questions:', mcQuestions);
      console.log('Text questions:', session.test.questions.filter(q => !isMultipleChoice(q.type as string)));
    }
  }, [session?.progress?.answers]);

  // Setup timer when session is loaded
  useEffect(() => {
    if (!session) return;
    
    // Calculate remaining time
    const startTime = new Date(session.progress.startedAt).getTime();
    const timeLimit = session.test.timeLimit * 60; // Convert minutes to seconds
    const elapsed = Math.floor((Date.now() - startTime) / 1000);
    const remaining = Math.max(0, timeLimit - elapsed);
    
    setTimeRemaining(remaining);
    
    // Start timer
    if (remaining > 0) {
      timerRef.current = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev === null || prev <= 1) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
            }
            if (prev === 1) {
              // Auto-submit when time is up
              handleSubmit();
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [session]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen dark:bg-slate-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-600 dark:text-red-400 p-4 dark:bg-slate-900">
        {error}
      </div>
    );
  }

  if (!session) {
    return (
      <div className="text-center text-gray-600 dark:text-gray-400 p-4 dark:bg-slate-900">
        {t('tests:take.noActiveSession', 'No active test session.')}
      </div>
    );
  }

  const handleAnswerChange = (questionNumber: number, value: string) => {
    setLocalAnswers(prev => ({
      ...prev,
      [questionNumber]: value
    }));
  };

  const handleMultipleChoiceAnswerChange = (questionNumber: number, optionIndex: number, options: string[]) => {
    // Get the actual option value or fall back to the option letter
    const optionValue = options[optionIndex] || Object.values(AnswerOption)[optionIndex];
    handleAnswerChange(questionNumber, optionValue);
    handleSaveAnswer(questionNumber, optionValue);
  };

  const handleSaveAnswer = async (questionNumber: number, value?: string) => {
    const answerValue = value || localAnswers[questionNumber];
    if (!answerValue || !answerValue.trim()) return;

    const question = session.test.questions[questionNumber - 1];
    if (!question) return;

    const answer: StudentAnswer = {
      questionNumber,
      answer: question.type === QuestionType.MultipleChoice
        ? answerValue
        : answerValue.split(',').map(a => a.trim()),
    };

    await saveAnswer(answer);
  };

  const handleSubmit = async () => {
    try {
      console.log("Starting test submission process");
      console.log("Current answers in session:", session.progress.answers);
      console.log("Local answers state:", localAnswers);
    
      // Save all answers first
      await handleSaveAllAnswers();
      console.log("All answers saved before submission");
      
      // Get unanswered questions
      const unansweredQuestions = session.test.questions.filter(
        (_, index) => !session.progress.answers.some(a => a.questionNumber === index + 1)
      );
      
      console.log("Unanswered questions:", unansweredQuestions.length);

      if (unansweredQuestions.length) {
        if (window.confirm(`You have ${unansweredQuestions.length} unanswered questions. Are you sure you want to submit?`)) {
          console.log("Submitting test with unanswered questions");
          const result = await submitTest();
          console.log("Test submission result:", result);
          navigate(`/student/tests/${testId}/result`);
        }
      } else {
        console.log("Submitting test with all questions answered");
        const result = await submitTest();
        console.log("Test submission result:", result);
        navigate(`/student/tests/${testId}/result`);
      }
    } catch (error) {
      console.error("Error submitting test:", error);
      alert("There was an error submitting your test. Please try again.");
    }
  };

  const handleSaveAllAnswers = async () => {
    console.log("Saving all answers before submission");
    const answerPromises = [];
    
    for (const questionNumber of Object.keys(localAnswers)) {
      const qNum = parseInt(questionNumber);
      console.log(`Saving answer for question ${qNum}: ${localAnswers[qNum]}`);
      answerPromises.push(handleSaveAnswer(qNum));
    }
    
    await Promise.all(answerPromises);
    console.log("All answers saved successfully");
  };

  const handlePause = async () => {
    await handleSaveAllAnswers();
    await pauseTest();
    navigate('/student/tests');
  };

  const isAnswered = (questionNumber: number) => {
    return session.progress.answers.some(a => a.questionNumber === questionNumber);
  };

  const isFlagged = (questionNumber: number) => {
    return session.progress.flaggedQuestions.includes(questionNumber);
  };

  // Get user's answer for a specific question
  const getAnswer = (questionNumber: number): string => {
    return localAnswers[questionNumber] || '';
  };
  
  // Helper function to check if the user has selected an option
  const isOptionSelected = (questionNumber: number, optionIndex: number, options: string[]): boolean => {
    const userAnswer = getAnswer(questionNumber);
    
    // Check if the answer is a direct match with the option value
    if (userAnswer === options[optionIndex]) {
      return true;
    }
    
    // Check if the answer is the letter (A, B, C, D) corresponding to this option
    const optionLetter = Object.values(AnswerOption)[optionIndex];
    if (userAnswer === optionLetter) {
      return true;
    }
    
    return false;
  };
  
  // Helper function to check if a question is multiple choice type
  const isMultipleChoice = (questionType: string): boolean => {
    // Log the question type for debugging
    console.log(`Question type: ${questionType}, type of: ${typeof questionType}`);
    
    const multipleChoiceValues = [
      QuestionType.MultipleChoice, 
      'MULTIPLE_CHOICE', 
      'multipleChoice',
      'multiple-choice',
      'multiple_choice'
    ];
    
    return multipleChoiceValues.includes(questionType);
  };
  
  // Group questions by type
  const multipleChoiceQuestions = session.test.questions.filter(q => 
    isMultipleChoice(q.type as string)
  );
  
  const textQuestions = session.test.questions.filter(q => 
    !isMultipleChoice(q.type as string)
  );

  return (
    <div className="container mx-auto px-4 py-8 dark:bg-slate-900 dark:text-white min-h-screen">
      <div className="mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl font-bold dark:text-white">{session.test.title}</h1>
        
        {/* Timer Display */}
        {timeRemaining !== null && (
          <div className={`px-4 py-2 rounded-lg font-mono text-lg ${
            timeRemaining < 300 
              ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' 
              : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
          }`}>
            {formatTime(timeRemaining)}
          </div>
        )}
        
        <div className="flex space-x-4">
          <button
            onClick={handlePause}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-300 dark:hover:text-white"
          >
            {t('tests:take.saveAndExit', 'Save & Exit')}
          </button>
          <button
            onClick={() => setShowConfirmSubmit(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            {t('tests:take.submit', 'Submit Test')}
          </button>
        </div>
      </div>

      {/* Mobile Tab Navigation */}
      <div className="lg:hidden mb-4 flex border-b dark:border-slate-700">
        <button
          onClick={() => setActiveTab('content')}
          className={`flex-1 py-2 px-4 font-medium ${
            activeTab === 'content' 
              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400' 
              : 'text-gray-500 dark:text-gray-400'
          }`}
        >
          {t('take.testContent')}
        </button>
        <button
          onClick={() => setActiveTab('answer')}
          className={`flex-1 py-2 px-4 font-medium ${
            activeTab === 'answer' 
              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400' 
              : 'text-gray-500 dark:text-gray-400'
          }`}
        >
          {t('take.answerSheet')}
        </button>
      </div>

      {/* Progress Summary - Added as separate section */}
      <div className="bg-white dark:bg-slate-800 p-4 rounded-lg shadow mb-4">
        <h2 className="text-lg font-semibold mb-3 text-gray-800 dark:text-white">{t('take.progressSummary')}</h2>
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-gray-50 dark:bg-slate-700 p-3 rounded-lg text-center shadow-sm">
            <div className="text-sm text-gray-700 dark:text-gray-100 mb-1 font-medium">{t('take.totalQuestions')}</div>
            <div className="text-xl font-bold text-gray-900 dark:text-white">{session.test.questions.length}</div>
          </div>
          <div className="bg-gray-50 dark:bg-slate-700 p-3 rounded-lg text-center shadow-sm">
            <div className="text-sm text-gray-700 dark:text-gray-100 mb-1 font-medium">{t('take.answered')}</div>
            <div className="text-xl font-bold text-gray-900 dark:text-white">{session.progress.answers.length}</div>
          </div>
          <div className="bg-gray-50 dark:bg-slate-700 p-3 rounded-lg text-center shadow-sm">
            <div className="text-sm text-gray-700 dark:text-gray-100 mb-1 font-medium">{t('take.flagged')}</div>
            <div className="text-xl font-bold text-gray-900 dark:text-white">{session.progress.flaggedQuestions.length}</div>
          </div>
        </div>
      </div>

      {/* Two-Panel Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Test Content - Scrollable */}
        <div className={`lg:col-span-2 bg-white dark:bg-slate-800 p-4 rounded-lg shadow ${activeTab !== 'content' && 'hidden lg:block'}`}>
          <h2 className="text-xl font-semibold mb-4 dark:text-white">{t('take.testContent')}</h2>
          <div className="prose prose-sm md:prose-base lg:prose-lg dark:prose-invert max-w-none overflow-y-auto max-h-[70vh] p-5 border dark:border-slate-700 rounded-lg dark:bg-slate-900/50">
            {session.test.content ? (
              <div 
                dangerouslySetInnerHTML={{ __html: session.test.content.replace(/color:\s*rgb\(0,\s*0,\s*0\)/g, 'color: currentColor') }} 
                className="whitespace-pre-wrap dark:text-slate-200 text-gray-900 test-content"
                style={{ 
                  lineHeight: '1.8', 
                  fontSize: '1.05rem',
                  fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                  unicodeBidi: 'embed',
                  letterSpacing: '0.015em',
                  textRendering: 'optimizeLegibility',
                  wordSpacing: '0.05em',
                  fontFeatureSettings: '"liga", "kern"',
                  // Use current language or content language
                  writingMode: session.test.language && ['ja', 'zh', 'ko'].includes(session.test.language) ? 'vertical-rl' : 'horizontal-tb'
                }}
                lang={session.test.language || i18n.language || 'en'}
                dir={session.test.language && ['ar', 'he', 'fa', 'ur'].includes(session.test.language) ? 'rtl' : 'ltr'}
              />
            ) : (
              <div className="italic text-gray-500 dark:text-gray-400">{t('take.noContent')}</div>
            )}
          </div>
        </div>
        
        {/* Answer Sheet - Fixed */}
        <div className={`lg:col-span-1 bg-white dark:bg-slate-800 p-4 rounded-lg shadow ${activeTab !== 'answer' && 'hidden lg:block'}`}>
          <h2 className="text-xl font-semibold mb-4 dark:text-white">{t('take.answerSheet')}</h2>
          <div className="space-y-6 overflow-y-auto max-h-[70vh] pr-2">
            
            {/* Multiple Choice Grid */}
            {multipleChoiceQuestions.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-3 dark:text-white">{t('take.multipleChoice')}</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white dark:bg-slate-800 border dark:border-slate-700">
                    <thead>
                      <tr className="bg-gray-50 dark:bg-slate-700">
                        <th className="py-2 px-3 border-b dark:border-slate-600 text-left font-medium dark:text-slate-300">Q#</th>
                        <th className="py-2 px-3 border-b dark:border-slate-600 text-center font-medium dark:text-slate-300">A</th>
                        <th className="py-2 px-3 border-b dark:border-slate-600 text-center font-medium dark:text-slate-300">B</th>
                        <th className="py-2 px-3 border-b dark:border-slate-600 text-center font-medium dark:text-slate-300">C</th>
                        <th className="py-2 px-3 border-b dark:border-slate-600 text-center font-medium dark:text-slate-300">D</th>
                        <th className="py-2 px-3 border-b dark:border-slate-600 text-center font-medium dark:text-slate-300 w-16">Flag</th>
                      </tr>
                    </thead>
                    <tbody>
                      {multipleChoiceQuestions.map((question, index) => {
                        const questionNumber = index + 1;
                        const options = question.options || ['Option A', 'Option B', 'Option C', 'Option D'];
                        
                        return (
                          <tr key={questionNumber} className={`hover:bg-gray-50 dark:hover:bg-slate-700/70 transition-colors ${isFlagged(questionNumber) ? 'bg-yellow-50 dark:bg-yellow-900/20' : ''}`}>
                            <td className="py-2 px-3 border-b dark:border-slate-600 font-medium cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors" 
                                onClick={() => navigateToQuestion(questionNumber)}>
                                {questionNumber}
                            </td>
                            {Object.values(AnswerOption).slice(0, 4).map((option, optIndex) => (
                              <td key={option} 
                                  className="py-2 px-3 border-b dark:border-slate-600 text-center cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors">
                                <input
                                  type="radio"
                                  name={`question-${questionNumber}`}
                                  checked={isOptionSelected(questionNumber, optIndex, options)}
                                  onChange={() => handleMultipleChoiceAnswerChange(questionNumber, optIndex, options)}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 cursor-pointer"
                                  aria-label={`Option ${option} for question ${questionNumber}`}
                                />
                              </td>
                            ))}
                            <td className="py-2 px-2 border-b dark:border-slate-600 text-center">
                              <button
                                onClick={() => toggleFlag(questionNumber)}
                                className={`p-1 rounded transition-colors ${isFlagged(questionNumber) ? 'bg-yellow-100 dark:bg-yellow-900/40 text-yellow-600 dark:text-yellow-400' : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'}`}
                                aria-label={isFlagged(questionNumber) ? 'Unflag question' : 'Flag question'}
                              >
                                {isFlagged(questionNumber) ? '🚩' : '⚑'}
                              </button>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
            
            {/* Text Input Questions */}
            {textQuestions.length > 0 && (
              <div>
                <h3 className="text-lg font-medium mb-3 dark:text-white">{t('take.fillInTheBlank')}</h3>
                <div className="space-y-4">
                  {textQuestions.map((_, index) => {
                    const questionNumber = index + multipleChoiceQuestions.length + 1;
                    
                    return (
                      <div 
                        key={questionNumber} 
                        className={`p-4 border dark:border-slate-600 rounded-lg transition-colors ${
                          isFlagged(questionNumber) ? 'border-yellow-400 dark:border-yellow-500 border-2 bg-yellow-50 dark:bg-yellow-900/20' : 'dark:bg-slate-700/50 hover:bg-gray-50 dark:hover:bg-slate-700'
                        }`}
                      >
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="font-medium dark:text-white">{t('take.question')} {questionNumber}</h4>
                          <button
                            onClick={() => toggleFlag(questionNumber)}
                            className={`px-2 py-1 text-xs rounded transition-colors ${
                              isFlagged(questionNumber)
                                ? 'bg-yellow-100 dark:bg-yellow-900/40 text-yellow-800 dark:text-yellow-300'
                                : 'bg-gray-100 dark:bg-slate-600 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-500'
                            }`}
                          >
                            {isFlagged(questionNumber) ? t('take.flagged') : t('take.flag')}
                          </button>
                        </div>
                        
                        <textarea
                          value={localAnswers[questionNumber] || ''}
                          onChange={(e) => handleAnswerChange(questionNumber, e.target.value)}
                          onBlur={() => handleSaveAnswer(questionNumber)}
                          placeholder={t('take.yourAnswer')}
                          className="w-full p-2 text-sm border dark:border-slate-600 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-800 text-gray-900 dark:text-white"
                          rows={2}
                        />
                        
                        <div className="flex justify-between items-center mt-2">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {isAnswered(questionNumber) ? t('take.answered') : t('take.notAnswered')}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Submit Confirmation Modal */}
      {showConfirmSubmit && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-slate-800 p-6 rounded-lg max-w-md w-full shadow-xl">
            <h3 className="text-xl font-bold mb-4 dark:text-white">{t('take.submitTest')}</h3>
            <p className="mb-6 dark:text-gray-200">{t('take.submitConfirmation')}</p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowConfirmSubmit(false)}
                className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors"
              >
                {t('buttons.cancel', { ns: 'common' })}
              </button>
              <button
                onClick={handleSubmit}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"
              >
                {t('take.submit')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TestTaking; 