import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { categoriesApi } from '../../api/categoriesApi';
import { Category } from '../../types/category';
import { UserRole } from '../../types/auth';
import { useAuth } from '../../hooks/useAuth';

const CategoriesList = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const data = await categoriesApi.getAll();
      setCategories(data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Lỗi khi tải danh sách danh mục');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const handleToggleActive = async (id: string) => {
    try {
      await categoriesApi.toggleActive(id);
      // Cập nhật lại danh sách
      fetchCategories();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Lỗi khi thay đổi trạng thái danh mục');
      console.error(err);
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa danh mục này?')) {
      try {
        await categoriesApi.delete(id);
        // Cập nhật lại danh sách
        fetchCategories();
      } catch (err: any) {
        setError(err.response?.data?.message || 'Lỗi khi xóa danh mục');
        console.error(err);
      }
    }
  };

  if (loading) return <div className="text-center p-4 dark:text-white">Đang tải...</div>;
  if (error) return <div className="text-red-500 dark:text-red-400 p-4 text-center">{error}</div>;

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold dark:text-white">Danh sách danh mục</h1>
          {user?.role === UserRole.TEACHER && (
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Chỉ hiển thị danh mục của bạn
            </span>
          )}
        </div>
        {(user?.role === UserRole.ADMIN || user?.role === UserRole.TEACHER) && (
          <Link
            to="/categories/create"
            className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
          >
            Tạo danh mục mới
          </Link>
        )}
      </div>

      {categories.length === 0 ? (
        <div className="text-center p-4 dark:text-slate-300">Không có danh mục nào</div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700">
            <thead className="bg-gray-100 dark:bg-slate-700">
              <tr>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">Tên</th>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">Mô tả</th>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">Người tạo</th>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">Trạng thái</th>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">Hành động</th>
              </tr>
            </thead>
            <tbody>
              {categories.map((category) => (
                <tr key={category.id} className="hover:bg-gray-50 dark:hover:bg-slate-700">
                  <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 dark:text-white">{category.name}</td>
                  <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 dark:text-white">{category.description || '-'}</td>
                  <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 dark:text-white">
                    {category.createdBy && typeof category.createdBy !== 'string' ?
                      `${category.createdBy.firstName} ${category.createdBy.lastName}` :
                      'Không xác định'}
                  </td>
                  <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600">
                    <span
                      className={`px-2 py-1 rounded text-sm ${
                        category.isActive
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                      }`}
                    >
                      {category.isActive ? 'Kích hoạt' : 'Vô hiệu'}
                    </span>
                  </td>
                  <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600">
                    <div className="flex space-x-2">
                      <Link
                        to={`/categories/${category.id}`}
                        className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        Xem
                      </Link>
                      {(user?.role === UserRole.ADMIN ||
                        (user?.role === UserRole.TEACHER)) && (
                        <>
                          <Link
                            to={`/categories/${category.id}/edit`}
                            className="text-yellow-500 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-300"
                          >
                            Sửa
                          </Link>
                          <button
                            onClick={() => handleToggleActive(category.id)}
                            className="text-purple-500 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
                          >
                            {category.isActive ? 'Vô hiệu hóa' : 'Kích hoạt'}
                          </button>
                          {user?.role === UserRole.ADMIN && (
                            <button
                              onClick={() => handleDelete(category.id)}
                              className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                            >
                              Xóa
                            </button>
                          )}
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default CategoriesList;