import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { categoriesApi } from '../../api/categoriesApi';
import { Category } from '../../types/category';
import { UserRole } from '../../types/auth';
import { useAuth } from '../../contexts/AuthContext';

const CategoryDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [category, setCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    const fetchCategory = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await categoriesApi.getById(id);
        setCategory(data);
        setError(null);
      } catch (err: any) {
        setError(err.response?.data?.message || 'Lỗi khi tải thông tin danh mục');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchCategory();
  }, [id]);

  const handleToggleActive = async () => {
    if (!id) return;

    try {
      await categoriesApi.toggleActive(id);
      // Cập nhật lại thông tin
      const data = await categoriesApi.getById(id);
      setCategory(data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Lỗi khi thay đổi trạng thái danh mục');
      console.error(err);
    }
  };

  const handleDelete = async () => {
    if (!id) return;

    if (window.confirm('Bạn có chắc chắn muốn xóa danh mục này?')) {
      try {
        await categoriesApi.delete(id);
        navigate('/categories');
      } catch (err: any) {
        setError(err.response?.data?.message || 'Lỗi khi xóa danh mục');
        console.error(err);
      }
    }
  };

  if (loading) return <div className="text-center p-4 dark:text-white">Đang tải...</div>;
  if (error) return <div className="text-red-500 dark:text-red-400 p-4 text-center">{error}</div>;
  if (!category) return <div className="text-center p-4 dark:text-white">Không tìm thấy danh mục</div>;

  const canModify =
    user?.role === UserRole.ADMIN ||
    (user?.role === UserRole.TEACHER &&
     typeof category.createdBy === 'object' &&
     category.createdBy.id === user?.id);

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6">
        <Link to="/categories" className="text-blue-500 hover:text-blue-700">
          &larr; Quay lại danh sách
        </Link>
      </div>

      <div className="bg-white dark:bg-slate-800 shadow-md rounded-lg p-6 max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-4 dark:text-white">{category.name}</h1>

        <div className="mb-4">
          <div className="flex items-center mb-2">
            <span className="font-medium mr-2 dark:text-white">Trạng thái:</span>
            <span
              className={`px-2 py-1 rounded text-sm ${
                category.isActive
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
              }`}
            >
              {category.isActive ? 'Kích hoạt' : 'Vô hiệu'}
            </span>
          </div>

          <div className="mb-2">
            <span className="font-medium dark:text-white">Mô tả:</span>
            <p className="mt-1 dark:text-gray-300">{category.description || 'Không có mô tả'}</p>
          </div>

          <div className="mb-2">
            <span className="font-medium dark:text-white">Người tạo:</span>
            <p className="mt-1 dark:text-gray-300">
              {typeof category.createdBy === 'object'
                ? `${category.createdBy.firstName} ${category.createdBy.lastName}`
                : 'Không xác định'}
            </p>
          </div>

          <div className="mb-2">
            <span className="font-medium dark:text-white">Ngày tạo:</span>
            <p className="mt-1 dark:text-gray-300">
              {new Date(category.createdAt).toLocaleDateString('vi-VN')}
            </p>
          </div>

          <div className="mb-2">
            <span className="font-medium dark:text-white">Ngày cập nhật:</span>
            <p className="mt-1 dark:text-gray-300">
              {new Date(category.updatedAt).toLocaleDateString('vi-VN')}
            </p>
          </div>
        </div>

        {canModify && (
          <div className="flex gap-2 mt-6">
            <Link
              to={`/categories/${category.id}/edit`}
              className="bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded"
            >
              Chỉnh sửa
            </Link>
            <button
              onClick={handleToggleActive}
              className="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded"
            >
              {category.isActive ? 'Vô hiệu hóa' : 'Kích hoạt'}
            </button>
            {user?.role === UserRole.ADMIN && (
              <button
                onClick={handleDelete}
                className="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded"
              >
                Xóa
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryDetail;