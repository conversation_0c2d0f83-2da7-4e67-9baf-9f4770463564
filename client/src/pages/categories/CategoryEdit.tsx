import { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { categoriesApi } from '../../api/categoriesApi';
import { UpdateCategoryDto } from '../../types/category';

const CategoryEdit = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [formData, setFormData] = useState<UpdateCategoryDto>({
    name: '',
    description: '',
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategory = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await categoriesApi.getById(id);
        setFormData({
          name: data.name,
          description: data.description,
        });
        setError(null);
      } catch (err: any) {
        setError(err.response?.data?.message || 'Lỗi khi tải thông tin danh mục');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchCategory();
  }, [id]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id) return;

    setSubmitting(true);
    setError(null);

    try {
      await categoriesApi.update(id, formData);
      navigate(`/categories/${id}`);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Lỗi khi cập nhật danh mục');
      console.error(err);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) return <div className="text-center p-4 dark:text-white">Đang tải...</div>;

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6">
        <Link to={`/categories/${id}`} className="text-blue-500 hover:text-blue-700">
          &larr; Quay lại chi tiết
        </Link>
      </div>

      <h1 className="text-2xl font-bold mb-6 dark:text-white">Chỉnh sửa danh mục</h1>

      {error && <div className="bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 p-3 rounded mb-4">{error}</div>}

      <form onSubmit={handleSubmit} className="max-w-lg">
        <div className="mb-4">
          <label htmlFor="name" className="block mb-1 font-medium dark:text-white">
            Tên danh mục <span className="text-red-500 dark:text-red-400">*</span>
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-white"
          />
        </div>

        <div className="mb-4">
          <label htmlFor="description" className="block mb-1 font-medium dark:text-white">
            Mô tả
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-slate-700 dark:text-white"
          />
        </div>

        <div className="flex gap-3">
          <button
            type="submit"
            disabled={submitting}
            className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300 dark:disabled:bg-blue-800"
          >
            {submitting ? 'Đang xử lý...' : 'Cập nhật'}
          </button>
          <button
            type="button"
            onClick={() => navigate(`/categories/${id}`)}
            className="bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-white py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Hủy
          </button>
        </div>
      </form>
    </div>
  );
};

export default CategoryEdit;