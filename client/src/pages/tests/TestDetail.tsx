import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { testsApi } from '../../api/testsApi';
import { Test, TestType, TestDifficulty } from '../../types/test';
import { UserRole } from '../../types/user';
import { useAuth } from '../../hooks/useAuth';

const TestDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [test, setTest] = useState<Test | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation(['tests', 'common']);

  useEffect(() => {
    const fetchTest = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await testsApi.getById(id);
        setTest(data);
        setError(null);
      } catch (err: any) {
        setError(err.response?.data?.message || t('common:messages.error'));
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchTest();
  }, [id]);

  const handleToggleActive = async () => {
    if (!id || !test) return;

    try {
      await testsApi.toggleActive(id);
      // Cập nhật lại thông tin
      const data = await testsApi.getById(id);
      setTest(data);
    } catch (err: any) {
      setError(err.response?.data?.message || t('common:messages.error'));
      console.error(err);
    }
  };

  const handleTogglePublish = async () => {
    if (!id || !test) return;

    try {
      await testsApi.togglePublished(id);
      // Cập nhật lại thông tin
      const data = await testsApi.getById(id);
      setTest(data);
    } catch (err: any) {
      setError(err.response?.data?.message || t('common:messages.error'));
      console.error(err);
    }
  };

  const handleDelete = async () => {
    if (!id) return;

    if (window.confirm(t('tests:list.confirmDelete'))) {
      try {
        await testsApi.delete(id);
        navigate('/tests');
      } catch (err: any) {
        setError(err.response?.data?.message || t('common:messages.error'));
        console.error(err);
      }
    }
  };

  const getTestTypeName = (type: TestType) => {
    switch (type) {
      case TestType.READING:
        return t('tests:types.reading');
      case TestType.LISTENING:
        return t('tests:types.listening');
      case TestType.WRITING:
        return t('tests:types.writing');
      case TestType.SPEAKING:
        return t('tests:types.speaking');
      case TestType.FULL_TEST:
        return t('tests:types.fullTest');
      default:
        return type;
    }
  };

  const getDifficultyName = (difficulty: TestDifficulty) => {
    switch (difficulty) {
      case TestDifficulty.EASY:
        return t('tests:difficulties.easy');
      case TestDifficulty.MEDIUM:
        return t('tests:difficulties.medium');
      case TestDifficulty.HARD:
        return t('tests:difficulties.hard');
      default:
        return difficulty;
    }
  };

  const canModify =
    test && (
      user?.role === UserRole.ADMIN ||
      (user?.role === UserRole.TEACHER &&
        typeof test.createdBy === 'object' &&
        test.createdBy._id === user?._id)
    );

  if (loading) return <div className="text-center p-4 dark:text-white">{t('common:messages.loading')}</div>;
  if (error) return <div className="text-red-500 dark:text-red-400 p-4 text-center">{error}</div>;
  if (!test) return <div className="text-center p-4 dark:text-white">{t('common:messages.notFound')}</div>;

  return (
    <div className="container mx-auto p-4 dark:bg-slate-900">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold dark:text-white">{test.title}</h1>
        <div className="flex gap-2">
          <Link
            to="/tests"
            className="bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700 text-white py-2 px-4 rounded transition-colors"
          >
            {t('common:buttons.back')}
          </Link>
          {canModify && (
            <>
              <Link
                to={`/tests/${id}/edit`}
                className="bg-yellow-500 hover:bg-yellow-600 dark:bg-yellow-600 dark:hover:bg-yellow-700 text-white py-2 px-4 rounded transition-colors"
              >
                {t('common:buttons.edit')}
              </Link>
              <button
                onClick={handleTogglePublish}
                className="bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white py-2 px-4 rounded transition-colors"
              >
                {test.isPublished ? t('tests:list.actions.unpublish') : t('tests:list.actions.publish')}
              </button>
              <button
                onClick={handleToggleActive}
                className="bg-purple-500 hover:bg-purple-600 dark:bg-purple-600 dark:hover:bg-purple-700 text-white py-2 px-4 rounded transition-colors"
              >
                {test.isActive ? t('tests:list.actions.deactivate') : t('tests:list.actions.activate')}
              </button>
              {user?.role === UserRole.ADMIN && (
                <button
                  onClick={handleDelete}
                  className="bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white py-2 px-4 rounded transition-colors"
                >
                  {t('common:buttons.delete')}
                </button>
              )}
            </>
          )}
          {test.isPublished && test.isActive && (
            <Link
              to={`/tests/${id}/take`}
              className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors"
            >
              {t('tests:list.actions.take')}
            </Link>
          )}
        </div>
      </div>

      <div className="bg-white dark:bg-slate-800 shadow-md rounded p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h2 className="text-lg font-semibold mb-2 dark:text-white">{t('tests:detail.generalInfo')}</h2>
            <div className="space-y-2 dark:text-gray-300">
              <p><span className="font-medium dark:text-white">{t('tests:fields.type')}:</span> {getTestTypeName(test.type)}</p>
              <p><span className="font-medium dark:text-white">{t('tests:fields.difficulty')}:</span> {getDifficultyName(test.difficulty)}</p>
              <p>
                <span className="font-medium dark:text-white">{t('tests:fields.category')}:</span> {
                  typeof test.category === 'object'
                    ? test.category.name
                    : t('tests:detail.unknown')
                }
              </p>
              <p>
                <span className="font-medium dark:text-white">{t('tests:fields.timeLimit')}:</span> {test.timeLimit} {t('tests:fields.minutes')}
              </p>
              <p>
                <span className="font-medium dark:text-white">{t('tests:fields.maxScore')}:</span> {test.maxScore} {t('tests:fields.points')}
              </p>
              <p>
                <span className="font-medium dark:text-white">{t('tests:list.columns.published')}:</span>{' '}
                <span
                  className={`px-2 py-1 rounded text-sm ${
                    test.isPublished
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                  }`}
                >
                  {test.isPublished ? t('tests:list.status.published') : t('tests:list.status.draft')}
                </span>
              </p>
              <p>
                <span className="font-medium dark:text-white">{t('tests:list.columns.status')}:</span>{' '}
                <span
                  className={`px-2 py-1 rounded text-sm ${
                    test.isActive
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  }`}
                >
                  {test.isActive ? t('tests:list.status.active') : t('tests:list.status.inactive')}
                </span>
              </p>
            </div>
          </div>
          <div>
            <h2 className="text-lg font-semibold mb-2 dark:text-white">{t('tests:detail.creatorInfo')}</h2>
            <div className="space-y-2 dark:text-gray-300">
              <p>
                <span className="font-medium dark:text-white">{t('tests:detail.createdBy')}:</span> {
                  typeof test.createdBy === 'object'
                    ? `${test.createdBy.firstName} ${test.createdBy.lastName}`
                    : t('tests:detail.unknown')
                }
              </p>
              <p>
                <span className="font-medium dark:text-white">{t('tests:detail.createdAt')}:</span> {
                  new Date(test.createdAt).toLocaleDateString(i18n.language, {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })
                }
              </p>
              <p>
                <span className="font-medium dark:text-white">{t('tests:detail.updatedAt')}:</span> {
                  new Date(test.updatedAt).toLocaleDateString(i18n.language, {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-slate-800 shadow-md rounded p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4 dark:text-white">{t('tests:detail.description')}</h2>
        <div className="prose max-w-none dark:text-gray-300">
          {test.description || t('tests:detail.noDescription')}
        </div>
      </div>

      <div className="bg-white dark:bg-slate-800 shadow-md rounded p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4 dark:text-white">{t('tests:detail.instructions')}</h2>
        <div className="prose max-w-none dark:text-gray-300">
          {test.instructions || t('tests:detail.noInstructions')}
        </div>
      </div>

      <div className="bg-white dark:bg-slate-800 shadow-md rounded p-6">
        <h2 className="text-lg font-semibold mb-4 dark:text-white">{t('tests:detail.structure')}</h2>
        {test.sections && test.sections.length > 0 ? (
          <div className="space-y-4">
            {test.sections.map((section, index) => (
              <div key={index} className="border dark:border-slate-700 rounded p-4 dark:bg-slate-700">
                <h3 className="font-medium mb-2 dark:text-white">{t('tests:detail.section')} {index + 1}: {section.title}</h3>
                <p className="mb-2 dark:text-gray-300">{section.description}</p>
                <p className="dark:text-gray-300"><span className="font-medium dark:text-white">{t('tests:detail.questionCount')}:</span> {section.questions?.length || 0}</p>
              </div>
            ))}
          </div>
        ) : (
          <p className="dark:text-gray-300">{t('tests:detail.noStructure')}</p>
        )}
      </div>
    </div>
  );
};

export default TestDetail;