import { useState, useEffect, ChangeEvent, FormEvent, useCallback } from 'react';
import { useNavigate, usePara<PERSON>, Link } from 'react-router-dom';
import { testsApi } from '../../api/testsApi';
import { categoriesApi } from '../../api/categoriesApi';
import { Category } from '../../types/category';
import { Test, TestType, TestDifficulty } from '../../types/test';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types/auth';
import TextEditor from '../../components/TextEditor';

interface FormData {
  title: string;
  description: string;
  instructions: string;
  type: TestType;
  difficulty: TestDifficulty;
  categoryId: string;
  timeLimit: number;
  maxScore: number;
}

const TestEdit = () => {
  const { id } = useParams<{ id: string }>();
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    instructions: '',
    type: TestType.READING,
    difficulty: TestDifficulty.MEDIUM,
    categoryId: '',
    timeLimit: 60,
    maxScore: 100,
  });

  const [test, setTest] = useState<Test | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { user } = useAuth();

  useEffect(() => {
    const checkPermission = () => {
      if (!user) return;
      if (user.role !== UserRole.ADMIN && user.role !== UserRole.TEACHER) {
        navigate('/');
      }
    };
    checkPermission();
  }, [user, navigate]);

  const fetchTest = useCallback(async () => {
    try {
      setLoading(true);
      if (!id) return;
      
      const test = await testsApi.getById(id);
      
      // Check edit permissions
      if (user?.role !== UserRole.ADMIN) {
        // Using type assertion to handle the createdBy property
        const testCreator = test as unknown as { createdBy?: { id?: string } };
        if (testCreator.createdBy && typeof testCreator.createdBy === 'object' && 'id' in testCreator.createdBy) {
          if (testCreator.createdBy.id !== user?.id) {
            toast.error('Bạn không có quyền chỉnh sửa bài thi này');
            navigate('/tests');
            return;
          }
        }
      }

      setTest(test);
      setCategories(await categoriesApi.getAll());
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Lỗi khi tải thông tin bài thi');
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, [id, navigate, user]);

  useEffect(() => {
    fetchTest();
  }, [fetchTest]);

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    
    // Xử lý các trường số
    if (name === 'timeLimit' || name === 'maxScore') {
      const numValue = parseInt(value);
      setFormData(prev => ({
        ...prev,
        [name]: isNaN(numValue) ? 0 : numValue,
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // Add a new handler for the rich text editor
  const handleEditorChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!id) return;
    
    if (!formData.title.trim()) {
      setError('Vui lòng nhập tiêu đề bài kiểm tra');
      return;
    }

    if (!formData.categoryId) {
      setError('Vui lòng chọn danh mục');
      return;
    }

    try {
      setSubmitting(true);
      await testsApi.update(id, formData);
      navigate(`/tests/${id}`);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Lỗi khi cập nhật bài kiểm tra');
      console.error(err);
      setSubmitting(false);
    }
  };

  if (loading) return <div className="text-center p-4">Đang tải...</div>;
  if (!test && !loading) return <div className="text-center p-4">Không tìm thấy bài kiểm tra</div>;

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Chỉnh sửa bài kiểm tra</h1>
        <div className="flex gap-2">
          <Link
            to={`/tests/${id}`}
            className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded"
          >
            Hủy
          </Link>
          <Link
            to={`/tests/${id}/sections`}
            className="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded"
          >
            Quản lý nội dung
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white shadow-md rounded p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="col-span-2">
            <label className="block text-gray-700 font-medium mb-2" htmlFor="title">
              Tiêu đề <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-gray-700 font-medium mb-2" htmlFor="type">
              Loại bài kiểm tra <span className="text-red-500">*</span>
            </label>
            <select
              id="type"
              name="type"
              value={formData.type}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              required
            >
              <option value={TestType.READING}>Đọc</option>
              <option value={TestType.LISTENING}>Nghe</option>
              <option value={TestType.WRITING}>Viết</option>
              <option value={TestType.SPEAKING}>Nói</option>
              <option value={TestType.FULL_TEST}>Bài thi đầy đủ</option>
            </select>
          </div>

          <div>
            <label className="block text-gray-700 font-medium mb-2" htmlFor="difficulty">
              Độ khó <span className="text-red-500">*</span>
            </label>
            <select
              id="difficulty"
              name="difficulty"
              value={formData.difficulty}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              required
            >
              <option value={TestDifficulty.EASY}>Dễ</option>
              <option value={TestDifficulty.MEDIUM}>Trung bình</option>
              <option value={TestDifficulty.HARD}>Khó</option>
            </select>
          </div>

          <div>
            <label className="block text-gray-700 font-medium mb-2" htmlFor="categoryId">
              Danh mục <span className="text-red-500">*</span>
            </label>
            <select
              id="categoryId"
              name="categoryId"
              value={formData.categoryId}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              required
            >
              <option value="">-- Chọn danh mục --</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-gray-700 font-medium mb-2" htmlFor="timeLimit">
              Thời gian làm bài (phút) <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="timeLimit"
              name="timeLimit"
              min="1"
              value={formData.timeLimit}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-gray-700 font-medium mb-2" htmlFor="maxScore">
              Điểm tối đa <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="maxScore"
              name="maxScore"
              min="1"
              value={formData.maxScore}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              required
            />
          </div>

          <div className="col-span-2">
            <label className="block text-gray-700 font-medium mb-2" htmlFor="description">
              Mô tả
            </label>
            <TextEditor
              value={formData.description || ''}
              onChange={(value) => handleEditorChange('description', value)}
              placeholder="Nhập mô tả bài thi..."
              className="h-48 mb-2"
            />
          </div>

          <div className="col-span-2">
            <label className="block text-gray-700 font-medium mb-2" htmlFor="instructions">
              Hướng dẫn làm bài
            </label>
            <TextEditor
              value={formData.instructions || ''}
              onChange={(value) => handleEditorChange('instructions', value)}
              placeholder="Nhập hướng dẫn làm bài..."
              className="h-48 mb-2"
            />
          </div>

          <div className="col-span-2 flex justify-end gap-2">
            <Link
              to={`/tests/${id}`}
              className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded"
            >
              Hủy
            </Link>
            <button
              type="submit"
              disabled={submitting}
              className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded disabled:opacity-50"
            >
              {submitting ? 'Đang lưu...' : 'Lưu thay đổi'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default TestEdit; 