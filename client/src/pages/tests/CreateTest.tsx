import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import { testsApi } from '../../api/testsApi';
import { TestType, TestDifficulty } from '../../types/test';

const CreateTest = () => {
  const navigate = useNavigate();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [instructions, setInstructions] = useState('');
  const [type, setType] = useState<TestType>(TestType.READING);
  const [difficulty, setDifficulty] = useState<TestDifficulty>(TestDifficulty.MEDIUM);
  const [categoryId, setCategoryId] = useState('');
  const [timeLimit, setTimeLimit] = useState(60);
  const [maxScore, setMaxScore] = useState(100);
  const [error, setError] = useState('');

  const createTestMutation = useMutation({
    mutationFn: (data: any) => testsApi.create(data),
    onSuccess: () => {
      navigate('/tests');
    },
    onError: (err: any) => {
      setError(err.response?.data?.message || 'Failed to create test');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createTestMutation.mutate({
      title,
      description,
      instructions,
      type,
      difficulty,
      categoryId,
      timeLimit,
      maxScore
    });
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Create New Test</h1>
      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        {error && <div className="text-red-500 mb-4">{error}</div>}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700">Title</label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="mt-1 form-input"
            required
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700">Description</label>
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="mt-1 form-textarea"
            rows={3}
            required
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700">Instructions</label>
          <textarea
            value={instructions}
            onChange={(e) => setInstructions(e.target.value)}
            className="mt-1 form-textarea"
            rows={3}
            required
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700">Type</label>
          <select
            value={type}
            onChange={(e) => setType(e.target.value as TestType)}
            className="mt-1 form-select"
          >
            {Object.values(TestType).map((type) => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700">Difficulty</label>
          <select
            value={difficulty}
            onChange={(e) => setDifficulty(e.target.value as TestDifficulty)}
            className="mt-1 form-select"
          >
            {Object.values(TestDifficulty).map((diff) => (
              <option key={diff} value={diff}>{diff}</option>
            ))}
          </select>
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700">Category ID</label>
          <input
            type="text"
            value={categoryId}
            onChange={(e) => setCategoryId(e.target.value)}
            className="mt-1 form-input"
            required
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700">Time Limit (minutes)</label>
          <input
            type="number"
            value={timeLimit}
            onChange={(e) => setTimeLimit(Number(e.target.value))}
            className="mt-1 form-input"
            required
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700">Max Score</label>
          <input
            type="number"
            value={maxScore}
            onChange={(e) => setMaxScore(Number(e.target.value))}
            className="mt-1 form-input"
            required
          />
        </div>
        <button
          type="submit"
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          disabled={createTestMutation.isPending}
        >
          {createTestMutation.isPending ? 'Creating...' : 'Create Test'}
        </button>
      </form>
    </div>
  );
};

export default CreateTest;