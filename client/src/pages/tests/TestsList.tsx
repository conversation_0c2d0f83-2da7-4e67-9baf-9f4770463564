import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { testsApi } from '../../api/testsApi';
import { Test, TestType, TestDifficulty } from '../../types/test';
import { UserRole } from '../../types/user';
import { useAuth } from '../../hooks/useAuth';

const TestsList = () => {
  const [tests, setTests] = useState<Test[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { t } = useTranslation(['tests', 'common']);

  useEffect(() => {
    const fetchTests = async () => {
      try {
        setLoading(true);
        const data = await testsApi.getAll();
        setTests(data);
        setError(null);
      } catch (err: any) {
        setError(err.response?.data?.message || t('common:messages.error'));
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchTests();
  }, []);

  const handleToggleActive = async (id: string) => {
    try {
      await testsApi.toggleActive(id);
      // Cập nhật lại danh sách
      const data = await testsApi.getAll();
      setTests(data);
    } catch (err: any) {
      setError(err.response?.data?.message || t('common:messages.error'));
      console.error(err);
    }
  };

  const handleTogglePublish = async (id: string) => {
    try {
      await testsApi.togglePublished(id);
      // Cập nhật lại danh sách
      const data = await testsApi.getAll();
      setTests(data);
    } catch (err: any) {
      setError(err.response?.data?.message || t('common:messages.error'));
      console.error(err);
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm(t('tests:list.confirmDelete'))) {
      try {
        await testsApi.delete(id);
        // Cập nhật lại danh sách
        const data = await testsApi.getAll();
        setTests(data);
      } catch (err: any) {
        setError(err.response?.data?.message || t('common:messages.error'));
        console.error(err);
      }
    }
  };

  const getTestTypeName = (type: TestType) => {
    switch (type) {
      case TestType.READING:
        return t('tests:types.reading');
      case TestType.LISTENING:
        return t('tests:types.listening');
      case TestType.WRITING:
        return t('tests:types.writing');
      case TestType.SPEAKING:
        return t('tests:types.speaking');
      case TestType.FULL_TEST:
        return t('tests:types.fullTest');
      default:
        return type;
    }
  };

  const getDifficultyName = (difficulty: TestDifficulty) => {
    switch (difficulty) {
      case TestDifficulty.EASY:
        return t('tests:difficulties.easy');
      case TestDifficulty.MEDIUM:
        return t('tests:difficulties.medium');
      case TestDifficulty.HARD:
        return t('tests:difficulties.hard');
      default:
        return difficulty;
    }
  };

  if (loading) return <div className="text-center p-4 dark:text-white">{t('common:messages.loading')}</div>;
  if (error) return <div className="text-red-500 dark:text-red-400 p-4 text-center">{error}</div>;

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold dark:text-white">{t('tests:list.title')}</h1>
          {user?.role === UserRole.TEACHER && (
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {t('tests:list.showingYourTests')}
            </span>
          )}
        </div>
        {(user?.role === UserRole.ADMIN || user?.role === UserRole.TEACHER) && (
          <Link
            to="/tests/create"
            className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
          >
            {t('tests:list.createNew')}
          </Link>
        )}
      </div>

      {tests.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12">
          <svg
            className="w-64 h-64 mb-4 text-gray-400 dark:text-slate-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p className="text-xl text-gray-600 dark:text-slate-400 mb-4">{t('tests:list.noTests')}</p>
          {(user?.role === UserRole.ADMIN || user?.role === UserRole.TEACHER) && (
            <Link
              to="/tests/create"
              className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
            >
              {t('tests:list.createFirst')}
            </Link>
          )}
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700">
            <thead className="bg-gray-100 dark:bg-slate-700">
              <tr>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">{t('tests:list.columns.title')}</th>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">{t('tests:list.columns.type')}</th>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">{t('tests:list.columns.difficulty')}</th>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">{t('tests:list.columns.category')}</th>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">{t('tests:list.columns.creator')}</th>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">{t('tests:list.columns.published')}</th>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">{t('tests:list.columns.status')}</th>
                <th className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 text-left dark:text-white">{t('tests:list.columns.actions')}</th>
              </tr>
            </thead>
            <tbody>
              {tests.map((test) => {
                const canModify =
                  user?.role === UserRole.ADMIN ||
                  (user?.role === UserRole.TEACHER &&
                    typeof test.createdBy === 'object' &&
                    test.createdBy._id === user?._id);

                return (
                  <tr key={test.id} className="hover:bg-gray-50 dark:hover:bg-slate-700">
                    <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 dark:text-white">{test.title}</td>
                    <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 dark:text-white">{getTestTypeName(test.type)}</td>
                    <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 dark:text-white">{getDifficultyName(test.difficulty)}</td>
                    <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 dark:text-white">
                      {typeof test.category === 'object' ? test.category.name : t('tests:detail.unknown')}
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600 dark:text-white">
                      {typeof test.createdBy === 'object'
                        ? `${test.createdBy.firstName} ${test.createdBy.lastName}`
                        : t('tests:detail.unknown')}
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600">
                      <span
                        className={`px-2 py-1 rounded text-sm ${
                          test.isPublished
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                        }`}
                      >
                        {test.isPublished ? t('tests:list.status.published') : t('tests:list.status.draft')}
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600">
                      <span
                        className={`px-2 py-1 rounded text-sm ${
                          test.isActive
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                            : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                        }`}
                      >
                        {test.isActive ? t('tests:list.status.active') : t('tests:list.status.inactive')}
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200 dark:border-slate-600">
                      <div className="flex space-x-2">
                        <Link
                          to={`/tests/${test.id}`}
                          className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          {t('tests:list.actions.view')}
                        </Link>
                        {canModify && (
                          <>
                            <Link
                              to={`/tests/${test.id}/edit`}
                              className="text-yellow-500 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-300"
                            >
                              {t('tests:list.actions.edit')}
                            </Link>
                            <button
                              onClick={() => handleTogglePublish(test.id)}
                              className="text-green-500 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                            >
                              {test.isPublished ? t('tests:list.actions.unpublish') : t('tests:list.actions.publish')}
                            </button>
                            <button
                              onClick={() => handleToggleActive(test.id)}
                              className="text-purple-500 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
                            >
                              {test.isActive ? t('tests:list.actions.deactivate') : t('tests:list.actions.activate')}
                            </button>
                            {user?.role === UserRole.ADMIN && (
                              <button
                                onClick={() => handleDelete(test.id)}
                                className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                              >
                                {t('tests:list.actions.delete')}
                              </button>
                            )}
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default TestsList;