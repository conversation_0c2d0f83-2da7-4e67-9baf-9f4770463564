import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { testsApi } from '../../api/testsApi';
import { Test, TestDifficulty, TestType, AnswerOption, MultipleChoiceAnswer, TextAnswer, AnswerSheet } from '../../types/test';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import React from 'react';
import TextEditor from '../../components/TextEditor';

// Helper function to parse answerSheet data if it's returned as strings
const parseAnswerSheet = (test: Test): Test => {
  const parsedTest = { ...test };

  if (test.answerSheet) {
    const answerSheet = { ...test.answerSheet };

    // Parse multipleChoiceAnswers if it's a string
    if (typeof answerSheet.multipleChoiceAnswers === 'string') {
      answerSheet.multipleChoiceAnswers = JSON.parse(answerSheet.multipleChoiceAnswers as unknown as string) as MultipleChoiceAnswer[];
    }

    // Parse textAnswers if it's a string
    if (typeof answerSheet.textAnswers === 'string') {
      answerSheet.textAnswers = JSON.parse(answerSheet.textAnswers as unknown as string) as TextAnswer[];
    }

    parsedTest.answerSheet = answerSheet;
  }

  return parsedTest;
};

const TestDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [formData, setFormData] = useState<Partial<Test>>({});
  const [isFormInitialized, setIsFormInitialized] = useState(false);
  const [newQuestionCount, setNewQuestionCount] = useState(1);
  const [newMcQuestionNumber, setNewMcQuestionNumber] = useState('');
  const [newMcCorrectOption, setNewMcCorrectOption] = useState<AnswerOption>(AnswerOption.A);
  const [newTextQuestionNumber, setNewTextQuestionNumber] = useState('');
  const [newTextCorrectAnswers, setNewTextCorrectAnswers] = useState('');
  const [newTextCaseSensitive, setNewTextCaseSensitive] = useState(false);

  // Fetch test data
  const { data: rawTest, isLoading, error } = useQuery({
    queryKey: ['test', id],
    queryFn: () => testsApi.getById(id!),
  });

  // Parse the test data to handle string JSON fields
  const test = rawTest ? parseAnswerSheet(rawTest) : undefined;

  // Set form data when test data is fetched - only once
  useEffect(() => {
    if (test && !isFormInitialized) {
      // Create a new form data object, excluding 'content' field
      const { content, ...restData } = test;
      setFormData(restData);
      setIsFormInitialized(true);
    }
  }, [test, isFormInitialized]);

  // Update test mutation
  const updateMutation = useMutation({
    mutationFn: (data: Partial<Test>) =>
      testsApi.update(id!, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['test', id] });
      toast.success('Test updated successfully');
      setIsEditing(false);
    },
    onError: (error) => {
      toast.error('Failed to update test');
      console.error(error);
    }
  });

  // Publish/unpublish mutation
  const publishMutation = useMutation({
    mutationFn: () => testsApi.togglePublished(id!),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['test', id] });
      toast.success(test?.isPublished ? 'Test unpublished' : 'Test published');
      setShowConfirmDialog(false);
    },
    onError: (error) => {
      toast.error('Failed to change publication status');
      console.error(error);
      setShowConfirmDialog(false);
    }
  });

  // Add a new handler for rich text editor changes
  const handleEditorChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleMultipleChoiceAnswerChange = (questionNumber: number, correctOption: string) => {
    setFormData(prev => {
      const currentAnswers = prev.answerSheet?.multipleChoiceAnswers || [];
      const currentTextAnswers = prev.answerSheet?.textAnswers || [];

      const existingIndex = currentAnswers.findIndex(a => a.questionNumber === questionNumber);

      const updatedAnswers = [...currentAnswers];
      if (existingIndex >= 0) {
        updatedAnswers[existingIndex] = {
          questionNumber,
          correctOption: correctOption as AnswerOption
        };
      } else {
        updatedAnswers.push({
          questionNumber,
          correctOption: correctOption as AnswerOption
        });
      }

      return {
        ...prev,
        answerSheet: {
          multipleChoiceAnswers: updatedAnswers,
          textAnswers: currentTextAnswers
        }
      };
    });
  };

  const handleTextAnswerChange = (questionNumber: number, correctAnswers: string[], caseSensitive: boolean) => {
    setFormData(prev => {
      const currentAnswers = prev.answerSheet?.textAnswers || [];
      const currentMcAnswers = prev.answerSheet?.multipleChoiceAnswers || [];

      const existingIndex = currentAnswers.findIndex(a => a.questionNumber === questionNumber);

      const updatedAnswers = [...currentAnswers];
      if (existingIndex >= 0) {
        updatedAnswers[existingIndex] = {
          questionNumber,
          correctAnswers,
          caseSensitive
        };
      } else {
        updatedAnswers.push({
          questionNumber,
          correctAnswers,
          caseSensitive
        });
      }

      return {
        ...prev,
        answerSheet: {
          textAnswers: updatedAnswers,
          multipleChoiceAnswers: currentMcAnswers
        }
      };
    });
  };

  const handleRemoveMultipleChoiceAnswer = (questionNumber: number) => {
    setFormData(prev => ({
      ...prev,
      answerSheet: {
        multipleChoiceAnswers: (prev.answerSheet?.multipleChoiceAnswers || [])
          .filter(a => a.questionNumber !== questionNumber),
        textAnswers: prev.answerSheet?.textAnswers || []
      }
    }));
  };

  const handleRemoveTextAnswer = (questionNumber: number) => {
    setFormData(prev => ({
      ...prev,
      answerSheet: {
        multipleChoiceAnswers: prev.answerSheet?.multipleChoiceAnswers || [],
        textAnswers: (prev.answerSheet?.textAnswers || [])
          .filter(a => a.questionNumber !== questionNumber)
      }
    }));
  };

  // Add multiple choice questions
  const handleAddMultipleChoiceQuestions = () => {
    const questionNumber = Number(newMcQuestionNumber);

    // Validation
    if (!questionNumber || questionNumber <= 0) {
      toast.error('Please enter a valid question number');
      return;
    }

    // Check for duplicate question number
    if (formData.answerSheet?.multipleChoiceAnswers?.some(a => a.questionNumber === questionNumber)) {
      toast.error('Question number already exists');
      return;
    }

    handleMultipleChoiceAnswerChange(questionNumber, newMcCorrectOption);
    setNewMcQuestionNumber('');
    setNewMcCorrectOption(AnswerOption.A);
    toast.success('Answer added successfully');
  };

  // Add a text answer
  const handleAddTextAnswer = () => {
    const questionNumber = Number(newTextQuestionNumber);

    // Validation
    if (!questionNumber || questionNumber <= 0) {
      toast.error('Please enter a valid question number');
      return;
    }

    if (!newTextCorrectAnswers.trim()) {
      toast.error('Please enter at least one correct answer');
      return;
    }

    // Check for duplicate question number
    if (formData.answerSheet?.textAnswers?.some(a => a.questionNumber === questionNumber)) {
      toast.error('Question number already exists');
      return;
    }

    const answers = newTextCorrectAnswers
      .split(',')
      .map(a => a.trim())
      .filter(a => a !== '');

    if (answers.length === 0) {
      toast.error('Please enter valid answers');
      return;
    }

    handleTextAnswerChange(questionNumber, answers, newTextCaseSensitive);
    setNewTextQuestionNumber('');
    setNewTextCorrectAnswers('');
    setNewTextCaseSensitive(false);
    toast.success('Answer added successfully');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateMutation.mutate(formData);
  };

  const handlePublishToggle = () => {
    publishMutation.mutate();
  };

  if (isLoading) return <div className="container mx-auto p-4 dark:bg-slate-900 dark:text-white">Loading...</div>;
  if (error) return <div className="container mx-auto p-4 dark:bg-slate-900 dark:text-red-400">Error loading test</div>;
  if (!test) return <div className="container mx-auto p-4 dark:bg-slate-900 dark:text-white">Test not found</div>;

  // Render View Mode
  if (!isEditing) {
    return (
      <div className="container mx-auto p-4 dark:bg-slate-900">
        <ToastContainer />
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold dark:text-white">{test.title}</h1>
          <div className="space-x-2">
            <button
              onClick={() => setIsEditing(true)}
              className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
            >
              Edit
            </button>
            <button
              onClick={() => setShowConfirmDialog(true)}
              className={`${
                test.isPublished
                  ? 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700'
                  : 'bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700'
              } text-white px-4 py-2 rounded-md transition-colors`}
            >
              {test.isPublished ? 'Unpublish' : 'Publish'}
            </button>
          </div>
        </div>

        {/* Publication Status */}
        <div className="mb-6">
          <span
            className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
              test.isPublished
                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
            }`}
          >
            {test.isPublished ? 'Published' : 'Draft'}
          </span>
        </div>

        {/* Test Details */}
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4 dark:text-white">Test Details</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <p className="text-gray-600 dark:text-gray-400">Type</p>
              <p className="font-medium dark:text-white">{test.type}</p>
            </div>
            <div>
              <p className="text-gray-600 dark:text-gray-400">Difficulty</p>
              <p className="font-medium dark:text-white">{test.difficulty}</p>
            </div>
            <div>
              <p className="text-gray-600 dark:text-gray-400">Time Limit</p>
              <p className="font-medium dark:text-white">{test.timeLimit} minutes</p>
            </div>
            <div>
              <p className="text-gray-600 dark:text-gray-400">Max Score</p>
              <p className="font-medium dark:text-white">{test.maxScore} points</p>
            </div>
            <div>
              <p className="text-gray-600 dark:text-gray-400">Category</p>
              <p className="font-medium dark:text-white">{test.category?.name || 'Not categorized'}</p>
            </div>
          </div>

          <h3 className="text-lg font-semibold mb-2 dark:text-white">Instructions</h3>
          <p className="mb-4 dark:text-gray-300">{test.instructions || 'No instructions provided'}</p>

          <h3 className="text-lg font-semibold mb-2 dark:text-white">Description</h3>
          <div className="bg-gray-50 dark:bg-slate-700 p-4 rounded mb-6 overflow-auto max-h-96">
            {test.description ? (
              <div
                className="prose prose-sm md:prose lg:prose-lg max-w-none dark:prose-invert"
                dangerouslySetInnerHTML={{
                  __html: test.description
                }}
              />
            ) : (
              <p className="dark:text-gray-300">No description provided</p>
            )}
          </div>
        </div>

        {/* Answer Sheet */}
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4 dark:text-white">Answer Sheet</h2>

          {/* Multiple Choice Answers */}
          {test.answerSheet?.multipleChoiceAnswers && test.answerSheet.multipleChoiceAnswers.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2 dark:text-white">Multiple Choice Questions</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100 dark:bg-slate-700">
                      <th className="border dark:border-slate-600 p-2 dark:text-gray-300">Question</th>
                      <th className="border dark:border-slate-600 p-2 dark:text-gray-300">Correct Option</th>
                    </tr>
                  </thead>
                  <tbody>
                    {test.answerSheet.multipleChoiceAnswers
                      .sort((a, b) => a.questionNumber - b.questionNumber)
                      .map((answer) => (
                        <tr key={answer.questionNumber} className="dark:bg-slate-800 hover:bg-gray-50 dark:hover:bg-slate-700">
                          <td className="border dark:border-slate-600 p-2 text-center dark:text-white">{answer.questionNumber}</td>
                          <td className="border dark:border-slate-600 p-2 text-center font-medium dark:text-white">{answer.correctOption}</td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Text Answers */}
          {test.answerSheet?.textAnswers && test.answerSheet.textAnswers.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-2 dark:text-white">Fill-in-the-Blank Questions</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100 dark:bg-slate-700">
                      <th className="border dark:border-slate-600 p-2 dark:text-gray-300">Question</th>
                      <th className="border dark:border-slate-600 p-2 dark:text-gray-300">Correct Answers</th>
                      <th className="border dark:border-slate-600 p-2 dark:text-gray-300">Case Sensitive</th>
                    </tr>
                  </thead>
                  <tbody>
                    {test.answerSheet.textAnswers
                      .sort((a, b) => a.questionNumber - b.questionNumber)
                      .map((answer) => (
                        <tr key={answer.questionNumber} className="dark:bg-slate-800 hover:bg-gray-50 dark:hover:bg-slate-700">
                          <td className="border dark:border-slate-600 p-2 text-center dark:text-white">{answer.questionNumber}</td>
                          <td className="border dark:border-slate-600 p-2 dark:text-white">
                            {answer.correctAnswers.join(', ')}
                          </td>
                          <td className="border dark:border-slate-600 p-2 text-center dark:text-white">
                            {answer.caseSensitive ? 'Yes' : 'No'}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {(!test.answerSheet?.multipleChoiceAnswers || test.answerSheet.multipleChoiceAnswers.length === 0) &&
           (!test.answerSheet?.textAnswers || test.answerSheet.textAnswers.length === 0) && (
            <p className="text-gray-500 dark:text-gray-400 italic">No answers have been provided for this test.</p>
          )}
        </div>

        {/* Confirmation Dialog */}
        {showConfirmDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-slate-800 rounded-lg p-6 max-w-md w-full">
              <h3 className="text-lg font-semibold mb-4 dark:text-white">
                {test.isPublished ? 'Unpublish this test?' : 'Publish this test?'}
              </h3>
              <p className="mb-6 dark:text-gray-300">
                {test.isPublished
                  ? 'This will hide the test from students. Are you sure you want to continue?'
                  : 'This will make the test visible to students. Are you sure you want to continue?'}
              </p>
              <div className="flex justify-end space-x-2">
                <button
                  onClick={() => setShowConfirmDialog(false)}
                  className="bg-gray-300 hover:bg-gray-400 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-white px-4 py-2 rounded-md transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handlePublishToggle}
                  className={`${
                    test.isPublished
                      ? 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700'
                      : 'bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700'
                  } text-white px-4 py-2 rounded-md transition-colors`}
                >
                  Confirm
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Render Edit Mode
  return (
    <div className="container mx-auto p-4 dark:bg-slate-900">
      <ToastContainer />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold dark:text-white">Edit Test</h1>
        <button
          onClick={() => setIsEditing(false)}
          className="bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700 text-white px-4 py-2 rounded-md transition-colors"
        >
          Cancel
        </button>
      </div>

      <form onSubmit={handleSubmit} className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
        {/* Basic Information */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4 dark:text-white">Test Information</h2>

          <div className="mb-4">
            <label className="block text-gray-700 dark:text-gray-300 mb-2">Title</label>
            <input
              type="text"
              name="title"
              value={formData.title || ''}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">Type</label>
              <select
                name="type"
                value={formData.type || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white"
                required
              >
                <option value="">Select type</option>
                <option value={TestType.READING}>Reading</option>
                <option value={TestType.LISTENING}>Listening</option>
                <option value={TestType.WRITING}>Writing</option>
                <option value={TestType.SPEAKING}>Speaking</option>
              </select>
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">Difficulty</label>
              <select
                name="difficulty"
                value={formData.difficulty || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white"
                required
              >
                <option value="">Select difficulty</option>
                <option value={TestDifficulty.EASY}>Easy</option>
                <option value={TestDifficulty.MEDIUM}>Medium</option>
                <option value={TestDifficulty.HARD}>Hard</option>
              </select>
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">Time Limit (minutes)</label>
              <input
                type="number"
                name="timeLimit"
                value={formData.timeLimit || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white"
                required
                min="1"
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">Max Score</label>
              <input
                type="number"
                name="maxScore"
                value={formData.maxScore || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white"
                required
                min="1"
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 dark:text-gray-300 mb-2">Instructions</label>
            <textarea
              name="instructions"
              value={formData.instructions || ''}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white"
              rows={3}
            />
          </div>
        </div>

        {/* Description and Answer Sheet side by side */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Description Column */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
            <h2 className="text-xl font-semibold mb-4 dark:text-white">Description</h2>
            <div className="overflow-auto" style={{ height: "600px" }}>
              <TextEditor
                value={formData.description || ''}
                onChange={(value) => handleEditorChange('description', value)}
                placeholder="Enter test description..."
                className="h-[570px]"
              />
            </div>
          </div>

          {/* Answer Sheet Column */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
            <div className="bg-white dark:bg-slate-800 pb-2 border-b border-gray-200 dark:border-slate-700 mb-4">
              <h2 className="text-xl font-semibold py-2 dark:text-white">Answer Sheet</h2>
            </div>

            <div className="overflow-auto" style={{ height: "580px" }}>
              {/* Multiple Choice Answers */}
              <div className="mb-8">
                <div className="bg-white dark:bg-slate-800 py-2 border-b border-gray-100 dark:border-slate-700 mb-4">
                  <h3 className="text-lg font-semibold dark:text-white">Multiple Choice Questions</h3>
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    Add answers for multiple choice questions
                  </div>
                </div>

                {/* Add Multiple Choice Answer */}
                <div className="flex flex-wrap items-end gap-4 p-4 bg-gray-50 dark:bg-slate-700 rounded-md mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Question Number
                      <span className="text-red-500 dark:text-red-400 ml-1">*</span>
                    </label>
                    <input
                      type="number"
                      value={newMcQuestionNumber}
                      onChange={(e) => setNewMcQuestionNumber(e.target.value)}
                      className="px-3 py-2 border dark:border-slate-600 rounded-md w-24 dark:bg-slate-700 dark:text-white"
                      min="1"
                      placeholder="e.g. 1"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Correct Option
                      <span className="text-red-500 dark:text-red-400 ml-1">*</span>
                    </label>
                    <select
                      value={newMcCorrectOption}
                      onChange={(e) => setNewMcCorrectOption(e.target.value as AnswerOption)}
                      className="px-3 py-2 border dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white"
                    >
                      {Object.values(AnswerOption).map((option) => (
                        <option key={option} value={option}>{option}</option>
                      ))}
                    </select>
                  </div>
                  <button
                    type="button"
                    onClick={handleAddMultipleChoiceQuestions}
                    className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
                  >
                    Add Answer
                  </button>
                </div>

                {/* Multiple Choice Answers Table */}
                {formData.answerSheet?.multipleChoiceAnswers && formData.answerSheet.multipleChoiceAnswers.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full bg-white dark:bg-slate-800 border dark:border-slate-600">
                      <thead>
                        <tr className="bg-gray-50 dark:bg-slate-700">
                          <th className="py-2 px-4 border-b dark:border-slate-600 text-left font-medium dark:text-gray-300">Q#</th>
                          <th className="py-2 px-4 border-b dark:border-slate-600 text-center font-medium dark:text-gray-300">A</th>
                          <th className="py-2 px-4 border-b dark:border-slate-600 text-center font-medium dark:text-gray-300">B</th>
                          <th className="py-2 px-4 border-b dark:border-slate-600 text-center font-medium dark:text-gray-300">C</th>
                          <th className="py-2 px-4 border-b dark:border-slate-600 text-center font-medium dark:text-gray-300">D</th>
                          <th className="py-2 px-4 border-b dark:border-slate-600 text-center font-medium dark:text-gray-300">Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.answerSheet?.multipleChoiceAnswers
                          .sort((a, b) => a.questionNumber - b.questionNumber)
                          .map((answer) => (
                            <tr key={answer.questionNumber} className="hover:bg-gray-50 dark:hover:bg-slate-700">
                              <td className="py-2 px-4 border-b dark:border-slate-600 dark:text-white">{answer.questionNumber}</td>
                              {Object.values(AnswerOption).map((option) => (
                                <td key={option} className="py-2 px-4 border-b dark:border-slate-600 text-center">
                                  <input
                                    type="radio"
                                    name={`question-${answer.questionNumber}`}
                                    checked={answer.correctOption === option}
                                    onChange={() => handleMultipleChoiceAnswerChange(answer.questionNumber, option)}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 dark:bg-slate-700 dark:border-slate-600"
                                  />
                                </td>
                              ))}
                              <td className="py-2 px-4 border-b dark:border-slate-600 text-center">
                                <button
                                  type="button"
                                  onClick={() => handleRemoveMultipleChoiceAnswer(answer.questionNumber)}
                                  className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                >
                                  Remove
                                </button>
                              </td>
                            </tr>
                          ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400 italic text-center py-4">No multiple choice answers added yet.</p>
                )}
              </div>

              {/* Text Answers */}
              <div>
                <div className="bg-white dark:bg-slate-800 py-2 border-t border-gray-200 dark:border-slate-700 pt-4 mb-4">
                  <h3 className="text-lg font-semibold dark:text-white">Fill-in-the-Blank Questions</h3>
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    Add answers for fill-in-the-blank questions
                  </div>
                </div>

                {/* Add Text Answer */}
                <div className="flex flex-wrap items-end gap-4 p-4 bg-gray-50 dark:bg-slate-700 rounded-md mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Question Number
                      <span className="text-red-500 dark:text-red-400 ml-1">*</span>
                    </label>
                    <input
                      type="number"
                      value={newTextQuestionNumber}
                      onChange={(e) => setNewTextQuestionNumber(e.target.value)}
                      className="px-3 py-2 border dark:border-slate-600 rounded-md w-24 dark:bg-slate-700 dark:text-white"
                      min="1"
                      placeholder="e.g. 1"
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Correct Answers
                      <span className="text-red-500 dark:text-red-400 ml-1">*</span>
                      <span className="text-gray-500 dark:text-gray-400 text-xs ml-1">(comma-separated)</span>
                    </label>
                    <input
                      type="text"
                      value={newTextCorrectAnswers}
                      onChange={(e) => setNewTextCorrectAnswers(e.target.value)}
                      className="w-full px-3 py-2 border dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white"
                      placeholder="e.g. answer1, answer2, answer3"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={newTextCaseSensitive}
                      onChange={(e) => setNewTextCaseSensitive(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 dark:bg-slate-700 dark:border-slate-600"
                    />
                    <label className="text-sm text-gray-700 dark:text-gray-300">
                      Case Sensitive
                    </label>
                  </div>
                  <button
                    type="button"
                    onClick={handleAddTextAnswer}
                    className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
                  >
                    Add Answer
                  </button>
                </div>

                {/* Text Answers Table */}
                {formData.answerSheet?.textAnswers && formData.answerSheet.textAnswers.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full border-collapse">
                      <thead>
                        <tr className="bg-gray-50 dark:bg-slate-700">
                          <th className="border dark:border-slate-600 p-2 font-medium dark:text-gray-300">Question</th>
                          <th className="border dark:border-slate-600 p-2 font-medium dark:text-gray-300">Correct Answers</th>
                          <th className="border dark:border-slate-600 p-2 font-medium dark:text-gray-300">Case Sensitive</th>
                          <th className="border dark:border-slate-600 p-2 font-medium dark:text-gray-300">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.answerSheet?.textAnswers
                          .sort((a, b) => a.questionNumber - b.questionNumber)
                          .map((answer) => (
                            <tr key={answer.questionNumber} className="hover:bg-gray-50 dark:hover:bg-slate-700 dark:bg-slate-800">
                              <td className="border dark:border-slate-600 p-2 text-center dark:text-white">{answer.questionNumber}</td>
                              <td className="border dark:border-slate-600 p-2">
                                <input
                                  type="text"
                                  value={answer.correctAnswers.join(', ')}
                                  onChange={(e) => {
                                    const answers = e.target.value
                                      .split(',')
                                      .map(a => a.trim())
                                      .filter(a => a !== '');
                                    handleTextAnswerChange(
                                      answer.questionNumber,
                                      answers,
                                      answer.caseSensitive
                                    );
                                  }}
                                  className="w-full px-2 py-1 border dark:border-slate-600 rounded dark:bg-slate-700 dark:text-white"
                                  placeholder="Comma-separated answers"
                                />
                              </td>
                              <td className="border dark:border-slate-600 p-2 text-center">
                                <input
                                  type="checkbox"
                                  checked={answer.caseSensitive}
                                  onChange={(e) => {
                                    handleTextAnswerChange(
                                      answer.questionNumber,
                                      answer.correctAnswers,
                                      e.target.checked
                                    );
                                  }}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 dark:bg-slate-700 dark:border-slate-600"
                                />
                              </td>
                              <td className="border dark:border-slate-600 p-2 text-center">
                                <button
                                  type="button"
                                  onClick={() => handleRemoveTextAnswer(answer.questionNumber)}
                                  className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                >
                                  Remove
                                </button>
                              </td>
                            </tr>
                          ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400 italic text-center py-4">No fill-in-the-blank answers added yet.</p>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors"
            disabled={updateMutation.isPending}
          >
            {updateMutation.isPending ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default TestDetails;
