import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { categoriesApi } from '../../api/categoriesApi';
import { testsApi } from '../../api/testsApi';
import { AnswerOption, MultipleChoiceAnswer, TextAnswer } from '../../types/test';
import TextEditor from '../../components/TextEditor';
import { useTheme } from '../../contexts/ThemeContext';
import AnswerSheetImporter from '../../components/tests/AnswerSheetImporter';
import { parseMultipleChoiceAnswers, parseTextAnswers } from '../../utils/answerSheetParser';

const TestCreate = () => {
  const navigate = useNavigate();
  const { t } = useTranslation(['tests', 'common']);
  const { theme } = useTheme();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [categoryId, setCategoryId] = useState('');
  const [type, setType] = useState('READING');
  const [difficulty, setDifficulty] = useState('MEDIUM');
  const [timeLimit, setTimeLimit] = useState(60);
  const [maxScore, setMaxScore] = useState(100);
  const [instructions, setInstructions] = useState('');

  // Answer sheet states
  const [multipleChoiceAnswers, setMultipleChoiceAnswers] = useState<MultipleChoiceAnswer[]>([]);
  const [textAnswers, setTextAnswers] = useState<TextAnswer[]>([]);
  const [showAnswerSheet, setShowAnswerSheet] = useState(false);
  const [newQuestionCount, setNewQuestionCount] = useState(5);

  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Fetch categories - use the appropriate endpoint based on user role
  const { data: categories, isLoading: isLoadingCategories } = useQuery({
    queryKey: ['categories'],
    queryFn: categoriesApi.getAll,
  });

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    try {
      setIsLoading(true);
      setError('');

      // Validate multiple choice answers
      if (multipleChoiceAnswers.length > 0) {
        const invalidMcQuestions = multipleChoiceAnswers.filter(
          answer => !answer.correctOption
        );

        if (invalidMcQuestions.length > 0) {
          setError(`Please select a correct option for all multiple choice questions. Question(s) ${invalidMcQuestions.map(q => q.questionNumber).join(', ')} missing correct option.`);
          setIsLoading(false);
          return;
        }
      }

      // Validate text answers
      if (textAnswers.length > 0) {
        const invalidTextQuestions = textAnswers.filter(
          answer => !answer.correctAnswers || answer.correctAnswers.length === 0 ||
                  answer.correctAnswers.every(ans => !ans.trim())
        );

        if (invalidTextQuestions.length > 0) {
          setError(t('validation.correctAnswerRequired', { ns: 'tests' }) + ` Question(s) ${invalidTextQuestions.map(q => q.questionNumber).join(', ')} missing answer.`);
          setIsLoading(false);
          return;
        }
      }

      // Check for empty or invalid question numbers in multiple choice questions
      const invalidMcNumberQuestions = multipleChoiceAnswers.filter(
        answer => answer.questionNumber === undefined || answer.questionNumber <= 0
      );

      if (invalidMcNumberQuestions.length > 0) {
        setError(t('validation.invalidQuestionNumbers', { ns: 'tests', defaultValue: 'Please enter valid question numbers (greater than 0) for all questions' }));
        setIsLoading(false);
        return;
      }

      // Check for empty or invalid question numbers in text answers
      const invalidTextNumberQuestions = textAnswers.filter(
        answer => answer.questionNumber === undefined || answer.questionNumber <= 0
      );

      if (invalidTextNumberQuestions.length > 0) {
        setError(t('validation.invalidQuestionNumbers', { ns: 'tests', defaultValue: 'Please enter valid question numbers (greater than 0) for all questions' }));
        setIsLoading(false);
        return;
      }

      // Check for duplicate question numbers
      const allQuestionNumbers = [
        ...multipleChoiceAnswers.filter(q => q.questionNumber !== undefined).map(q => q.questionNumber as number),
        ...textAnswers.filter(q => q.questionNumber !== undefined).map(q => q.questionNumber as number)
      ];

      const uniqueQuestionNumbers = new Set(allQuestionNumbers);
      if (uniqueQuestionNumbers.size !== allQuestionNumbers.length) {
        // Find the duplicates
        const counts: Record<number, number> = {};
        const duplicates: number[] = [];

        allQuestionNumbers.forEach(num => {
          if (num !== undefined) {
            counts[num] = (counts[num] || 0) + 1;
            if (counts[num] === 2) duplicates.push(num);
          }
        });

        setError(t('validation.duplicateQuestionNumbers', { ns: 'tests', defaultValue: 'Duplicate question numbers found' }) + `: ${duplicates.join(', ')}`);
        setIsLoading(false);
        return;
      }

      await testsApi.create({
        title,
        description: content, // Using content for description field
        categoryId,
        type,
        difficulty,
        timeLimit,
        maxScore,
        instructions,
        answerSheet: {
          multipleChoiceAnswers,
          textAnswers
        }
      });

      // Redirect to tests list
      navigate('/tests');
    } catch (err: any) {
      setError(err.response?.data?.message || t('create.error', { ns: 'tests' }));
    } finally {
      setIsLoading(false);
    }
  };

  // Add multiple choice questions
  const handleAddMultipleChoiceQuestions = () => {
    const newQuestions: MultipleChoiceAnswer[] = [];

    // Filter out undefined question numbers
    const definedQuestionNumbers = multipleChoiceAnswers
      .filter(q => q.questionNumber !== undefined)
      .map(q => q.questionNumber as number);

    // Also check text answers to avoid duplicates
    const textQuestionNumbers = textAnswers
      .filter(q => q.questionNumber !== undefined)
      .map(q => q.questionNumber as number);

    // Combine all existing question numbers
    const allQuestionNumbers = [...definedQuestionNumbers, ...textQuestionNumbers];

    // Find the current maximum question number
    const currentMax = allQuestionNumbers.length > 0
      ? Math.max(...allQuestionNumbers)
      : 0;

    for (let i = 1; i <= newQuestionCount; i++) {
      newQuestions.push({
        questionNumber: currentMax + i,
        correctOption: AnswerOption.A // Default to Option A
      });
    }

    setMultipleChoiceAnswers([...multipleChoiceAnswers, ...newQuestions]);
  };

  // Add a text answer question
  const handleAddTextAnswer = () => {
    // Filter out undefined question numbers from multiple choice questions
    const multipleChoiceNumbers = multipleChoiceAnswers
      .filter(q => q.questionNumber !== undefined)
      .map(q => q.questionNumber as number);

    // Filter out undefined question numbers from text answers
    const textAnswerNumbers = textAnswers
      .filter(q => q.questionNumber !== undefined)
      .map(q => q.questionNumber as number);

    // Find the maximum from multiple choice questions
    const multipleChoiceMax = multipleChoiceNumbers.length > 0
      ? Math.max(...multipleChoiceNumbers)
      : 0;

    // Find the maximum from text answers
    const textAnswerMax = textAnswerNumbers.length > 0
      ? Math.max(...textAnswerNumbers)
      : 0;

    // Use the larger of the two values to determine the next question number
    const nextQuestionNumber = Math.max(multipleChoiceMax, textAnswerMax) + 1;

    setTextAnswers([
      ...textAnswers,
      {
        questionNumber: nextQuestionNumber,
        correctAnswers: [''], // Initialize with an empty string
        caseSensitive: false
      }
    ]);
  };

  // Update multiple choice answer
  const handleMultipleChoiceChange = (index: number, option: AnswerOption) => {
    const updatedAnswers = [...multipleChoiceAnswers];
    updatedAnswers[index].correctOption = option;
    setMultipleChoiceAnswers(updatedAnswers);
  };

  // Update text answer
  const handleTextAnswerChange = (index: number, answerIndex: number, value: string) => {
    const updatedAnswers = [...textAnswers];
    updatedAnswers[index].correctAnswers[answerIndex] = value;
    setTextAnswers(updatedAnswers);
  };

  // Add another acceptable answer to a text question
  const handleAddAcceptableAnswer = (index: number) => {
    const updatedAnswers = [...textAnswers];
    updatedAnswers[index].correctAnswers.push('');
    setTextAnswers(updatedAnswers);
  };

  // Toggle case sensitivity for a text answer
  const handleToggleCaseSensitivity = (index: number) => {
    const updatedAnswers = [...textAnswers];
    updatedAnswers[index].caseSensitive = !updatedAnswers[index].caseSensitive;
    setTextAnswers(updatedAnswers);
  };

  // Remove a multiple choice question
  const handleRemoveMultipleChoiceQuestion = (index: number) => {
    const updatedAnswers = [...multipleChoiceAnswers];
    updatedAnswers.splice(index, 1);
    setMultipleChoiceAnswers(updatedAnswers);
  };

  // Remove a text answer question
  const handleRemoveTextQuestion = (index: number) => {
    const updatedAnswers = [...textAnswers];
    updatedAnswers.splice(index, 1);
    setTextAnswers(updatedAnswers);
  };

  // Handle importing multiple choice answers
  const handleImportMultipleChoice = (text: string) => {
    const { answers, errors } = parseMultipleChoiceAnswers(text);

    if (errors.length > 0) {
      setError(t('import.parseErrors', { ns: 'tests' }) + ': ' + errors.join('; '));
      return;
    }

    if (answers.length === 0) {
      setError(t('import.noValidAnswers', { ns: 'tests' }));
      return;
    }

    // Merge with existing answers, avoiding duplicates
    const existingQuestionNumbers = new Set(
      multipleChoiceAnswers.map(a => a.questionNumber)
    );

    const newAnswers = answers.filter(a => !existingQuestionNumbers.has(a.questionNumber));

    if (newAnswers.length === 0) {
      setError(t('import.allDuplicates', { ns: 'tests' }));
      return;
    }

    setMultipleChoiceAnswers([...multipleChoiceAnswers, ...newAnswers]);
    setError(''); // Clear any previous errors
  };

  // Handle importing fill in the blank answers
  const handleImportTextAnswers = (text: string) => {
    const { answers, errors } = parseTextAnswers(text);

    if (errors.length > 0) {
      setError(t('import.parseErrors', { ns: 'tests' }) + ': ' + errors.join('; '));
      return;
    }

    if (answers.length === 0) {
      setError(t('import.noValidAnswers', { ns: 'tests' }));
      return;
    }

    // Merge with existing answers, avoiding duplicates
    const existingQuestionNumbers = new Set(
      textAnswers.map(a => a.questionNumber)
    );

    const newAnswers = answers.filter(a => !existingQuestionNumbers.has(a.questionNumber));

    if (newAnswers.length === 0) {
      setError(t('import.allDuplicates', { ns: 'tests' }));
      return;
    }

    setTextAnswers([...textAnswers, ...newAnswers]);
    setError(''); // Clear any previous errors
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-slate-900 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
            {t('create.title', { ns: 'tests' })}
          </h1>

          {error && (
            <div className="mb-4 bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-md" role="alert">
              <span className="block sm:inline">{error}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6 bg-white dark:bg-slate-800 shadow sm:rounded-lg p-6 border border-gray-200 dark:border-slate-700">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                {t('fields.title', { ns: 'tests' })}
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  name="title"
                  id="title"
                  required
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="form-input sm:text-sm border-gray-300 dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white py-2 px-3 h-10"
                />
              </div>
            </div>

            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                {t('fields.content', { ns: 'tests' })}
              </label>
              <div className="mt-1">
                <TextEditor
                  value={content}
                  onChange={(value) => setContent(value)}
                  placeholder={t('fields.content', { ns: 'tests' }) + '...'}
                  className="h-96 mb-2"
                />
              </div>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Include all passages, questions, and instructions. Number each question clearly (e.g., "1.", "2.", etc.).
              </p>
            </div>

            <div>
              <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                {t('fields.category', { ns: 'tests' })}
              </label>
              <div className="mt-1">
                <select
                  id="categoryId"
                  name="categoryId"
                  required
                  value={categoryId}
                  onChange={(e) => setCategoryId(e.target.value)}
                  className="form-select sm:text-sm border-gray-300 dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white py-2 px-3 h-10"
                  disabled={isLoadingCategories}
                >
                  <option value="">-- {t('fields.category', { ns: 'tests' })} --</option>
                  {categories?.filter(category => category.isActive).map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                {t('fields.type', { ns: 'tests' })}
              </label>
              <div className="mt-1">
                <select
                  id="type"
                  name="type"
                  required
                  value={type}
                  onChange={(e) => setType(e.target.value)}
                  className="form-select sm:text-sm border-gray-300 dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white py-2 px-3 h-10"
                >
                  <option value="READING">{t('types.reading', { ns: 'tests' })}</option>
                  <option value="LISTENING">{t('types.listening', { ns: 'tests' })}</option>
                  <option value="WRITING">{t('types.writing', { ns: 'tests' })}</option>
                  <option value="SPEAKING">{t('types.speaking', { ns: 'tests' })}</option>
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="difficulty" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                {t('fields.difficulty', { ns: 'tests' })}
              </label>
              <div className="mt-1">
                <select
                  id="difficulty"
                  name="difficulty"
                  required
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value)}
                  className="form-select sm:text-sm border-gray-300 dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white py-2 px-3 h-10"
                >
                  <option value="EASY">{t('difficulties.easy', { ns: 'tests' })}</option>
                  <option value="MEDIUM">{t('difficulties.medium', { ns: 'tests' })}</option>
                  <option value="HARD">{t('difficulties.hard', { ns: 'tests' })}</option>
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="timeLimit" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                {t('fields.timeLimit', { ns: 'tests' })} ({t('fields.minutes', { ns: 'tests' })})
              </label>
              <div className="mt-1">
                <input
                  type="number"
                  id="timeLimit"
                  name="timeLimit"
                  min="1"
                  required
                  value={timeLimit}
                  onChange={(e) => setTimeLimit(Number(e.target.value))}
                  className="form-input sm:text-sm border-gray-300 dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white py-2 px-3 h-10"
                />
              </div>
            </div>

            <div>
              <label htmlFor="maxScore" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                {t('fields.maxScore', { ns: 'tests' })} ({t('fields.points', { ns: 'tests' })})
              </label>
              <div className="mt-1">
                <input
                  type="number"
                  id="maxScore"
                  name="maxScore"
                  min="1"
                  required
                  value={maxScore}
                  onChange={(e) => setMaxScore(Number(e.target.value))}
                  className="form-input sm:text-sm border-gray-300 dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white py-2 px-3 h-10"
                />
              </div>
            </div>

            <div>
              <label htmlFor="instructions" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                {t('fields.instructions', { ns: 'tests' })}
              </label>
              <div className="mt-1">
                <textarea
                  id="instructions"
                  name="instructions"
                  rows={3}
                  value={instructions}
                  onChange={(e) => setInstructions(e.target.value)}
                  className="form-textarea sm:text-sm border-gray-300 dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white py-2 px-3 min-h-[80px]"
                />
              </div>
            </div>

            {/* Answer Sheet Section */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">{t('fields.answerSheet', { ns: 'tests' })}</h3>
                <button
                  type="button"
                  onClick={() => setShowAnswerSheet(!showAnswerSheet)}
                  className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  {showAnswerSheet ? t('actions.hide', { ns: 'common' }) : t('actions.view', { ns: 'common' })}
                </button>
              </div>

              {showAnswerSheet && (
                <div className="space-y-6">
                  {/* Multiple Choice Section */}
                  <div className="bg-gray-50 dark:bg-slate-700 p-4 rounded-md">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-medium text-gray-700 dark:text-gray-200">{t('fields.multipleChoiceQuestions', { ns: 'tests' })}</h4>
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          min="1"
                          max="50"
                          value={newQuestionCount}
                          onChange={(e) => setNewQuestionCount(Number(e.target.value))}
                          className="w-16 form-input sm:text-sm p-1 border-gray-300 dark:border-slate-600 rounded-md dark:bg-slate-800 dark:text-white"
                        />
                        <button
                          type="button"
                          onClick={handleAddMultipleChoiceQuestions}
                          className="bg-blue-500 text-white py-1 px-2 rounded text-sm hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700"
                        >
                          {t('fields.addQuestions', { ns: 'tests' })}
                        </button>
                      </div>
                    </div>

                    {/* Multiple Choice Answer Importer */}
                    <AnswerSheetImporter
                      type="multipleChoice"
                      onImport={handleImportMultipleChoice}
                      placeholder={t('import.multipleChoicePlaceholder', { ns: 'tests', defaultValue: '1. A\n2. B\n3. C\n4. D' })}
                    />

                    {multipleChoiceAnswers.length > 0 ? (
                      <div className="overflow-x-auto">
                        <div className="flex justify-end mb-2">
                          <button
                            type="button"
                            onClick={() => {
                              // Sort multiple choice answers by question number
                              const sortedAnswers = [...multipleChoiceAnswers].sort((a, b) => {
                                // Handle undefined values in sorting
                                if (a.questionNumber === undefined) return 1; // Move undefined to the end
                                if (b.questionNumber === undefined) return -1; // Move undefined to the end
                                return a.questionNumber - b.questionNumber;
                              });
                              setMultipleChoiceAnswers(sortedAnswers);
                            }}
                            className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                          >
                            {t('fields.sortByNumber', { ns: 'tests' })}
                          </button>
                        </div>
                        <table className="min-w-full bg-white dark:bg-slate-800 border dark:border-slate-600 rounded-md">
                          <thead>
                            <tr>
                              <th className="py-2 px-4 border-b dark:border-slate-600 text-left text-gray-700 dark:text-gray-200">{t('fields.questionNumber', { ns: 'tests' })}</th>
                              <th className="py-2 px-4 border-b dark:border-slate-600 text-center text-gray-700 dark:text-gray-200">A</th>
                              <th className="py-2 px-4 border-b dark:border-slate-600 text-center text-gray-700 dark:text-gray-200">B</th>
                              <th className="py-2 px-4 border-b dark:border-slate-600 text-center text-gray-700 dark:text-gray-200">C</th>
                              <th className="py-2 px-4 border-b dark:border-slate-600 text-center text-gray-700 dark:text-gray-200">D</th>
                              <th className="py-2 px-4 border-b dark:border-slate-600 text-center text-gray-700 dark:text-gray-200">{t('fields.remove', { ns: 'tests' })}</th>
                            </tr>
                          </thead>
                          <tbody>
                            {multipleChoiceAnswers.map((answer, index) => (
                              <tr key={index} className="hover:bg-gray-50 dark:hover:bg-slate-700">
                                <td className="py-2 px-4 border-b dark:border-slate-600 text-gray-700 dark:text-gray-200">
                                  <input
                                    type="number"
                                    value={answer.questionNumber === undefined ? '' : Number(answer.questionNumber)}
                                    onChange={(e) => {
                                      const updatedAnswers = [...multipleChoiceAnswers];
                                      // Allow empty string (when deleting)
                                      updatedAnswers[index].questionNumber = e.target.value === '' ? undefined : Number(e.target.value);
                                      setMultipleChoiceAnswers(updatedAnswers);
                                    }}
                                    className="w-16 form-input sm:text-sm p-1 border-gray-300 dark:border-slate-600 rounded-md dark:bg-slate-800 dark:text-white"
                                  />
                                </td>
                                {Object.values(AnswerOption).map((option) => (
                                  <td key={option} className="py-2 px-4 border-b dark:border-slate-600 text-center">
                                    <input
                                      type="radio"
                                      name={`question-${answer.questionNumber}`}
                                      checked={answer.correctOption === option}
                                      onChange={() => handleMultipleChoiceChange(index, option)}
                                      className="form-radio text-blue-600 dark:text-blue-500 dark:bg-slate-700 dark:border-slate-500"
                                    />
                                  </td>
                                ))}
                                <td className="py-2 px-4 border-b dark:border-slate-600 text-center">
                                  <button
                                    type="button"
                                    onClick={() => handleRemoveMultipleChoiceQuestion(index)}
                                    className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                  >
                                    {t('fields.remove', { ns: 'tests' })}
                                  </button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400 text-center py-4">{t('take.multipleChoice', { ns: 'tests' })} {t('take.notAnswered', { ns: 'tests' })}</p>
                    )}
                  </div>

                  {/* Fill in the Blank Section */}
                  <div className="bg-gray-50 dark:bg-slate-700 p-4 rounded-md">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-medium text-gray-700 dark:text-gray-200">{t('fields.fillInBlankQuestions', { ns: 'tests' })}</h4>
                      <div className="flex space-x-2">
                        <button
                          type="button"
                          onClick={() => {
                            // Sort text answers by question number
                            const sortedAnswers = [...textAnswers].sort((a, b) => {
                              // Handle undefined values in sorting
                              if (a.questionNumber === undefined) return 1; // Move undefined to the end
                              if (b.questionNumber === undefined) return -1; // Move undefined to the end
                              return a.questionNumber - b.questionNumber;
                            });
                            setTextAnswers(sortedAnswers);
                          }}
                          className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                        >
                          {t('fields.sortByNumber', { ns: 'tests' })}
                        </button>
                        <button
                          type="button"
                          onClick={handleAddTextAnswer}
                          className="bg-blue-500 text-white py-1 px-2 rounded text-sm hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700"
                        >
                          {t('fields.addQuestion', { ns: 'tests' })}
                        </button>
                      </div>
                    </div>

                    {/* Fill in the Blank Answer Importer */}
                    <AnswerSheetImporter
                      type="fillInBlank"
                      onImport={handleImportTextAnswers}
                      placeholder={t('import.fillInBlankPlaceholder', { ns: 'tests', defaultValue: '1. Answer 1\n2. Answer 2, Alt Answer\n3. Answer 3' })}
                    />

                    {textAnswers.length > 0 ? (
                      <div className="space-y-4">
                        {textAnswers.map((answer, index) => (
                          <div key={index} className="bg-white dark:bg-slate-800 p-3 rounded border border-gray-200 dark:border-slate-600">
                            <div className="flex justify-between items-center mb-2">
                              <div className="flex items-center">
                                <span className="font-medium text-gray-700 dark:text-gray-200 mr-2">{t('fields.questionNumber', { ns: 'tests' })}</span>
                                <input
                                  type="number"
                                  value={answer.questionNumber === undefined ? '' : Number(answer.questionNumber)}
                                  onChange={(e) => {
                                    const updatedAnswers = [...textAnswers];
                                    // Allow empty string (when deleting)
                                    updatedAnswers[index].questionNumber = e.target.value === '' ? undefined : Number(e.target.value);
                                    setTextAnswers(updatedAnswers);
                                  }}
                                  className="w-16 form-input sm:text-sm p-1 border-gray-300 dark:border-slate-600 rounded-md dark:bg-slate-800 dark:text-white"
                                />
                              </div>
                              <button
                                type="button"
                                onClick={() => handleRemoveTextQuestion(index)}
                                className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 text-sm"
                              >
                                {t('fields.remove', { ns: 'tests' })}
                              </button>
                            </div>

                            <div className="space-y-2">
                              {answer.correctAnswers.map((correctAnswer, answerIndex) => (
                                <div key={answerIndex} className="flex items-center space-x-2">
                                  <input
                                    type="text"
                                    value={correctAnswer}
                                    onChange={(e) => handleTextAnswerChange(index, answerIndex, e.target.value)}
                                    placeholder={`${t('fields.acceptableAnswer', { ns: 'tests' })} ${answerIndex + 1}`}
                                    className="flex-1 form-input sm:text-sm border-gray-300 dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white py-2 px-3 h-10"
                                    required={answerIndex === 0}
                                  />
                                  {answerIndex > 0 && (
                                    <button
                                      type="button"
                                      onClick={() => {
                                        const updatedAnswers = [...textAnswers];
                                        updatedAnswers[index].correctAnswers.splice(answerIndex, 1);
                                        setTextAnswers(updatedAnswers);
                                      }}
                                      className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                                      </svg>
                                    </button>
                                  )}
                                </div>
                              ))}

                              <div className="flex items-center justify-between">
                                <button
                                  type="button"
                                  onClick={() => handleAddAcceptableAnswer(index)}
                                  className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                                >
                                  + {t('fields.addAlternateAnswer', { ns: 'tests' })}
                                </button>

                                <div className="flex items-center">
                                  <input
                                    type="checkbox"
                                    id={`case-sensitive-${index}`}
                                    checked={answer.caseSensitive || false}
                                    onChange={() => handleToggleCaseSensitivity(index)}
                                    className="form-checkbox text-blue-600 dark:text-blue-500 dark:bg-slate-700 dark:border-slate-500"
                                  />
                                  <label htmlFor={`case-sensitive-${index}`} className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                    {t('fields.caseSensitive', { ns: 'tests' })}
                                  </label>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400 text-center py-4">{t('take.fillInTheBlank', { ns: 'tests' })} {t('take.notAnswered', { ns: 'tests' })}</p>
                    )}
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => navigate('/tests')}
                className="inline-flex items-center justify-center py-2 px-4 border border-gray-300 dark:border-slate-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-slate-700 hover:bg-gray-50 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-slate-800 transition-colors h-10"
              >
                {t('buttons.cancel', { ns: 'common' })}
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="inline-flex items-center justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-slate-800 disabled:opacity-50 dark:disabled:opacity-40 transition-colors h-10"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t('buttons.creating', { ns: 'common' })}
                  </>
                ) : (
                  t('create.title', { ns: 'tests' })
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default TestCreate;