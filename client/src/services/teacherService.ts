import axios from 'axios';
import { API_URL } from '../config';

// Types
export interface Group {
  id: string;
  name: string;
  level: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  createdBy: string;
  students?: StudentGroup[];
  assignments?: GroupAssignment[];
}

export interface StudentGroup {
  id: string;
  groupId: string;
  studentId: string;
  joinedAt: string;
  student?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface GroupAssignment {
  id: string;
  groupId: string;
  testId: string;
  assignedAt: string;
  dueDate?: string;
  test?: {
    id: string;
    title: string;
    description?: string;
    type: string;
    difficulty: string;
  };
}

export interface CreateGroupDto {
  name: string;
  level: string;
  description?: string;
}

export interface UpdateGroupDto {
  name?: string;
  level?: string;
  description?: string;
  isActive?: boolean;
}

export interface AddStudentsDto {
  studentIds: string[];
}

export interface AssignTestDto {
  testId: string;
  dueDate?: string;
}

export interface GroupResults {
  groupId: string;
  groupName: string;
  studentCount: number;
  testCount: number;
  averageScore: number;
  completionRate: number;
  students: {
    id: string;
    name: string;
    email: string;
    testsCompleted: number;
    averageScore: number;
  }[];
}

export interface StudentResults {
  studentId: string;
  studentName: string;
  email: string;
  groupId: string;
  testsAssigned: number;
  testsCompleted: number;
  averageScore: number;
  testResults: {
    testId: string;
    testTitle: string;
    assignedAt: string;
    dueDate?: string;
    completed: boolean;
    score: number | null;
  }[];
}

// API Service
const teacherService = {
  // Group Management
  getAllGroups: async (): Promise<Group[]> => {
    const response = await axios.get(`${API_URL}/teacher/groups`, {
      withCredentials: true,
    });
    return response.data;
  },

  getGroupById: async (id: string): Promise<Group> => {
    const response = await axios.get(`${API_URL}/teacher/groups/${id}`, {
      withCredentials: true,
    });
    return response.data;
  },

  createGroup: async (createGroupDto: CreateGroupDto): Promise<Group> => {
    const response = await axios.post(`${API_URL}/teacher/groups`, createGroupDto, {
      withCredentials: true,
    });
    return response.data;
  },

  updateGroup: async (id: string, updateGroupDto: UpdateGroupDto): Promise<Group> => {
    const response = await axios.patch(`${API_URL}/teacher/groups/${id}`, updateGroupDto, {
      withCredentials: true,
    });
    return response.data;
  },

  deleteGroup: async (id: string): Promise<Group> => {
    const response = await axios.delete(`${API_URL}/teacher/groups/${id}`, {
      withCredentials: true,
    });
    return response.data;
  },

  // Student Management
  getStudentsInGroup: async (groupId: string): Promise<StudentGroup[]> => {
    const response = await axios.get(`${API_URL}/teacher/groups/${groupId}/students`, {
      withCredentials: true,
    });
    return response.data;
  },

  addStudentsToGroup: async (groupId: string, addStudentsDto: AddStudentsDto): Promise<StudentGroup[]> => {
    const response = await axios.post(`${API_URL}/teacher/groups/${groupId}/students`, addStudentsDto, {
      withCredentials: true,
    });
    return response.data;
  },

  removeStudentFromGroup: async (groupId: string, studentId: string): Promise<void> => {
    await axios.delete(`${API_URL}/teacher/groups/${groupId}/students/${studentId}`, {
      withCredentials: true,
    });
  },

  // Test Assignment
  getTestsAssignedToGroup: async (groupId: string): Promise<GroupAssignment[]> => {
    const response = await axios.get(`${API_URL}/teacher/groups/${groupId}/tests`, {
      withCredentials: true,
    });
    return response.data;
  },

  assignTestToGroup: async (groupId: string, assignTestDto: AssignTestDto): Promise<GroupAssignment> => {
    const response = await axios.post(`${API_URL}/teacher/groups/${groupId}/tests`, assignTestDto, {
      withCredentials: true,
    });
    return response.data;
  },

  removeTestFromGroup: async (groupId: string, testId: string): Promise<void> => {
    await axios.delete(`${API_URL}/teacher/groups/${groupId}/tests/${testId}`, {
      withCredentials: true,
    });
  },

  // Results and Analytics
  getGroupResults: async (groupId: string): Promise<GroupResults> => {
    const response = await axios.get(`${API_URL}/teacher/groups/${groupId}/results`, {
      withCredentials: true,
    });
    return response.data;
  },

  getStudentResults: async (groupId: string, studentId: string): Promise<StudentResults> => {
    const response = await axios.get(`${API_URL}/teacher/groups/${groupId}/students/${studentId}/results`, {
      withCredentials: true,
    });
    return response.data;
  },
};

export default teacherService;
