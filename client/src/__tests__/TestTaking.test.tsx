import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import TestTaking from '../pages/student/TestTaking';
import { TestSessionProvider } from '../contexts/TestSessionContext';
import { QuestionType, AnswerOption } from '../types/test';
import { TestStatus } from '../types/student-test';

// Mock the TestSessionContext values
jest.mock('../contexts/TestSessionContext', () => {
  const originalModule = jest.requireActual('../contexts/TestSessionContext');
  
  return {
    ...originalModule,
    useTestSession: jest.fn(() => ({
      session: {
        test: {
          id: 'test-123',
          title: 'Mock IELTS Reading Test',
          content: '<div><h2>Reading Passage</h2><p>This is a sample reading passage.</p><h3>Question 1</h3><p>What is the main topic?</p></div>',
          timeLimit: 60,
          questions: [
            {
              number: 1,
              prompt: 'What is the main topic?',
              type: QuestionType.MultipleChoice,
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
            {
              number: 2,
              prompt: 'Explain your reasoning.',
              type: QuestionType.Essay,
            },
          ],
        },
        progress: {
          startedAt: new Date().toISOString(),
          status: TestStatus.IN_PROGRESS,
          answers: [],
          flaggedQuestions: [],
        },
        currentQuestion: 1,
      },
      isLoading: false,
      error: null,
      startTest: jest.fn(),
      saveAnswer: jest.fn(),
      toggleFlag: jest.fn(),
      submitTest: jest.fn(),
      pauseTest: jest.fn(),
      navigateToQuestion: jest.fn(),
    })),
  };
});

// Mock router params
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: jest.fn(() => ({ testId: 'test-123' })),
  useNavigate: jest.fn(() => jest.fn()),
}));

describe('TestTaking Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the two-panel layout correctly', () => {
    render(
      <MemoryRouter initialEntries={['/student/tests/test-123']}>
        <TestSessionProvider>
          <Routes>
            <Route path="/student/tests/:testId" element={<TestTaking />} />
          </Routes>
        </TestSessionProvider>
      </MemoryRouter>
    );

    // Test content panel
    expect(screen.getByText('Test Content')).toBeInTheDocument();
    expect(screen.getByText('Reading Passage')).toBeInTheDocument();
    expect(screen.getByText('This is a sample reading passage.')).toBeInTheDocument();
    
    // Answer sheet panel
    expect(screen.getByText('Answer Sheet')).toBeInTheDocument();
    
    // Check for multiple choice grid
    expect(screen.getByText('Multiple Choice Questions')).toBeInTheDocument();
    const tableHeaders = screen.getAllByRole('columnheader');
    expect(tableHeaders.length).toBeGreaterThanOrEqual(5); // Q#, A, B, C, D
    
    // Check for essay/fill-in-the-blank section
    expect(screen.getByText('Fill-in-the-Blank Questions')).toBeInTheDocument();
    
    // Verify there's a textarea for the text question
    const textareas = screen.getAllByPlaceholderText('Enter your answer here...');
    expect(textareas.length).toBeGreaterThanOrEqual(1);
  });

  test('allows answering multiple choice questions in the grid', async () => {
    const { useTestSession } = jest.requireActual('../contexts/TestSessionContext');
    const mockSaveAnswer = jest.fn();
    
    (useTestSession as jest.Mock).mockReturnValue({
      session: {
        test: {
          id: 'test-123',
          title: 'Mock IELTS Reading Test',
          content: '<div><h2>Reading Passage</h2><p>This is a sample reading passage.</p></div>',
          timeLimit: 60,
          questions: [
            {
              number: 1,
              prompt: 'What is the main topic?',
              type: QuestionType.MultipleChoice,
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
          ],
        },
        progress: {
          startedAt: new Date().toISOString(),
          status: TestStatus.IN_PROGRESS,
          answers: [],
          flaggedQuestions: [],
        },
        currentQuestion: 1,
      },
      isLoading: false,
      error: null,
      startTest: jest.fn(),
      saveAnswer: mockSaveAnswer,
      toggleFlag: jest.fn(),
      submitTest: jest.fn(),
      pauseTest: jest.fn(),
      navigateToQuestion: jest.fn(),
    });

    render(
      <MemoryRouter initialEntries={['/student/tests/test-123']}>
        <TestSessionProvider>
          <Routes>
            <Route path="/student/tests/:testId" element={<TestTaking />} />
          </Routes>
        </TestSessionProvider>
      </MemoryRouter>
    );

    // Get all radio buttons and click the second one (Option B)
    const radioButtons = screen.getAllByRole('radio');
    expect(radioButtons.length).toBe(4); // A, B, C, D
    
    fireEvent.click(radioButtons[1]); // Option B

    // Verify saveAnswer was called with the correct data
    await waitFor(() => {
      expect(mockSaveAnswer).toHaveBeenCalledWith(expect.objectContaining({
        questionNumber: 1,
        answer: 'Option B',
      }));
    });
  });

  test('allows flagging questions from the answer grid', async () => {
    const { useTestSession } = jest.requireActual('../contexts/TestSessionContext');
    const mockToggleFlag = jest.fn();
    
    (useTestSession as jest.Mock).mockReturnValue({
      session: {
        test: {
          id: 'test-123',
          title: 'Mock IELTS Reading Test',
          content: '<div><h2>Reading Passage</h2><p>This is a sample reading passage.</p></div>',
          timeLimit: 60,
          questions: [
            {
              number: 1,
              prompt: 'What is the main topic?',
              type: QuestionType.MultipleChoice,
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
            },
          ],
        },
        progress: {
          startedAt: new Date().toISOString(),
          status: TestStatus.IN_PROGRESS,
          answers: [],
          flaggedQuestions: [],
        },
        currentQuestion: 1,
      },
      isLoading: false,
      error: null,
      startTest: jest.fn(),
      saveAnswer: jest.fn(),
      toggleFlag: mockToggleFlag,
      submitTest: jest.fn(),
      pauseTest: jest.fn(),
      navigateToQuestion: jest.fn(),
    });

    render(
      <MemoryRouter initialEntries={['/student/tests/test-123']}>
        <TestSessionProvider>
          <Routes>
            <Route path="/student/tests/:testId" element={<TestTaking />} />
          </Routes>
        </TestSessionProvider>
      </MemoryRouter>
    );

    // Find the flag button in the table and click it
    const flagButton = screen.getByText('⚑');
    fireEvent.click(flagButton);

    // Verify toggleFlag was called with the correct question number
    expect(mockToggleFlag).toHaveBeenCalledWith(1);
  });

  test('handles mobile tab navigation', () => {
    // Mock a smaller viewport
    global.innerWidth = 500;
    global.dispatchEvent(new Event('resize'));
    
    render(
      <MemoryRouter initialEntries={['/student/tests/test-123']}>
        <TestSessionProvider>
          <Routes>
            <Route path="/student/tests/:testId" element={<TestTaking />} />
          </Routes>
        </TestSessionProvider>
      </MemoryRouter>
    );

    // Check that the tab navigation is rendered
    const contentTab = screen.getByText('Test Content');
    const answerSheetTab = screen.getByText('Answer Sheet');
    
    // Initially the content tab should be active
    expect(contentTab).toHaveClass('text-blue-600');
    
    // Click on answer sheet tab
    fireEvent.click(answerSheetTab);
    
    // Now the answer sheet tab should be active
    expect(answerSheetTab).toHaveClass('text-blue-600');
  });
}); 