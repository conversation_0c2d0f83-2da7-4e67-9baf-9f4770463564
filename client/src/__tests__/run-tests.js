const jest = require('jest');

// Run unit tests first
jest.run(['--testPathPattern=src/__tests__/.*\\.test\\.tsx?$']).then(() => {
  console.log('Unit tests completed. Starting end-to-end tests...');
  
  // Then run E2E tests
  jest.run(['--testPathPattern=src/__tests__/e2e/.*\\.e2e\\.test\\.js$'])
    .then(() => {
      console.log('All tests completed successfully!');
    })
    .catch(error => {
      console.error('E2E tests failed:', error);
      process.exit(1);
    });
}).catch(error => {
  console.error('Unit tests failed:', error);
  process.exit(1);
}); 