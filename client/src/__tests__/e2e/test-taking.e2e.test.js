// @ts-check
const puppeteer = require('puppeteer');

describe('Test Taking Page E2E', () => {
  let browser;
  let page;

  beforeAll(async () => {
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
  });

  afterAll(async () => {
    await browser.close();
  });

  beforeEach(async () => {
    page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 800 });
    
    // Mock API responses
    await page.setRequestInterception(true);
    page.on('request', async (request) => {
      if (request.url().includes('/api/student/tests/test-123')) {
        await request.respond({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            test: {
              id: 'test-123',
              title: 'Mock IELTS Reading Test',
              content: '<div><h2>Reading Passage</h2><p>This is a sample reading passage for testing purposes.</p><p>It includes multiple paragraphs and should display properly.</p></div>',
              timeLimit: 60,
              questions: [
                {
                  number: 1,
                  prompt: 'What is the main topic?',
                  type: 'multipleChoice',
                  options: ['Option A', 'Option B', 'Option C', 'Option D'],
                },
                {
                  number: 2,
                  prompt: 'What is mentioned in paragraph 2?',
                  type: 'multipleChoice',
                  options: ['Point 1', 'Point 2', 'Point 3', 'Point 4'],
                },
                {
                  number: 3,
                  prompt: 'Describe the passage in your own words.',
                  type: 'essay',
                },
              ],
              type: 'READING',
              difficulty: 'MEDIUM',
            },
            progress: {
              startedAt: new Date().toISOString(),
              status: 'IN_PROGRESS',
              answers: [],
              flaggedQuestions: [],
            }
          }),
        });
        return;
      }
      
      if (request.url().includes('/api/student/tests/test-123/progress')) {
        await request.respond({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true }),
        });
        return;
      }
      
      request.continue();
    });
    
    // Navigate to the test taking page
    await page.goto('http://localhost:3000/student/tests/test-123');
  });

  test('renders the new TOEIC-style answer sheet grid', async () => {
    // Wait for content to load
    await page.waitForSelector('.prose');
    
    // Check for the answer sheet
    const answerSheetHeading = await page.$('h2:contains("Answer Sheet")');
    expect(answerSheetHeading).not.toBeNull();
    
    // Check for the multiple choice grid
    const mcHeading = await page.$('h3:contains("Multiple Choice Questions")');
    expect(mcHeading).not.toBeNull();
    
    // Check for the grid table
    const tableHeaders = await page.$$('th');
    expect(tableHeaders.length).toBeGreaterThanOrEqual(6); // Q#, A, B, C, D, Flag
    
    // Check for the text input section
    const textHeading = await page.$('h3:contains("Fill-in-the-Blank Questions")');
    expect(textHeading).not.toBeNull();
    
    // Verify there are radio buttons in the grid
    const radioButtons = await page.$$('input[type="radio"]');
    expect(radioButtons.length).toBeGreaterThanOrEqual(8); // 2 questions x 4 options
  });

  test('allows selecting answers in the grid', async () => {
    // Wait for content to load
    await page.waitForSelector('.prose');
    
    // Click the first radio button for question 1 (Option A)
    await page.evaluate(() => {
      const rows = document.querySelectorAll('table tbody tr');
      const firstRadio = rows[0].querySelectorAll('input[type="radio"]')[0];
      firstRadio.click();
    });
    
    // Wait for the answer to be saved
    await page.waitForTimeout(500);
    
    // Verify the radio button is checked
    const isChecked = await page.evaluate(() => {
      const rows = document.querySelectorAll('table tbody tr');
      const firstRadio = rows[0].querySelectorAll('input[type="radio"]')[0];
      return firstRadio.checked;
    });
    
    expect(isChecked).toBe(true);
  });

  test('allows switching between mobile tabs', async () => {
    // Resize to mobile viewport
    await page.setViewport({ width: 375, height: 667 });
    
    // Wait for content to load
    await page.waitForSelector('.prose');
    
    // Verify tab buttons exist
    const tabs = await page.$$('.lg\\:hidden button');
    expect(tabs.length).toBe(2);
    
    // Click the Answer Sheet tab
    await tabs[1].click();
    
    // Verify the answer sheet is visible
    const isAnswerSheetVisible = await page.evaluate(() => {
      const answerSheet = document.querySelectorAll('div[class*="lg:col-span-1"]')[0];
      const computedStyle = window.getComputedStyle(answerSheet);
      return computedStyle.display !== 'none';
    });
    
    expect(isAnswerSheetVisible).toBe(true);
    
    // Click the Test Content tab
    await tabs[0].click();
    
    // Verify the test content is visible
    const isContentVisible = await page.evaluate(() => {
      const content = document.querySelectorAll('div[class*="lg:col-span-2"]')[0];
      const computedStyle = window.getComputedStyle(content);
      return computedStyle.display !== 'none';
    });
    
    expect(isContentVisible).toBe(true);
  });
  
  test('flagging functionality works in the grid', async () => {
    // Wait for content to load
    await page.waitForSelector('.prose');
    
    // Find and click the flag button for the first question
    const flagBtn = await page.evaluateHandle(() => {
      const rows = document.querySelectorAll('table tbody tr');
      return rows[0].querySelector('button:contains("⚑")');
    });
    
    await flagBtn.click();
    
    // Wait for the UI to update
    await page.waitForTimeout(500);
    
    // Verify the question is now flagged (background color changed)
    const isFlagged = await page.evaluate(() => {
      const rows = document.querySelectorAll('table tbody tr');
      return rows[0].classList.contains('bg-yellow-50');
    });
    
    expect(isFlagged).toBe(true);
  });
}); 