import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { I18nextProvider } from 'react-i18next';
import i18n from '../i18n';
import Groups from '../pages/teacher/Groups';
import GroupForm from '../pages/teacher/GroupForm';
import GroupDetail from '../pages/teacher/GroupDetail';
import GroupResults from '../pages/teacher/GroupResults';
import { AuthProvider } from '../contexts/AuthContext';
import { UserRole } from '../types/user';

// Mock the teacherService
jest.mock('../services/teacherService', () => ({
  getAllGroups: jest.fn(),
  getGroupById: jest.fn(),
  createGroup: jest.fn(),
  updateGroup: jest.fn(),
  deleteGroup: jest.fn(),
  addStudentsToGroup: jest.fn(),
  removeStudentFromGroup: jest.fn(),
  assignTestToGroup: jest.fn(),
  removeTestFromGroup: jest.fn(),
  getGroupResults: jest.fn(),
  getStudentResults: jest.fn(),
}));

// Mock the auth context
jest.mock('../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useAuth: () => ({
    user: {
      id: 'teacher-id',
      role: UserRole.TEACHER,
      name: 'Test Teacher',
    },
    isAuthenticated: true,
    isLoading: false,
  }),
}));

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  success: jest.fn(),
  error: jest.fn(),
}));

describe('Teacher Group Management', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('Groups page renders correctly', async () => {
    const mockGroups = [
      {
        id: 'group-1',
        name: 'Beginner Group',
        level: 'Beginner',
        description: 'Group for beginners',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isActive: true,
        createdBy: 'teacher-id',
        students: [],
        assignments: [],
      },
    ];

    const teacherService = require('../services/teacherService');
    teacherService.getAllGroups.mockResolvedValue(mockGroups);

    render(
      <QueryClientProvider client={queryClient}>
        <I18nextProvider i18n={i18n}>
          <MemoryRouter initialEntries={['/teacher/groups']}>
            <Routes>
              <Route path="/teacher/groups" element={<Groups />} />
            </Routes>
          </MemoryRouter>
        </I18nextProvider>
      </QueryClientProvider>
    );

    // Wait for the groups to load
    await waitFor(() => {
      expect(teacherService.getAllGroups).toHaveBeenCalled();
    });

    // Check if the group name is displayed
    expect(screen.getByText('Beginner Group')).toBeInTheDocument();
  });

  test('GroupForm creates a new group', async () => {
    const teacherService = require('../services/teacherService');
    teacherService.createGroup.mockResolvedValue({
      id: 'new-group-id',
      name: 'New Group',
      level: 'Intermediate',
      description: 'New group description',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
      createdBy: 'teacher-id',
    });

    render(
      <QueryClientProvider client={queryClient}>
        <I18nextProvider i18n={i18n}>
          <MemoryRouter initialEntries={['/teacher/groups/new']}>
            <Routes>
              <Route path="/teacher/groups/new" element={<GroupForm />} />
            </Routes>
          </MemoryRouter>
        </I18nextProvider>
      </QueryClientProvider>
    );

    // Fill out the form
    const nameInput = screen.getByLabelText(/name/i);
    const levelSelect = screen.getByLabelText(/level/i);
    const descriptionTextarea = screen.getByLabelText(/description/i);

    await userEvent.type(nameInput, 'New Group');
    await userEvent.selectOptions(levelSelect, 'Intermediate');
    await userEvent.type(descriptionTextarea, 'New group description');

    // Submit the form
    const saveButton = screen.getByRole('button', { name: /save/i });
    await userEvent.click(saveButton);

    // Check if the createGroup function was called with the correct data
    await waitFor(() => {
      expect(teacherService.createGroup).toHaveBeenCalledWith({
        name: 'New Group',
        level: 'Intermediate',
        description: 'New group description',
      });
    });
  });

  // Additional tests would be added for other components and functionality
});
