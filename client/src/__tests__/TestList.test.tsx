import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import TestList from '../pages/student/TestList';
import { TestDifficulty, TestType } from '../types/test';

// Mock the useQuery hook
jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(() => ({
    data: {
      tests: [
        {
          id: 'test-1',
          title: 'IELTS Reading Practice Test',
          description: '<p>This is a very long description that should be truncated in the UI. It contains a lot of text to ensure the truncation functionality works correctly. We want to make sure it doesn\'t break the UI.</p>',
          timeLimit: 60,
          difficulty: TestDifficulty.MEDIUM,
          type: TestType.READING,
          category: { name: 'Academic' },
        },
        {
          id: 'test-2',
          title: 'IELTS Listening Test',
          description: '<p>Short description</p>',
          timeLimit: 30,
          difficulty: TestDifficulty.EASY,
          type: TestType.LISTENING,
          category: { name: 'General Training' },
        },
      ],
      meta: {
        totalItems: 2,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1
      }
    },
    isLoading: false,
    error: null,
  })),
}));

describe('TestList Component', () => {
  test('renders all test cards correctly', () => {
    render(
      <MemoryRouter>
        <TestList />
      </MemoryRouter>
    );

    // Check if both test titles are displayed
    expect(screen.getByText('IELTS Reading Practice Test')).toBeInTheDocument();
    expect(screen.getByText('IELTS Listening Test')).toBeInTheDocument();

    // Verify difficulty badges
    expect(screen.getByText('MEDIUM')).toBeInTheDocument();
    expect(screen.getByText('EASY')).toBeInTheDocument();

    // Verify test type and category info
    expect(screen.getByText('Reading')).toBeInTheDocument();
    expect(screen.getByText('Listening')).toBeInTheDocument();
    expect(screen.getByText('Academic')).toBeInTheDocument();
    expect(screen.getByText('General Training')).toBeInTheDocument();

    // Check time limits
    expect(screen.getByText('60 minutes')).toBeInTheDocument();
    expect(screen.getByText('30 minutes')).toBeInTheDocument();
  });

  test('truncates long descriptions', () => {
    render(
      <MemoryRouter>
        <TestList />
      </MemoryRouter>
    );

    // Verify the truncation helper works by checking the text content
    // The text content should not contain the full long description
    const testCards = screen.getAllByText(/Start Test/i);
    expect(testCards.length).toBe(2);

    // Should not find the full text of the long description
    const fullDescriptionText = 'This is a very long description that should be truncated in the UI. It contains a lot of text to ensure the truncation functionality works correctly. We want to make sure it doesn\'t break the UI.';
    const pageContent = document.body.textContent;
    expect(pageContent).not.toContain(fullDescriptionText);
  });
});