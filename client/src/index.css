@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom form styles */
@layer components {
  .form-input {
    @apply block w-full rounded-md border border-gray-300 shadow-sm;
    @apply focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
    @apply py-2 px-3;
    height: auto;
    min-height: 38px;
  }

  .form-select {
    @apply block w-full rounded-md border border-gray-300 shadow-sm;
    @apply focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
    @apply py-2 pl-3 pr-10;
    height: auto;
    min-height: 38px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .form-textarea {
    @apply block w-full rounded-md border border-gray-300 shadow-sm;
    @apply focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
    @apply py-2 px-3;
    min-height: 80px;
  }

  .form-checkbox {
    @apply rounded border-gray-300 text-blue-600;
    @apply focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
    height: 1rem;
    width: 1rem;
  }

  .form-radio {
    @apply border-gray-300 text-blue-600;
    @apply focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
    height: 1rem;
    width: 1rem;
  }
}
