import { Test, MultipleChoiceAnswer, TextAnswer } from './test';

export enum TestStatus {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  EXPIRED = 'EXPIRED'
}

export enum AnswerState {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  GRADED = 'GRADED'
}

export interface StudentAnswer {
  questionNumber: number;
  answer: string | string[];
  isCorrect?: boolean;
  feedback?: string;
  correctAnswer?: string | string[];
}

export interface TestProgress {
  testId: string;
  studentId: string;
  status: TestStatus;
  answers: StudentAnswer[];
  flaggedQuestions: number[];
  timeSpent: number;
  lastSaved: string;
  startedAt: string;
  score?: number;
  submittedAt?: string;
}

export interface TestResult {
  id: string;
  testId: string;
  userId: string;
  studentId?: string; // For backward compatibility
  score: number;
  maxScore: number;
  totalQuestions: number;
  correctAnswers: number;
  incorrectAnswers: number;
  timeSpent: number;
  submittedAt: string | Date;
  answers: StudentAnswer[] | string;
  testTitle?: string; // Title of the test
}

export interface TestSession {
  test: Test;
  progress: TestProgress;
  currentQuestion: number;
  timeRemaining?: number;
}