import { Test } from './test';

export enum QuestionType {
  MULTIPLE_CHOICE = 'multipleChoice',
  FILL_IN_THE_BLANK = 'fillInTheBlank',
}

export interface IncorrectAnswer {
  answer: string;
  count: number;
}

export interface QuestionAnalysis {
  questionNumber: number;
  questionText: string;
  questionType: QuestionType;
  correctAnswer: string | string[];
  correctCount: number;
  incorrectCount: number;
  incorrectAnswers: IncorrectAnswer[];
  skippedCount: number;
}

export interface TestQuestionAnalysis {
  testId: string;
  testTitle: string;
  groupId: string;
  totalStudents: number;
  completedCount: number;
  averageScore: number;
  questions: QuestionAnalysis[];
}

export interface Group {
  id: string;
  name: string;
  level: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  createdBy: string;
  students?: StudentGroup[];
  assignments?: GroupAssignment[];
}

export interface StudentGroup {
  id: string;
  groupId: string;
  studentId: string;
  joinedAt: string;
  student: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface GroupAssignment {
  id: string;
  groupId: string;
  testId: string;
  assignedAt: string;
  dueDate?: string;
  test: Test;
}

export interface CreateGroupDto {
  name: string;
  level: string;
  description?: string;
}

export interface UpdateGroupDto {
  name?: string;
  level?: string;
  description?: string;
}

export interface AddStudentsDto {
  studentIds: string[];
}

export interface AssignTestDto {
  testId: string;
  dueDate?: string;
}

export interface GroupResults {
  groupId: string;
  groupName: string;
  studentCount: number;
  testCount: number;
  averageScore: number;
  completionRate: number;
  students: {
    id: string;
    name: string;
    email: string;
    testsCompleted: number;
    averageScore: number;
  }[];
}

export interface StudentResults {
  studentId: string;
  studentName: string;
  email: string;
  groupId: string;
  testsAssigned: number;
  testsCompleted: number;
  averageScore: number;
  testResults: {
    testId: string;
    testTitle: string;
    assignedAt: string;
    dueDate?: string;
    completed: boolean;
    score: number | null;
  }[];
}

export interface CreateGroupInviteDto {
  expiresInDays?: number;
  maxUses?: number;
}

export interface GroupInvite {
  id: string;
  token: string;
  createdAt: string;
  expiresAt: string;
  maxUses?: number;
  useCount: number;
  isActive: boolean;
  lastUsedAt?: string;
  inviteUrl: string;
}
