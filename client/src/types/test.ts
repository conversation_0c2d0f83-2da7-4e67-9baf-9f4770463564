import { User } from './auth';
import { Category } from './category';

export enum TestType {
  READING = 'READING',
  LISTENING = 'LISTENING',
  WRITING = 'WRITING',
  SPEAKING = 'SPEAKING',
  FULL_TEST = 'FULL_TEST',
}

export enum TestDifficulty {
  EASY = 'EASY',
  MEDIUM = 'MEDIUM',
  HARD = 'HARD',
}

export enum QuestionType {
  MultipleChoice = 'multipleChoice',
  TrueFalse = 'trueFalse',
  FillInTheBlank = 'fillInTheBlank',
  Essay = 'essay',
  Matching = 'matching',
}

export enum AnswerOption {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
}

export interface MultipleChoiceAnswer {
  questionNumber: number | undefined;
  correctOption: AnswerOption;
}

export interface TextAnswer {
  questionNumber: number | undefined;
  correctAnswers: string[];
  caseSensitive: boolean;
}

export interface AnswerSheet {
  multipleChoiceAnswers: MultipleChoiceAnswer[];
  textAnswers: TextAnswer[];
}

export interface Question {
  _id: string;
  prompt: string;
  type: QuestionType;
  options?: string[];
  correctAnswer?: string | string[];
  points: number;
}

export interface Section {
  _id: string;
  title: string;
  description?: string;
  questions: Question[];
}

export interface Test {
  id: string;
  title: string;
  description: string;
  type: TestType;
  difficulty: TestDifficulty;
  timeLimit: number;
  maxScore: number;
  instructions?: string;
  isPublished: boolean;
  isActive: boolean;
  categoryId: string;
  category?: Category;
  language?: string; // Language code (e.g., 'en', 'vi', 'ar', etc.)
  answerSheet?: {
    multipleChoiceAnswers: MultipleChoiceAnswer[];
    textAnswers: TextAnswer[];
  };
  questions: Question[];
  content?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  sections?: Section[];
}

export interface CreateTestDto {
  title: string;
  description?: string;
  type: TestType;
  difficulty?: TestDifficulty;
  category: string;
  content: any;
  durationMinutes?: number;
}

export interface UpdateTestDto {
  title?: string;
  description?: string;
  type?: TestType;
  difficulty?: TestDifficulty;
  category?: string;
  content?: any;
  durationMinutes?: number;
  isPublished?: boolean;
}