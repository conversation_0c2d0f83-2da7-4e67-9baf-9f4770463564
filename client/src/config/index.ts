// API URL configuration with proper fallback logic
const getApiUrl = () => {
  // First try environment variable
  if (import.meta.env.VITE_API_URL) {
    return import.meta.env.VITE_API_URL;
  }

  // Production fallback - use the known Railway URL
  if (import.meta.env.PROD) {
    return 'https://server-nest-production.up.railway.app/api';
  }

  // Development fallback - use localhost
  return 'http://localhost:5000/api';
};

export const API_URL = getApiUrl();

// Debug logging for API configuration
console.log('🔧 API Configuration:', {
  VITE_API_URL: import.meta.env.VITE_API_URL,
  API_URL: API_URL,
  mode: import.meta.env.MODE,
  prod: import.meta.env.PROD,
  fallbackUsed: !import.meta.env.VITE_API_URL
});

// Other configuration variables
export const APP_NAME = 'IELTS Toolkit';
export const DEFAULT_LANGUAGE = 'en';
export const SUPPORTED_LANGUAGES = ['en', 'vi'];
