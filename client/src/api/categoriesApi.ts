import { AxiosResponse } from 'axios';
import { Category } from '../types/category';
import axiosClient from './axiosClient';

const BASE_URL = '/categories';

export interface CategoryResponse extends Category {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export const categoriesApi = {
  // Get all categories (filtered by user role)
  getAll: async (): Promise<Category[]> => {
    const response: AxiosResponse<Category[]> = await axiosClient.get(BASE_URL);
    return response.data;
  },

  // Get all public categories (no authentication required)
  getAllPublic: async (): Promise<Category[]> => {
    const response: AxiosResponse<Category[]> = await axiosClient.get(`${BASE_URL}/public`);
    return response.data;
  },

  // Get a single category
  getById: async (id: string): Promise<Category> => {
    const response: AxiosResponse<Category> = await axiosClient.get(`${BASE_URL}/${id}`);
    return response.data;
  },

  // Create a new category
  create: async (data: Omit<Category, 'id'>): Promise<CategoryResponse> => {
    const response: AxiosResponse<CategoryResponse> = await axiosClient.post(BASE_URL, data);
    return response.data;
  },

  // Update a category
  update: async (id: string, data: Partial<Category>): Promise<CategoryResponse> => {
    const response: AxiosResponse<CategoryResponse> = await axiosClient.patch(`${BASE_URL}/${id}`, data);
    return response.data;
  },

  // Delete a category
  delete: async (id: string): Promise<void> => {
    await axiosClient.delete(`${BASE_URL}/${id}`);
  },

  // Toggle active status
  toggleActive: async (id: string): Promise<Category> => {
    const response = await axiosClient.patch(`${BASE_URL}/${id}/toggle-active`);
    return response.data;
  },

  // Legacy/backward compatibility methods
  getCategories: async (): Promise<Category[]> => {
    return categoriesApi.getAll();
  },

  getCategory: async (id: string): Promise<Category> => {
    return categoriesApi.getById(id);
  },

  createCategory: async (data: Omit<Category, 'id'>): Promise<CategoryResponse> => {
    return categoriesApi.create(data);
  },

  updateCategory: async (id: string, data: Partial<Category>): Promise<CategoryResponse> => {
    return categoriesApi.update(id, data);
  },

  deleteCategory: async (id: string): Promise<void> => {
    return categoriesApi.delete(id);
  }
};