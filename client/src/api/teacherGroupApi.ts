import axiosClient from './axiosClient';

// Types
export interface GroupInvite {
  id: string;
  token: string;
  createdAt: string;
  expiresAt: string;
  maxUses?: number;
  useCount: number;
  isActive: boolean;
  lastUsedAt?: string;
  inviteUrl: string;
}

export interface CreateGroupInviteDto {
  expiresInDays?: number; // Default to 7 if not provided
  maxUses?: number; // Optional, unlimited if not provided
}

export interface Group {
  id: string;
  name: string;
  level: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  createdBy: string;
  students?: StudentGroup[];
  assignments?: GroupAssignment[];
}

export interface StudentGroup {
  id: string;
  groupId: string;
  studentId: string;
  joinedAt: string;
  student?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface GroupAssignment {
  id: string;
  groupId: string;
  testId: string;
  assignedAt: string;
  dueDate?: string;
  test?: {
    id: string;
    title: string;
    description?: string;
    type: string;
    difficulty: string;
  };
}

export interface CreateGroupDto {
  name: string;
  level: string;
  description?: string;
}

export interface UpdateGroupDto {
  name?: string;
  level?: string;
  description?: string;
  isActive?: boolean;
}

export interface AddStudentsDto {
  studentIds: string[];
}

export interface AssignTestDto {
  testId: string;
  dueDate?: string;
}

export interface GroupResults {
  groupId: string;
  groupName: string;
  studentCount: number;
  testCount: number;
  averageScore: number;
  completionRate: number;
  students: {
    id: string;
    name: string;
    email: string;
    testsCompleted: number;
    averageScore: number;
  }[];
}

export interface StudentResults {
  studentId: string;
  studentName: string;
  email: string;
  groupId: string;
  testsAssigned: number;
  testsCompleted: number;
  averageScore: number;
  testResults: {
    testId: string;
    testTitle: string;
    assignedAt: string;
    dueDate?: string;
    completed: boolean;
    score: number | null;
  }[];
}

// API Service
const teacherGroupApi = {
  // Group Management
  getAllGroups: async (): Promise<Group[]> => {
    const response = await axiosClient.get('/teacher/groups');
    return response.data;
  },

  getGroupById: async (id: string): Promise<Group> => {
    const response = await axiosClient.get(`/teacher/groups/${id}`);
    return response.data;
  },

  createGroup: async (createGroupDto: CreateGroupDto): Promise<Group> => {
    const response = await axiosClient.post('/teacher/groups', createGroupDto);
    return response.data;
  },

  updateGroup: async (id: string, updateGroupDto: UpdateGroupDto): Promise<Group> => {
    const response = await axiosClient.patch(`/teacher/groups/${id}`, updateGroupDto);
    return response.data;
  },

  deleteGroup: async (id: string): Promise<Group> => {
    const response = await axiosClient.delete(`/teacher/groups/${id}`);
    return response.data;
  },

  // Student Management
  getAllStudents: async (): Promise<any[]> => {
    const response = await axiosClient.get('/teacher/students');
    return response.data;
  },

  getStudentsInGroup: async (groupId: string): Promise<StudentGroup[]> => {
    const response = await axiosClient.get(`/teacher/groups/${groupId}/students`);
    return response.data;
  },

  addStudentsToGroup: async (groupId: string, addStudentsDto: AddStudentsDto): Promise<StudentGroup[]> => {
    const response = await axiosClient.post(`/teacher/groups/${groupId}/students`, addStudentsDto);
    return response.data;
  },

  removeStudentFromGroup: async (groupId: string, studentId: string): Promise<void> => {
    await axiosClient.delete(`/teacher/groups/${groupId}/students/${studentId}`);
  },

  // Test Assignment
  getTestsAssignedToGroup: async (groupId: string): Promise<GroupAssignment[]> => {
    const response = await axiosClient.get(`/teacher/groups/${groupId}/tests`);
    return response.data;
  },

  assignTestToGroup: async (groupId: string, assignTestDto: AssignTestDto): Promise<GroupAssignment> => {
    const response = await axiosClient.post(`/teacher/groups/${groupId}/tests`, assignTestDto);
    return response.data;
  },

  removeTestFromGroup: async (groupId: string, testId: string): Promise<void> => {
    await axiosClient.delete(`/teacher/groups/${groupId}/tests/${testId}`);
  },

  // Results and Analytics
  getGroupResults: async (groupId: string): Promise<GroupResults> => {
    const response = await axiosClient.get(`/teacher/groups/${groupId}/results`);
    return response.data;
  },

  getStudentResults: async (groupId: string, studentId: string): Promise<StudentResults> => {
    const response = await axiosClient.get(`/teacher/groups/${groupId}/students/${studentId}/results`);
    return response.data;
  },

  getTestQuestionAnalysis: async (groupId: string, testId: string): Promise<TestQuestionAnalysis> => {
    const response = await axiosClient.get(`/teacher/groups/${groupId}/tests/${testId}/results`);
    return response.data;
  },

  // Group Invitation
  createGroupInvite: async (groupId: string, data: CreateGroupInviteDto): Promise<GroupInvite> => {
    const response = await axiosClient.post(`/teacher/groups/${groupId}/invites`, data);
    return response.data;
  },
};

export { teacherGroupApi };
