import axiosClient from './axiosClient';
import { LoginCredentials, RegisterData, AuthResponse } from '../types/auth';

const authApi = {
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await axiosClient.post('/auth/login', credentials);
    return response.data;
  },

  register: async (data: RegisterData): Promise<AuthResponse> => {
    const response = await axiosClient.post('/auth/register', data);
    return response.data;
  },

  getCurrentUser: async (): Promise<AuthResponse> => {
    const response = await axiosClient.get('/auth/me');
    return response.data;
  },

  changePassword: async (oldPassword: string, newPassword: string): Promise<{ message: string }> => {
    const response = await axiosClient.post('/auth/change-password', {
      oldPassword,
      newPassword,
    });
    return response.data;
  },

  requestPasswordReset: async (email: string): Promise<{ message: string }> => {
    const response = await axiosClient.post('/auth/request-reset-password', { email });
    return response.data;
  },

  resetPassword: async (token: string, newPassword: string): Promise<{ message: string }> => {
    const response = await axiosClient.post('/auth/reset-password', {
      token,
      newPassword,
    });
    return response.data;
  },
};

export { authApi }; 