import { AxiosResponse } from 'axios';
import { Test } from '../types/test';
import { TestProgress, TestResult, StudentAnswer } from '../types/student-test';
import axiosClient from './axiosClient';

// No need to include /api prefix as it's already in axiosInstance baseURL
const BASE_URL = '/student/tests';

export interface PaginatedResponse<T> {
  tests: T[];
  meta: {
    totalItems: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

export const studentTestApi = {
  // Get list of published tests with pagination
  getPublishedTests: async (page: number = 1, limit: number = 10): Promise<PaginatedResponse<Test>> => {
    const response: AxiosResponse<any> = await axiosClient.get(`${BASE_URL}/published`, {
      params: { page, limit }
    });

    // Handle the paginated response format
    if (response.data && typeof response.data === 'object') {
      // If the response has tests and meta properties, it's already in the correct format
      if (Array.isArray(response.data.tests) && response.data.meta) {
        return response.data as PaginatedResponse<Test>;
      }

      // If the response has only tests array but no meta
      if (Array.isArray(response.data.tests) && !response.data.meta) {
        return {
          tests: response.data.tests,
          meta: {
            totalItems: response.data.tests.length,
            itemsPerPage: limit,
            totalPages: 1,
            currentPage: page
          }
        };
      }

      // If the response is an array directly
      if (Array.isArray(response.data)) {
        return {
          tests: response.data,
          meta: {
            totalItems: response.data.length,
            itemsPerPage: limit,
            totalPages: 1,
            currentPage: page
          }
        };
      }
    }

    // Fallback to empty paginated response if nothing valid was found
    console.error('Unexpected response format from tests/published endpoint:', response.data);
    return {
      tests: [],
      meta: {
        totalItems: 0,
        itemsPerPage: limit,
        totalPages: 0,
        currentPage: page
      }
    };
  },

  // Get test details with progress
  getTestWithProgress: async (testId: string): Promise<{ test: Test; progress?: TestProgress }> => {
    const response: AxiosResponse<{ test: Test; progress?: TestProgress }> =
      await axiosClient.get(`${BASE_URL}/${testId}`);
    return response.data;
  },

  // Start a test
  startTest: async (testId: string): Promise<TestProgress> => {
    const response: AxiosResponse<TestProgress> = await axiosClient.post(`${BASE_URL}/${testId}/start`);
    return response.data;
  },

  // Save test progress
  saveProgress: async (
    testId: string,
    progressData: {
      answers?: StudentAnswer[],
      flaggedQuestions?: number[]
    }
  ): Promise<TestProgress> => {
    const response: AxiosResponse<TestProgress> = await axiosClient.patch(
      `${BASE_URL}/${testId}/progress`,
      progressData
    );
    return response.data;
  },

  // Flag/unflag question
  toggleQuestionFlag: async (testId: string, questionNumber: number): Promise<TestProgress> => {
    const response: AxiosResponse<TestProgress> = await axiosClient.post(
      `${BASE_URL}/${testId}/flag/${questionNumber}`
    );
    return response.data;
  },

  // Submit test
  submitTest: async (testId: string): Promise<TestResult> => {
    const response: AxiosResponse<TestResult> = await axiosClient.post(`${BASE_URL}/${testId}/submit`);
    return response.data;
  },

  // Get test result
  getTestResult: async (testId: string, resultId?: string): Promise<TestResult> => {
    const url = resultId
      ? `${BASE_URL}/${testId}/result/${resultId}`
      : `${BASE_URL}/${testId}/result`;
    const response: AxiosResponse<TestResult> = await axiosClient.get(url);
    return response.data;
  },

  // Get all test results for student
  getTestResults: async (testId?: string): Promise<TestResult[]> => {
    const url = testId
      ? `${BASE_URL}/${testId}/results`
      : `/student/results`;
    const response: AxiosResponse<TestResult[]> = await axiosClient.get(url, {
      params: { testId }
    });
    return response.data;
  },

  // Pause test
  pauseTest: async (testId: string): Promise<TestProgress> => {
    const response: AxiosResponse<TestProgress> = await axiosClient.post(`${BASE_URL}/${testId}/pause`);
    return response.data;
  },

  // Resume test
  resumeTest: async (testId: string): Promise<TestProgress> => {
    const response: AxiosResponse<TestProgress> = await axiosClient.post(`${BASE_URL}/${testId}/resume`);
    return response.data;
  }
};