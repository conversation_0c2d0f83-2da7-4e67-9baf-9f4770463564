import axiosClient from './axiosClient';
import { User } from '../types/auth';
import { CreateUserDto, UpdateUserDto } from '../types/user';

export const usersApi = {
  getAll: async (): Promise<User[]> => {
    const response = await axiosClient.get<User[]>('/users');
    return response.data;
  },
  
  getById: async (id: string): Promise<User> => {
    const response = await axiosClient.get<User>(`/users/${id}`);
    return response.data;
  },
  
  create: async (data: CreateUserDto): Promise<User> => {
    const response = await axiosClient.post<User>('/users', data);
    return response.data;
  },
  
  update: async (id: string, data: UpdateUserDto): Promise<User> => {
    const response = await axiosClient.patch<User>(`/users/${id}`, data);
    return response.data;
  },
  
  delete: async (id: string): Promise<void> => {
    await axiosClient.delete(`/users/${id}`);
  },
  
  toggleActive: async (id: string): Promise<User> => {
    const response = await axiosClient.patch<User>(`/users/${id}/toggle-active`);
    return response.data;
  },
}; 