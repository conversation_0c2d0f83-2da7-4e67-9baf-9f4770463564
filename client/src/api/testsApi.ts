import axiosClient from './axiosClient';
import { Test, MultipleChoiceAnswer, TextAnswer } from '../types/test';

interface CreateTestDto {
  title: string;
  description: string;
  instructions: string;
  type: string;
  difficulty: string;
  categoryId: string;
  timeLimit: number;
  maxScore: number;
  answerSheet?: {
    multipleChoiceAnswers?: MultipleChoiceAnswer[];
    textAnswers?: TextAnswer[];
  };
}

interface UpdateTestDto {
  title?: string;
  description?: string;
  instructions?: string;
  type?: string;
  difficulty?: string;
  categoryId?: string;
  timeLimit?: number;
  maxScore?: number;
  isPublished?: boolean;
  isActive?: boolean;
  answerSheet?: {
    multipleChoiceAnswers?: MultipleChoiceAnswer[];
    textAnswers?: TextAnswer[];
  };
}

const testsApi = {
  getAll: async (): Promise<Test[]> => {
    const response = await axiosClient.get('/tests');
    return response.data;
  },

  getById: async (id: string): Promise<Test> => {
    const response = await axiosClient.get(`/tests/${id}`);
    return response.data;
  },

  create: async (data: CreateTestDto): Promise<Test> => {
    const response = await axiosClient.post('/tests', data);
    return response.data;
  },

  update: async (id: string, data: Partial<Test>): Promise<Test> => {
    // Extract only the allowed fields for update
    const updateData: UpdateTestDto = {
      title: data.title,
      description: data.description,
      type: data.type,
      difficulty: data.difficulty,
      timeLimit: data.timeLimit,
      maxScore: data.maxScore,
      instructions: data.instructions,
      categoryId: data.categoryId,
      isPublished: data.isPublished,
      isActive: data.isActive,
      answerSheet: data.answerSheet
    };

    const response = await axiosClient.put(`/tests/${id}`, updateData);
    return response.data;
  },

  delete: async (id: string): Promise<Test> => {
    const response = await axiosClient.delete(`/tests/${id}`);
    return response.data;
  },

  togglePublished: async (id: string): Promise<Test> => {
    const response = await axiosClient.patch(`/tests/${id}/toggle-published`);
    return response.data;
  },

  toggleActive: async (id: string): Promise<Test> => {
    const response = await axiosClient.patch(`/tests/${id}/toggle-active`);
    return response.data;
  },
};

export { testsApi };
export type { CreateTestDto, UpdateTestDto };