import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from './contexts/ThemeContext';

// Pages
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import TestsList from './pages/tests/TestsList';
import TestDetails from './pages/tests/TestDetails';
import CreateTest from './pages/tests/TestCreate';
import EditTest from './pages/tests/TestEdit';
import CategoriesList from './pages/categories/CategoriesList';
import CategoriesCreate from './pages/categories/CategoriesCreate';
import CategoryDetail from './pages/categories/CategoryDetail';
import CategoryEdit from './pages/categories/CategoryEdit';
import UsersList from './pages/admin/UsersList';
import StudentTestList from './pages/student/TestList';
import TestTaking from './pages/student/TestTaking';
import TestResultView from './pages/student/TestResultView';
import TestResultsList from './pages/student/TestResultsList';

// Teacher Group Management
import Groups from './pages/teacher/Groups';
import GroupForm from './pages/teacher/GroupForm';
import GroupDetail from './pages/teacher/GroupDetail';
import GroupResults from './pages/teacher/GroupResults';
import StudentProfile from './pages/teacher/StudentProfile';
import TestQuestionReport from './pages/teacher/TestQuestionReport';

// Components
import MainLayout from './components/layout/MainLayout';
import ProtectedRoute from './components/ProtectedRoute';
import RoleBasedRoute from './components/RoleBasedRoute';
import { AuthProvider } from './contexts/AuthContext';
import { TestSessionProvider } from './contexts/TestSessionContext';
import { UserRole } from './types/user';
import { NotificationContainer } from './components/ui/Notification';

// Create a client
const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <ThemeProvider>
          <TestSessionProvider>
            <Router>
              <NotificationContainer />
              <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />

              {/* Protected Routes */}
              <Route element={<ProtectedRoute />}>
                <Route element={<MainLayout />}>
                  {/* Admin Routes */}
                  <Route element={<RoleBasedRoute allowedRoles={[UserRole.ADMIN]} />}>
                    <Route path="/admin/users" element={<UsersList />} />
                  </Route>

                  {/* Teacher Routes */}
                  <Route element={<RoleBasedRoute allowedRoles={[UserRole.ADMIN, UserRole.TEACHER]} />}>
                    <Route path="/tests" element={<TestsList />} />
                    <Route path="/tests/:id" element={<TestDetails />} />
                    <Route path="/tests/create" element={<CreateTest />} />
                    <Route path="/tests/edit/:id" element={<EditTest />} />
                    <Route path="/categories" element={<CategoriesList />} />
                    <Route path="/categories/create" element={<CategoriesCreate />} />
                    <Route path="/categories/:id" element={<CategoryDetail />} />
                    <Route path="/categories/:id/edit" element={<CategoryEdit />} />
                    <Route path="/teacher/groups" element={<Groups />} />
                    <Route path="/teacher/groups/new" element={<GroupForm />} />
                    <Route path="/teacher/groups/:id" element={<GroupDetail />} />
                    <Route path="/teacher/groups/:id/edit" element={<GroupForm />} />
                    <Route path="/teacher/groups/:id/results" element={<GroupResults />} />
                    <Route path="/teacher/groups/:groupId/tests/:testId/results" element={<TestQuestionReport />} />
                    <Route path="/teacher/students/:studentId" element={<StudentProfile />} />
                  </Route>

                  {/* Student Routes */}
                  <Route element={<RoleBasedRoute allowedRoles={[UserRole.STUDENT]} />}>
                    <Route path="/student/tests" element={<StudentTestList />} />
                    <Route path="/student/tests/:testId" element={<TestTaking />} />
                    <Route path="/student/tests/:testId/result" element={<TestResultView />} />
                    <Route path="/student/tests/:testId/result/:resultId" element={<TestResultView />} />
                    <Route path="/student/tests/:testId/results" element={<TestResultsList />} />
                    <Route path="/student/results" element={<TestResultsList />} />
                  </Route>

                  {/* Dashboard (accessible to all authenticated users) */}
                  <Route path="/" element={<Dashboard />} />
                </Route>
              </Route>
            </Routes>
            </Router>
          </TestSessionProvider>
        </ThemeProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
