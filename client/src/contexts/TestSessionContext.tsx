import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Test } from '../types/test';
import { TestProgress, TestSession, TestStatus, StudentAnswer } from '../types/student-test';
import { studentTestApi } from '../api/studentTestApi';

interface TestSessionContextType {
  session: TestSession | null;
  isLoading: boolean;
  error: string | null;
  startTest: (testId: string) => Promise<void>;
  saveAnswer: (answer: StudentAnswer) => Promise<void>;
  toggleFlag: (questionNumber: number) => Promise<void>;
  submitTest: () => Promise<void>;
  pauseTest: () => Promise<void>;
  resumeTest: () => Promise<void>;
  navigateToQuestion: (questionNumber: number) => void;
}

const TestSessionContext = createContext<TestSessionContextType | undefined>(undefined);

type Action =
  | { type: 'SET_SESSION'; payload: TestSession }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'UPDATE_ANSWER'; payload: StudentAnswer }
  | { type: 'SET_CURRENT_QUESTION'; payload: number }
  | { type: 'TOGGLE_FLAG'; payload: number }
  | { type: 'CLEAR_SESSION' };

interface State {
  session: TestSession | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: State = {
  session: null,
  isLoading: false,
  error: null,
};

function reducer(state: State, action: Action): State {
  switch (action.type) {
    case 'SET_SESSION':
      return { ...state, session: action.payload, error: null };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    case 'UPDATE_ANSWER':
      if (!state.session) return state;
      return {
        ...state,
        session: {
          ...state.session,
          progress: {
            ...state.session.progress,
            answers: [
              ...state.session.progress.answers.filter(
                a => a.questionNumber !== action.payload.questionNumber
              ),
              action.payload,
            ],
          },
        },
      };
    case 'SET_CURRENT_QUESTION':
      if (!state.session) return state;
      return {
        ...state,
        session: {
          ...state.session,
          currentQuestion: action.payload,
        },
      };
    case 'TOGGLE_FLAG':
      if (!state.session) return state;
      const flaggedQuestions = state.session.progress.flaggedQuestions.includes(action.payload)
        ? state.session.progress.flaggedQuestions.filter(q => q !== action.payload)
        : [...state.session.progress.flaggedQuestions, action.payload];
      return {
        ...state,
        session: {
          ...state.session,
          progress: {
            ...state.session.progress,
            flaggedQuestions,
          },
        },
      };
    case 'CLEAR_SESSION':
      return { ...state, session: null, error: null };
    default:
      return state;
  }
}

export function TestSessionProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(reducer, initialState);

  // Auto-save every 30 seconds
  useEffect(() => {
    if (!state.session || state.session.progress.status !== TestStatus.IN_PROGRESS) return;

    const saveInterval = setInterval(async () => {
      try {
        if (!state.session) return;
        
        await studentTestApi.saveProgress(
          state.session.test.id,
          { 
            answers: state.session.progress.answers,
            flaggedQuestions: state.session.progress.flaggedQuestions
          }
        );
      } catch (error) {
        console.error('Auto-save failed:', error);
      }
    }, 30000);

    return () => clearInterval(saveInterval);
  }, [state.session]);

  const startTest = async (testId: string) => {
    try {
      console.log(`Starting test with ID: ${testId}`);
      dispatch({ type: 'SET_LOADING', payload: true });
      const { test, progress } = await studentTestApi.getTestWithProgress(testId);
      
      console.log('API response:', { test, progress });
      console.log('Test questions:', test.questions);
      console.log('Question types:', test.questions.map(q => ({ type: q.type, typeString: String(q.type) })));
      
      if (progress && progress.status === TestStatus.IN_PROGRESS) {
        // Resume existing session
        dispatch({
          type: 'SET_SESSION',
          payload: {
            test,
            progress,
            currentQuestion: 1,
          },
        });
      } else {
        // Start new session
        const newProgress = await studentTestApi.startTest(testId);
        dispatch({
          type: 'SET_SESSION',
          payload: {
            test,
            progress: newProgress,
            currentQuestion: 1,
          },
        });
      }
    } catch (error) {
      console.error('Failed to start test:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to start test' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const saveAnswer = async (answer: StudentAnswer) => {
    if (!state.session) return;
    
    try {
      // First update the local state
      dispatch({ type: 'UPDATE_ANSWER', payload: answer });
      
      // Create updated answers array
      const existingAnswers = state.session.progress.answers || [];
      const updatedAnswers = [
        ...existingAnswers.filter(a => a.questionNumber !== answer.questionNumber),
        answer
      ];
      
      console.log('Saving answer to server:', answer);
      console.log('Complete answers array being sent:', updatedAnswers);
      
      // Make the API call with the COMPLETE list of answers
      await studentTestApi.saveProgress(state.session.test.id, {
        answers: updatedAnswers
      });
      
      console.log('Answer saved successfully');
    } catch (error) {
      console.error('Failed to save answer:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to save answer' });
    }
  };

  const toggleFlag = async (questionNumber: number) => {
    if (!state.session) return;
    
    try {
      dispatch({ type: 'TOGGLE_FLAG', payload: questionNumber });
      
      // Instead of using a separate endpoint, update using the progress endpoint
      const flaggedQuestions = state.session.progress.flaggedQuestions.includes(questionNumber)
        ? state.session.progress.flaggedQuestions.filter(q => q !== questionNumber)
        : [...state.session.progress.flaggedQuestions, questionNumber];
      
      await studentTestApi.saveProgress(state.session.test.id, { 
        flaggedQuestions 
      });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to toggle flag' });
    }
  };

  const submitTest = async () => {
    if (!state.session) return;
    
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      await studentTestApi.submitTest(state.session.test.id);
      dispatch({ type: 'CLEAR_SESSION' });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to submit test' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const pauseTest = async () => {
    if (!state.session) return;
    
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      await studentTestApi.pauseTest(state.session.test.id);
      dispatch({ type: 'CLEAR_SESSION' });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to pause test' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const resumeTest = async () => {
    if (!state.session) return;
    
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const progress = await studentTestApi.resumeTest(state.session.test.id);
      dispatch({
        type: 'SET_SESSION',
        payload: {
          ...state.session,
          progress,
        },
      });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to resume test' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const navigateToQuestion = (questionNumber: number) => {
    dispatch({ type: 'SET_CURRENT_QUESTION', payload: questionNumber });
  };

  return (
    <TestSessionContext.Provider
      value={{
        ...state,
        startTest,
        saveAnswer,
        toggleFlag,
        submitTest,
        pauseTest,
        resumeTest,
        navigateToQuestion,
      }}
    >
      {children}
    </TestSessionContext.Provider>
  );
}

export function useTestSession() {
  const context = useContext(TestSessionContext);
  if (context === undefined) {
    throw new Error('useTestSession must be used within a TestSessionProvider');
  }
  return context;
} 