import React from "react";
import { useTranslation } from "react-i18next";
import {
  FaChartBar,
  FaClipboardList,
  FaHome,
  FaLayerGroup,
  FaSignOutAlt,
  FaTachometerAlt,
  FaUserGraduate,
  FaUsers
} from "react-icons/fa";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "../../hooks/useAuth";
import { UserRole } from "../../types/user";

interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, toggleSidebar }) => {
  const { user, logout } = useAuth();
  const { t } = useTranslation("common");
  const location = useLocation();

  const isActive = (path: string) => {
    return (
      location.pathname === path || location.pathname.startsWith(`${path}/`)
    );
  };

  const navItems = [
    {
      path: "/",
      label: t("navigation.home"),
      icon: <FaHome className="w-5 h-5" />,
      roles: ["ALL"],
      comingSoon: false,
    },
    {
      path: "/tests",
      label: t("navigation.tests"),
      icon: <FaClipboardList className="w-5 h-5" />,
      roles: ["ALL"],
      comingSoon: false,
    },
    {
      path: "/student/tests",
      label: t("navigation.myTests"),
      icon: <FaUserGraduate className="w-5 h-5" />,
      roles: [UserRole.STUDENT],
      comingSoon: false,
    },
    {
      path: "/categories",
      label: t("navigation.categories"),
      icon: <FaLayerGroup className="w-5 h-5" />,
      roles: [UserRole.ADMIN, UserRole.TEACHER],
      comingSoon: false,
    },
    {
      path: "/teacher/groups",
      label: t("navigation.groups"),
      icon: <FaUsers className="w-5 h-5" />,
      roles: [UserRole.ADMIN, UserRole.TEACHER],
      comingSoon: false,
    },
    {
      path: "/dashboard",
      label: t("navigation.dashboard"),
      icon: <FaTachometerAlt className="w-5 h-5" />,
      roles: [UserRole.ADMIN, UserRole.TEACHER],
      comingSoon: true,
    },
    {
      path: "/student/results",
      label: t("navigation.results"),
      icon: <FaChartBar className="w-5 h-5" />,
      roles: [UserRole.STUDENT],
      comingSoon: false,
    },
  ];

  const filteredNavItems = navItems.filter((item) => {
    if (item.roles.includes("ALL")) return true;
    if (!user && !item.roles.includes("ALL")) return false;
    if (item.roles.includes("AUTHENTICATED") && user) return true;
    return user && item.roles.includes(user.role);
  });

  return (
    <>
      <div
        className={`fixed top-0 left-0 h-screen overflow-y-auto bg-slate-50 text-slate-700 w-64 z-30 transform transition-transform duration-300 ease-in-out ${
          isOpen ? "translate-x-0" : "-translate-x-full"
        } lg:translate-x-0 lg:static lg:z-auto lg:h-auto lg:self-stretch lg:w-64 border-r border-slate-200 dark:bg-slate-800 dark:text-slate-300 dark:border-slate-700`}
        style={{ top: "64px" }}
      >
        {/* Header section */}
        <div className="p-4 border-b border-slate-200 bg-white dark:bg-slate-800 dark:border-slate-700">
          <div className="flex items-center justify-between lg:hidden">
            <Link to="/" className="text-xl font-bold text-slate-800 dark:text-white">
              {t("app.title")}
            </Link>
            <button
              className="text-slate-500 hover:text-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-300 rounded dark:text-slate-400 dark:hover:text-white dark:focus:ring-slate-600"
              onClick={toggleSidebar}
              aria-label="Close sidebar"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          {user && (
            <div className="mt-2 text-sm text-slate-600 font-medium dark:text-slate-400">
              {t("messages.welcome", { name: user ? `${user.firstName} ${user.lastName}` : "" })}
            </div>
          )}
        </div>

        {/* Navigation items */}
        <nav className="mt-4">
          <ul className="space-y-1">
            {filteredNavItems.map((item) => (
              <li key={item.path}>
                <Link
                  to={item.comingSoon ? "#" : item.path}
                  className={`flex items-center px-4 py-3 transition-colors rounded-lg mx-2 ${
                    isActive(item.path)
                      ? "bg-slate-200 text-slate-900 font-medium dark:bg-slate-700 dark:text-white"
                      : "text-slate-600 hover:bg-slate-100 hover:text-slate-900 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white"
                  } ${item.comingSoon ? "cursor-default opacity-70" : ""}`}
                  onClick={(e) => {
                    if (item.comingSoon) {
                      e.preventDefault();
                    } else if (window.innerWidth < 1024) {
                      toggleSidebar();
                    }
                  }}
                >
                  <span className="mr-3 text-slate-500 dark:text-slate-400">{item.icon}</span>
                  <span className="flex-1">{item.label}</span>
                  {item.comingSoon && (
                    <span className="ml-2 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full whitespace-nowrap">
                      {t("status.comingSoon")}
                    </span>
                  )}
                </Link>
              </li>
            ))}
            {user && (
              <li>
                <button
                  onClick={() => {
                    logout();
                    if (window.innerWidth < 1024) {
                      toggleSidebar();
                    }
                  }}
                  className="w-full flex items-center px-4 py-3 text-left text-slate-600 hover:bg-slate-100 hover:text-slate-900 transition-colors rounded-lg mx-2 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white"
                >
                  <span className="mr-3 text-slate-500">
                    <FaSignOutAlt className="w-5 h-5" />
                  </span>
                  <span>{t("navigation.logout")}</span>
                </button>
              </li>
            )}
          </ul>
        </nav>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-slate-900/20 backdrop-blur-sm z-20 lg:hidden dark:bg-black/50"
          onClick={toggleSidebar}
        />
      )}
    </>
  );
};

export default Sidebar;
