import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useAuth } from "../../hooks/useAuth";
import LanguageSwitcher from "../LanguageSwitcher";
import ThemeSwitcher from "../ThemeSwitcher";

interface NavbarProps {
  toggleSidebar: () => void;
}

const Navbar: React.FC<NavbarProps> = ({ toggleSidebar }) => {
  const { user } = useAuth();
  const { t } = useTranslation("common");

  return (
    <nav className="bg-white border-b border-slate-200 z-10 sticky top-0 shadow-sm dark:bg-slate-800 dark:border-slate-700">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-4 w-full">
          <div className="flex items-center">
            <button
              className="text-slate-500 hover:text-slate-700 mr-4 focus:outline-none focus:ring-2 focus:ring-slate-300 rounded-lg p-2 transition-colors duration-200 lg:hidden dark:text-slate-300 dark:hover:text-white dark:focus:ring-slate-600"
              onClick={toggleSidebar}
              aria-label="Toggle sidebar"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
            <Link to="/" className="text-slate-800 text-2xl font-bold tracking-tight hover:text-slate-600 transition-colors duration-200 dark:text-white dark:hover:text-slate-300">
              {t("app.title")}
            </Link>
          </div>
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-3">
              <LanguageSwitcher />
              <ThemeSwitcher />
            </div>

            {!user && (
              <div className="flex space-x-3">
                <Link
                  to="/login"
                  className="text-slate-600 hover:text-slate-900 font-medium px-3 py-2 rounded-lg transition-colors duration-200 hover:bg-slate-100 dark:text-slate-300 dark:hover:text-white dark:hover:bg-slate-700"
                >
                  {t("navigation.login")}
                </Link>
                <Link
                  to="/register"
                  className="bg-slate-800 text-white hover:bg-slate-700 px-5 py-2 rounded-lg font-medium transition-colors duration-200 shadow hover:shadow-md dark:bg-blue-600 dark:hover:bg-blue-700"
                >
                  {t("navigation.register")}
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
