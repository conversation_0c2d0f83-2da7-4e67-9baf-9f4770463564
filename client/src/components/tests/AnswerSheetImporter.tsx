import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface AnswerSheetImporterProps {
  type: 'multipleChoice' | 'fillInBlank';
  onImport: (text: string) => void;
  placeholder: string;
}

const AnswerSheetImporter: React.FC<AnswerSheetImporterProps> = ({
  type,
  onImport,
  placeholder
}) => {
  const { t } = useTranslation(['tests', 'common']);
  const [text, setText] = useState('');

  const handleParse = () => {
    if (text.trim()) {
      onImport(text);
      setText(''); // Clear the input after parsing
    }
  };

  return (
    <div className="bg-white dark:bg-slate-800 p-4 rounded-md border border-gray-200 dark:border-slate-700 mb-4">
      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        {type === 'multipleChoice'
          ? t('tests:import.multipleChoiceTitle')
          : t('tests:import.fillInBlankTitle')}
      </h4>

      <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
        {type === 'multipleChoice'
          ? t('tests:import.multipleChoiceFormat')
          : t('tests:import.fillInBlankFormat')}
      </div>

      <textarea
        value={text}
        onChange={(e) => setText(e.target.value)}
        placeholder={placeholder}
        className="w-full h-24 p-2 border border-gray-300 dark:border-slate-600 rounded-md dark:bg-slate-700 dark:text-white mb-2"
      />

      <div className="flex justify-end">
        <button
          type="button"
          onClick={handleParse}
          disabled={!text.trim()}
          className="bg-blue-500 text-white py-1 px-3 rounded text-sm hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {t('tests:import.parseButton')}
        </button>
      </div>
    </div>
  );
};

export default AnswerSheetImporter;
