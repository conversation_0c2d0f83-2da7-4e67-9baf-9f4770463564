# UI Components

This directory contains reusable UI components that can be used throughout the application.

## Components

### ConfirmModal

A reusable confirmation modal that can be used for various confirmation actions such as deletion, removal, or any action that requires user confirmation.

#### Usage

```tsx
import { useState } from 'react';
import ConfirmModal from 'components/ui/ConfirmModal';

const MyComponent = () => {
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  
  const handleAction = () => {
    setIsConfirmOpen(true);
  };
  
  const confirmAction = () => {
    // Perform the confirmed action
    console.log('Action confirmed');
    setIsConfirmOpen(false);
  };
  
  return (
    <div>
      <button onClick={handleAction}>Delete Item</button>
      
      <ConfirmModal
        isOpen={isConfirmOpen}
        title="Confirm Deletion"
        message="Are you sure you want to delete this item? This action cannot be undone."
        confirmLabel="Delete"
        cancelLabel="Cancel"
        onConfirm={confirmAction}
        onCancel={() => setIsConfirmOpen(false)}
        type="danger" // Options: 'danger', 'warning', 'info'
      />
    </div>
  );
};
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| isOpen | boolean | required | Controls whether the modal is visible |
| title | string | required | The title displayed at the top of the modal |
| message | string | required | The main message or question in the modal body |
| confirmLabel | string | 'Confirm' | Text for the confirm button |
| cancelLabel | string | 'Cancel' | Text for the cancel button |
| onConfirm | function | required | Function called when confirm button is clicked |
| onCancel | function | required | Function called when cancel button or backdrop is clicked |
| type | 'danger' \| 'warning' \| 'info' | 'danger' | Changes the color and style of the confirm button |
