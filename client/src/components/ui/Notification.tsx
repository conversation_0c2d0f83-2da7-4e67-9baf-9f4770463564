import React from 'react';
import { toast, ToastContainer, ToastOptions, Id } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

type NotificationType = 'success' | 'error' | 'info' | 'warning';

interface ActionButton {
  label: string;
  onClick: () => void;
  style: 'primary' | 'secondary' | 'danger';
}

export const NotificationContainer = () => {
  return (
    <ToastContainer
      position="top-right"
      autoClose={5000}
      hideProgressBar={false}
      newestOnTop
      closeOnClick
      rtl={false}
      pauseOnFocusLoss
      draggable
      pauseOnHover
      theme="light"
    />
  );
};

/**
 * Display a toast notification
 * @param message The message to display
 * @param type The type of notification (success, error, info, warning)
 * @param actionButtons Optional action buttons to display in the notification
 * @param duration Time in ms before the notification auto-closes
 * @returns The notification ID that can be used to close it programmatically
 */
export const showNotification = (
  message: string,
  type: NotificationType = 'info',
  actionButtons?: ActionButton[],
  duration: number = 5000
): Id => {
  const options: ToastOptions = {
    autoClose: duration,
    closeOnClick: !actionButtons, // Don't close on click if we have action buttons
    draggable: true,
  };

  // If we have action buttons, render them
  if (actionButtons && actionButtons.length > 0) {
    return toast(
      <div>
        <div className="mb-2">{message}</div>
        <div className="flex space-x-2">
          {actionButtons.map((button, index) => {
            const buttonStyle = {
              primary: 'bg-blue-600 hover:bg-blue-700 text-white',
              secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800',
              danger: 'bg-red-600 hover:bg-red-700 text-white',
            }[button.style];
            
            return (
              <button
                key={index}
                onClick={() => {
                  button.onClick();
                  toast.dismiss(toast.id);
                }}
                className={`px-3 py-1 rounded text-sm font-medium ${buttonStyle}`}
              >
                {button.label}
              </button>
            );
          })}
        </div>
      </div>,
      {
        ...options,
        type,
      }
    );
  }

  // Standard notification without action buttons
  switch (type) {
    case 'success':
      return toast.success(message, options);
    case 'error':
      return toast.error(message, options);
    case 'warning':
      return toast.warning(message, options);
    case 'info':
    default:
      return toast.info(message, options);
  }
};

export const closeNotification = (id: Id): void => {
  toast.dismiss(id);
};
