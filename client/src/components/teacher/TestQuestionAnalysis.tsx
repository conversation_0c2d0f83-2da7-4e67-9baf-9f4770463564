import React from 'react';
import { useTranslation } from 'react-i18next';
import { QuestionAnalysis, QuestionType } from '../../types/teacher-group';

interface TestQuestionAnalysisProps {
  question: QuestionAnalysis;
  totalStudents: number;
}

const TestQuestionAnalysis: React.FC<TestQuestionAnalysisProps> = ({ question, totalStudents }) => {
  const { t } = useTranslation();

  // Calculate percentages
  const correctPercentage = totalStudents > 0 ? Math.round((question.correctCount / totalStudents) * 100) : 0;
  const incorrectPercentage = totalStudents > 0 ? Math.round((question.incorrectCount / totalStudents) * 100) : 0;
  const skippedPercentage = totalStudents > 0 ? Math.round((question.skippedCount / totalStudents) * 100) : 0;

  // Format the correct answer for display
  const formatCorrectAnswer = () => {
    if (question.questionType === QuestionType.MULTIPLE_CHOICE) {
      return `${t('teacher:questionAnalysis.option')} ${question.correctAnswer}`;
    } else if (Array.isArray(question.correctAnswer)) {
      return question.correctAnswer.join(', ');
    } else {
      return question.correctAnswer;
    }
  };

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4 mb-4">
      <h3 className="text-lg font-semibold mb-2 dark:text-white">
        {question.questionText.startsWith('Question')
          ? t('teacher:questionAnalysis.questionPrefix', { number: question.questionNumber })
          : question.questionText}
      </h3>

      <div className="mb-4">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {t('teacher:questionAnalysis.correctAnswer')}:
          <span className="ml-2 text-green-600 dark:text-green-400 font-semibold">
            {formatCorrectAnswer()}
          </span>
        </span>
      </div>

      {/* Results summary */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {correctPercentage}{t('teacher:questionAnalysis.percentSymbol')}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {t('teacher:questionAnalysis.correct')}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-500">
            ({question.correctCount}/{totalStudents})
          </div>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400">
            {incorrectPercentage}{t('teacher:questionAnalysis.percentSymbol')}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {t('teacher:questionAnalysis.incorrect')}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-500">
            ({question.incorrectCount}/{totalStudents})
          </div>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-gray-600 dark:text-gray-400">
            {skippedPercentage}{t('teacher:questionAnalysis.percentSymbol')}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {t('teacher:questionAnalysis.skipped')}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-500">
            ({question.skippedCount}/{totalStudents})
          </div>
        </div>
      </div>

      {/* Progress bar */}
      <div className="w-full h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mb-4">
        <div className="flex h-full">
          <div
            className="bg-green-500 dark:bg-green-600"
            style={{ width: `${correctPercentage}%` }}
          ></div>
          <div
            className="bg-red-500 dark:bg-red-600"
            style={{ width: `${incorrectPercentage}%` }}
          ></div>
          <div
            className="bg-gray-400 dark:bg-gray-500"
            style={{ width: `${skippedPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Incorrect answers breakdown */}
      {question.incorrectAnswers.length > 0 && (
        <div>
          <h4 className="text-md font-medium mb-2 dark:text-white">
            {t('teacher:questionAnalysis.incorrectAnswers')}
          </h4>
          <div className="space-y-2">
            {question.incorrectAnswers.map((incorrectAnswer, index) => (
              <div key={index} className="flex items-center">
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mr-2">
                  <div
                    className="bg-red-500 dark:bg-red-600 h-2.5 rounded-full"
                    style={{ width: `${Math.round((incorrectAnswer.count / question.incorrectCount) * 100)}%` }}
                  ></div>
                </div>
                <div className="flex justify-between w-full">
                  <span className="text-sm text-gray-700 dark:text-gray-300 truncate max-w-xs">
                    {incorrectAnswer.answer || t('teacher:questionAnalysis.emptyAnswer')}
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
                    {incorrectAnswer.count} ({Math.round((incorrectAnswer.count / question.incorrectCount) * 100)}{t('teacher:questionAnalysis.percentSymbol')})
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default TestQuestionAnalysis;
