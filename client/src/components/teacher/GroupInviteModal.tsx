import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CreateGroupInviteDto } from '../../api/teacherGroupApi';

interface GroupInviteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateGroupInviteDto) => void;
  isLoading?: boolean;
}

const GroupInviteModal: React.FC<GroupInviteModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const [expiresInDays, setExpiresInDays] = useState<number>(7);
  const [maxUses, setMaxUses] = useState<number | undefined>(undefined);
  const [hasMaxUses, setHasMaxUses] = useState<boolean>(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const data: CreateGroupInviteDto = {
      expiresInDays,
      maxUses: hasMaxUses ? maxUses : undefined,
    };
    onSubmit(data);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-black bg-opacity-70 backdrop-blur-sm transition-opacity" onClick={onClose}></div>
        
        <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl transform transition-all w-full max-w-md z-20">
          <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('teacher:groups.generateInvitationLink')}
            </h3>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <form onSubmit={handleSubmit}>
            <div className="p-6 space-y-5">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                  {t('teacher:groups.expirationDays')}
                </label>
                <select
                  value={expiresInDays}
                  onChange={(e) => setExpiresInDays(parseInt(e.target.value))}
                  className="block w-full py-2 px-3 rounded-md border-gray-300 dark:border-gray-600 dark:bg-slate-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value={1}>{t('teacher:groups.oneDay')}</option>
                  <option value={3}>{t('teacher:groups.threeDays')}</option>
                  <option value={7}>{t('teacher:groups.oneWeek')}</option>
                  <option value={14}>{t('teacher:groups.twoWeeks')}</option>
                  <option value={30}>{t('teacher:groups.oneMonth')}</option>
                </select>
              </div>
              
              <div className="pt-2">
                <div className="flex items-center">
                  <input
                    id="has-max-uses"
                    type="checkbox"
                    checked={hasMaxUses}
                    onChange={(e) => setHasMaxUses(e.target.checked)}
                    className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="has-max-uses" className="ml-2.5 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('teacher:groups.limitNumberOfUses')}
                  </label>
                </div>
                
                {hasMaxUses && (
                  <div className="mt-3">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                      {t('teacher:groups.maxUses')}
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="100"
                      value={maxUses || ''}
                      onChange={(e) => setMaxUses(e.target.value ? parseInt(e.target.value) : undefined)}
                      className="block w-full py-2 px-3 rounded-md border-gray-300 dark:border-gray-600 dark:bg-slate-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      placeholder={t('teacher:groups.enterMaxUses')}
                    />
                  </div>
                )}
              </div>
            </div>
            
            <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-transparent hover:bg-gray-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {t('common:buttons.cancel')}
              </button>
              <button
                type="submit"
                className="px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    {t('common:messages.loading')}
                  </>
                ) : (
                  t('teacher:groups.generateLink')
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default GroupInviteModal;
