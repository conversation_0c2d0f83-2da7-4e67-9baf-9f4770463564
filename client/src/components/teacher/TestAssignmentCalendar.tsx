import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { GroupAssignment } from '../../api/teacherGroupApi';

interface TestType {
  type: string;
  color: string;
}

const TEST_TYPES: Record<string, TestType> = {
  READING: { type: 'READING', color: 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800' },
  LISTENING: { type: 'LISTENING', color: 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800' },
  WRITING: { type: 'WRITING', color: 'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-800' },
  SPEAKING: { type: 'SPEAKING', color: 'bg-purple-100 text-purple-800 border-purple-300 dark:bg-purple-900/30 dark:text-purple-400 dark:border-purple-800' },
  FULL: { type: 'FULL', color: 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400 dark:border-red-800' },
  DEFAULT: { type: 'DEFAULT', color: 'bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700' }
};

interface CalendarDayEvent {
  id: string;
  title: string;
  type: string;
  dueDate: Date;
}

interface TestAssignmentCalendarProps {
  assignments: GroupAssignment[];
  onAssignmentClick: (assignmentId: string) => void;
  onDateClick: (date: Date) => void;
  locale?: string;
  translations?: {
    today?: string;
    viewByMonth?: string;
    noTests?: string;
  };
}

const TestAssignmentCalendar: React.FC<TestAssignmentCalendarProps> = ({
  assignments,
  onAssignmentClick,
  onDateClick,
  locale,
  translations = {}
}) => {
  const { t } = useTranslation();
  const today = new Date();
  const [currentMonth, setCurrentMonth] = useState(today.getMonth());
  const [currentYear, setCurrentYear] = useState(today.getFullYear());

  // Navigate to previous month
  const prevMonth = () => {
    if (currentMonth === 0) {
      setCurrentMonth(11);
      setCurrentYear(currentYear - 1);
    } else {
      setCurrentMonth(currentMonth - 1);
    }
  };

  // Navigate to next month
  const nextMonth = () => {
    if (currentMonth === 11) {
      setCurrentMonth(0);
      setCurrentYear(currentYear + 1);
    } else {
      setCurrentMonth(currentMonth + 1);
    }
  };

  // Get days in month
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Get first day of month (0 = Sunday, 1 = Monday, etc.)
  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  // Format assignments by date
  const formatAssignments = () => {
    const formattedAssignments: Record<string, CalendarDayEvent[]> = {};

    assignments.forEach(assignment => {
      if (assignment.dueDate) {
        const dueDate = new Date(assignment.dueDate);
        const dateKey = dueDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD

        if (!formattedAssignments[dateKey]) {
          formattedAssignments[dateKey] = [];
        }

        formattedAssignments[dateKey].push({
          id: assignment.id,
          title: assignment.test?.title || 'Unnamed Test',
          type: assignment.test?.type || 'DEFAULT',
          dueDate: dueDate
        });
      }
    });

    return formattedAssignments;
  };

  // Render calendar
  const renderCalendar = () => {
    const daysInMonth = getDaysInMonth(currentYear, currentMonth);
    const firstDayOfMonth = getFirstDayOfMonth(currentYear, currentMonth);
    const formattedAssignments = formatAssignments();
    const days = [];

    // Render empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(
        <div key={`empty-${i}`} className="border border-gray-200 dark:border-gray-700 h-24 bg-gray-50 dark:bg-slate-700"></div>
      );
    }

    // Render days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentYear, currentMonth, day);
      const dateKey = date.toISOString().split('T')[0];
      const eventsForDay = formattedAssignments[dateKey] || [];
      const isToday = today.getDate() === day &&
                       today.getMonth() === currentMonth &&
                       today.getFullYear() === currentYear;

      days.push(
        <div
          key={`day-${day}`}
          className={`border border-gray-200 dark:border-gray-700 h-24 p-2 overflow-hidden ${isToday ? 'bg-blue-50 dark:bg-blue-900/20' : 'dark:bg-slate-800'}`}
          onClick={() => onDateClick(date)}
        >
          <div className="flex justify-between mb-1">
            <span className={`text-sm font-medium ${isToday ? 'text-blue-600 dark:text-blue-400' : 'dark:text-white'}`}>{day}</span>
            {eventsForDay.length > 0 && (
              <span className="text-xs text-gray-500 dark:text-gray-400">{eventsForDay.length} {t('teacher:tests.tests', { defaultValue: 'tests' })}</span>
            )}
          </div>
          <div className="space-y-1 overflow-y-auto max-h-16">
            {eventsForDay.map((event) => (
              <div
                key={event.id}
                className={`text-xs px-2 py-1 rounded border truncate cursor-pointer ${TEST_TYPES[event.type]?.color || TEST_TYPES.DEFAULT.color}`}
                onClick={(e) => {
                  e.stopPropagation();
                  onAssignmentClick(event.id);
                }}
              >
                {event.title}
              </div>
            ))}
          </div>
        </div>
      );
    }

    return days;
  };

  const monthNames = [
    t('common:months.january'),
    t('common:months.february'),
    t('common:months.march'),
    t('common:months.april'),
    t('common:months.may'),
    t('common:months.june'),
    t('common:months.july'),
    t('common:months.august'),
    t('common:months.september'),
    t('common:months.october'),
    t('common:months.november'),
    t('common:months.december')
  ];

  const weekdayNames = [
    t('common:days.sunday'),
    t('common:days.monday'),
    t('common:days.tuesday'),
    t('common:days.wednesday'),
    t('common:days.thursday'),
    t('common:days.friday'),
    t('common:days.saturday')
  ];

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">
          {monthNames[currentMonth]} {currentYear}
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={prevMonth}
            className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors"
          >
            <svg className="h-5 w-5 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            onClick={() => {
              setCurrentMonth(today.getMonth());
              setCurrentYear(today.getFullYear());
            }}
            className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors"
          >
            {translations.today || t('teacher:tests.today', { defaultValue: 'Today' })}
          </button>
          <button
            onClick={nextMonth}
            className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors"
          >
            <svg className="h-5 w-5 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Calendar Legend */}
      <div className="flex flex-wrap space-x-2 mb-4">
        {Object.values(TEST_TYPES).map((type) => (
          <div key={type.type} className="flex items-center text-xs text-gray-700 dark:text-gray-300 mb-1 mr-2">
            <span className={`inline-block w-3 h-3 mr-1 rounded-sm ${type.color.split(' ')[0]}`}></span>
            <span>{type.type.charAt(0) + type.type.slice(1).toLowerCase()}</span>
          </div>
        ))}
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-px">
        {/* Days of the week */}
        {weekdayNames.map((day, index) => (
          <div key={index} className="text-center text-xs font-medium text-gray-500 dark:text-gray-400 py-2">
            {day.substring(0, 3)}
          </div>
        ))}

        {/* Calendar days */}
        {renderCalendar()}
      </div>
    </div>
  );
};

export default TestAssignmentCalendar;
