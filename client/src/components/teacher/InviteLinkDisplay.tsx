import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { GroupInvite } from '../../api/teacherGroupApi';
import { showNotification } from '../ui/Notification';

interface InviteLinkDisplayProps {
  isOpen: boolean;
  inviteLink: GroupInvite;
  onClose: () => void;
}

const InviteLinkDisplay: React.FC<InviteLinkDisplayProps> = ({ isOpen, inviteLink, onClose }) => {
  const { t } = useTranslation();
  const [copied, setCopied] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Format dates for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('default', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };
  
  // Return null if modal is not open
  if (!isOpen) return null;

  const handleCopyLink = () => {
    if (inputRef.current) {
      inputRef.current.select();
      navigator.clipboard.writeText(inputRef.current.value)
        .then(() => {
          setCopied(true);
          showNotification(t('teacher:groups.linkCopied'), 'success');
          setTimeout(() => setCopied(false), 3000);
        })
        .catch(err => {
          console.error('Failed to copy text: ', err);
          showNotification(t('common:errors.somethingWentWrong'), 'error');
        });
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-black bg-opacity-70 backdrop-blur-sm transition-opacity" onClick={onClose}></div>
        
        <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl transform transition-all w-full max-w-md z-20">
          <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('teacher:groups.invitationLink')}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="p-6 space-y-5">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('teacher:groups.shareThisLink')}
              </label>
              <div className="flex rounded-md overflow-hidden shadow-sm">
                <input
                  ref={inputRef}
                  type="text"
                  readOnly
                  value={inviteLink.inviteUrl}
                  className="flex-1 truncate px-3 py-2 border border-gray-300 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <button
                  onClick={handleCopyLink}
                  className="flex items-center justify-center min-w-[120px] flex-shrink-0 px-4 py-2 border border-transparent shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"
                >
                  {copied ? (
                    <span className="flex items-center">
                      <svg className="w-5 h-5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {t('common:buttons.copied')}
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <svg className="w-5 h-5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                      {t('common:buttons.copy')}
                    </span>
                  )}
                </button>
              </div>
            </div>
            
            <div className="mt-6 p-5 bg-gray-50 dark:bg-slate-700/50 rounded-lg border border-gray-100 dark:border-slate-600">
              <h4 className="text-base font-medium text-gray-800 dark:text-gray-200 mb-3">
                {t('teacher:groups.inviteDetails')}
              </h4>
              <div className="grid grid-cols-2 gap-3">
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('teacher:groups.created')}
                </dt>
                <dd className="text-sm text-gray-900 dark:text-white font-medium">
                  {formatDate(inviteLink.createdAt)}
                </dd>      
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('teacher:groups.expires')}
                </dt>
                <dd className="text-sm text-gray-900 dark:text-white font-medium">
                  {formatDate(inviteLink.expiresAt)}
                </dd>      
                {inviteLink.maxUses && (
                  <>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {t('teacher:groups.maxUses')}
                    </dt>
                    <dd className="text-sm text-gray-900 dark:text-white font-medium">
                      {inviteLink.maxUses}
                    </dd>
                    
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {t('teacher:groups.currentUses')}
                    </dt>
                    <dd className="text-sm text-gray-900 dark:text-white font-medium">
                      {inviteLink.useCount || 0} {inviteLink.maxUses ? `/ ${inviteLink.maxUses}` : ''}
                    </dd>
                  </>
                )}
              </div>
            </div>
          </div>
          
          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="px-5 py-2.5 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 hover:bg-gray-200 dark:bg-slate-700 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              {t('common:buttons.close')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InviteLinkDisplay;
