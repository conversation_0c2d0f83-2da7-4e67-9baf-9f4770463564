import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AssignTestDto } from '../../api/teacherGroupApi';

interface Test {
  id: string;
  title: string;
  type: string;
  difficulty: string;
}

interface BatchAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (assignments: AssignTestDto[]) => void;
  availableTests: Test[];
}

const BatchAssignmentModal: React.FC<BatchAssignmentModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  availableTests,
}) => {
  const { t } = useTranslation();
  const [selectedTestIds, setSelectedTestIds] = useState<string[]>([]);
  const [dueDate, setDueDate] = useState('');
  const [useSameDate, setUseSameDate] = useState(true);
  const [individualDueDates, setIndividualDueDates] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Filter available tests by type for easier selection
  const testsByType: Record<string, Test[]> = availableTests.reduce((acc, test) => {
    if (!acc[test.type]) {
      acc[test.type] = [];
    }
    acc[test.type].push(test);
    return acc;
  }, {} as Record<string, Test[]>);

  // Handle toggling test selection
  const handleToggleTest = (testId: string) => {
    if (selectedTestIds.includes(testId)) {
      setSelectedTestIds(selectedTestIds.filter(id => id !== testId));
      
      // Remove individual due date if deselected
      const newDates = { ...individualDueDates };
      delete newDates[testId];
      setIndividualDueDates(newDates);
    } else {
      setSelectedTestIds([...selectedTestIds, testId]);
      
      // Initialize individual due date if needed
      if (!useSameDate) {
        setIndividualDueDates({
          ...individualDueDates,
          [testId]: dueDate, // Default to common date if set
        });
      }
    }
  };
  
  // Handle select all tests by type
  const handleSelectAllByType = (type: string) => {
    const testsOfType = testsByType[type];
    const allTypeTestIds = testsOfType.map(test => test.id);
    
    const allSelected = testsOfType.every(test => selectedTestIds.includes(test.id));
    
    if (allSelected) {
      // Deselect all of this type
      setSelectedTestIds(selectedTestIds.filter(id => !allTypeTestIds.includes(id)));
      
      // Remove individual due dates
      const newDates = { ...individualDueDates };
      allTypeTestIds.forEach(id => {
        delete newDates[id];
      });
      setIndividualDueDates(newDates);
    } else {
      // Select all of this type
      const newSelectedIds = [...new Set([...selectedTestIds, ...allTypeTestIds])];
      setSelectedTestIds(newSelectedIds);
      
      // Add individual due dates if needed
      if (!useSameDate) {
        const newDates = { ...individualDueDates };
        allTypeTestIds.forEach(id => {
          if (!newDates[id]) {
            newDates[id] = dueDate;
          }
        });
        setIndividualDueDates(newDates);
      }
    }
  };
  
  // Handle individual due date change
  const handleIndividualDueDateChange = (testId: string, date: string) => {
    setIndividualDueDates({
      ...individualDueDates,
      [testId]: date,
    });
  };
  
  // Handle date mode toggle
  const handleDateModeToggle = (mode: boolean) => {
    setUseSameDate(mode);
    
    if (mode) {
      // If switching to same date, clear individual dates
      setIndividualDueDates({});
    } else {
      // If switching to individual dates, initialize with common date
      const newDates: Record<string, string> = {};
      selectedTestIds.forEach(id => {
        newDates[id] = dueDate;
      });
      setIndividualDueDates(newDates);
    }
  };
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedTestIds.length === 0) {
      return; // Nothing to assign
    }
    
    setIsSubmitting(true);
    
    const assignments: AssignTestDto[] = selectedTestIds.map(testId => ({
      testId,
      dueDate: useSameDate ? dueDate : individualDueDates[testId]
    }));
    
    onSubmit(assignments);
    
    // Reset form
    setSelectedTestIds([]);
    setDueDate('');
    setIndividualDueDates({});
    setIsSubmitting(false);
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">
            {t('teacher:tests.batchAssignment')}
          </h3>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            &times;
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="px-6 py-4 overflow-y-auto max-h-[calc(90vh-8rem)]">
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              {t('teacher:tests.dateOptions')}
            </h4>
            <div className="flex space-x-4 mb-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  checked={useSameDate}
                  onChange={() => handleDateModeToggle(true)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">
                  {t('teacher:tests.sameDateForAll')}
                </span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  checked={!useSameDate}
                  onChange={() => handleDateModeToggle(false)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">
                  {t('teacher:tests.individualDates')}
                </span>
              </label>
            </div>
            
            {useSameDate && (
              <div>
                <label htmlFor="due-date" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('teacher:groups.dueDate')}
                </label>
                <input
                  type="date"
                  id="due-date"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            )}
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              {t('teacher:tests.selectTests')} ({selectedTestIds.length} {t('teacher:tests.selected')})
            </h4>
            
            {Object.entries(testsByType).map(([type, tests]) => (
              <div key={type} className="mb-4 border border-gray-200 rounded-lg">
                <div className="bg-gray-50 px-4 py-2 flex justify-between items-center">
                  <h5 className="font-medium text-gray-700">{type}</h5>
                  <button
                    type="button"
                    onClick={() => handleSelectAllByType(type)}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    {tests.every(test => selectedTestIds.includes(test.id))
                      ? t('teacher:tests.deselectAll')
                      : t('teacher:tests.selectAll')}
                  </button>
                </div>
                
                <div className="divide-y divide-gray-200">
                  {tests.map(test => (
                    <div 
                      key={test.id} 
                      className={`px-4 py-3 ${selectedTestIds.includes(test.id) ? 'bg-blue-50' : ''}`}
                    >
                      <div className="flex items-start">
                        <div className="flex items-center h-5">
                          <input
                            id={`test-${test.id}`}
                            type="checkbox"
                            checked={selectedTestIds.includes(test.id)}
                            onChange={() => handleToggleTest(test.id)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <label htmlFor={`test-${test.id}`} className="font-medium text-gray-700 cursor-pointer">
                            {test.title}
                          </label>
                          <p className="text-gray-500 mt-1">
                            {t('teacher:tests.difficulty')}: {test.difficulty}
                          </p>
                        </div>
                      </div>
                      
                      {/* Individual due date if selected and in individual mode */}
                      {!useSameDate && selectedTestIds.includes(test.id) && (
                        <div className="mt-2 ml-7">
                          <label className="block text-xs font-medium text-gray-500 mb-1">
                            {t('teacher:groups.dueDate')}
                          </label>
                          <input
                            type="date"
                            value={individualDueDates[test.id] || ''}
                            onChange={(e) => handleIndividualDueDateChange(test.id, e.target.value)}
                            min={new Date().toISOString().split('T')[0]}
                            className="w-full sm:w-48 px-2 py-1 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </form>
        
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-3"
          >
            {t('common:buttons.cancel')}
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            disabled={selectedTestIds.length === 0 || isSubmitting}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300 disabled:cursor-not-allowed"
          >
            {isSubmitting ? t('common:buttons.assigning') : t('teacher:tests.assignSelected')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default BatchAssignmentModal;
