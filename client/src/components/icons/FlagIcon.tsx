import React from 'react';

type FlagIconProps = {
  country: 'gb' | 'vn';
  className?: string;
};

const FlagIcon: React.FC<FlagIconProps> = ({ country, className = '' }) => {
  const flags = {
    gb: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 60 30"
        className={`w-5 h-3.5 ${className}`}
      >
        <clipPath id="a">
          <path d="M0 0v30h60V0z" />
        </clipPath>
        <clipPath id="b">
          <path d="M30 15h30v15zv15H0zH0V0zV0h30z" />
        </clipPath>
        <g clipPath="url(#a)">
          <path d="M0 0v30h60V0z" fill="#012169" />
          <path d="M0 0l60 30m0-30L0 30" stroke="#fff" strokeWidth="6" />
          <path
            d="M0 0l60 30m0-30L0 30"
            clipPath="url(#b)"
            stroke="#C8102E"
            strokeWidth="4"
          />
          <path d="M30 0v30M0 15h60" stroke="#fff" strokeWidth="10" />
          <path d="M30 0v30M0 15h60" stroke="#C8102E" strokeWidth="6" />
        </g>
      </svg>
    ),
    vn: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 60 30"
        className={`w-5 h-3.5 ${className}`}
      >
        <rect width="60" height="30" fill="#da251d"/>
        <polygon points="30,6 35,20.5 20,11.5 40,11.5 25,20.5" fill="#ffff00"/>
      </svg>
    ),
  };

  return flags[country];
};

export default FlagIcon;
