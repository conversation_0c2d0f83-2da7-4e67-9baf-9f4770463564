.container {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
  height: 100%;
}

/* Dark mode for container */
:global(.dark) .container, .darkMode {
  border-color: #475569;
  background-color: #1e293b;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 8px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

/* Dark mode for toolbar */
:global(.dark) .toolbar, .darkMode .toolbar {
  border-bottom-color: #334155;
  background-color: #1e293b;
}

.button {
  padding: 6px 10px;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  background-color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  color: #374151;
}

/* Dark mode for buttons */
:global(.dark) .button, .darkMode .button {
  border-color: #475569;
  background-color: #334155;
  color: #e2e8f0;
}

.button:hover {
  background-color: #f1f5f9;
  border-color: #9ca3af;
}

/* Dark mode for button hover */
:global(.dark) .button:hover, .darkMode .button:hover {
  background-color: #475569;
  border-color: #64748b;
}

.button:active {
  background-color: #e2e8f0;
  transform: translateY(1px);
}

/* Dark mode for button active */
:global(.dark) .button:active, .darkMode .button:active {
  background-color: #475569;
}

.content {
  flex-grow: 1;
  min-height: 200px;
  height: calc(100% - 50px); /* Adjusted to account for toolbar height */
  padding: 12px 16px;
  outline: none;
  overflow-y: auto;
  line-height: 1.5;
  font-size: 15px;
  color: #374151;
  box-sizing: border-box;
  display: block; /* Ensure proper display */
  width: 100%; /* Ensure full width */
  background-color: white;
}

/* Dark mode for content */
:global(.dark) .content, .darkMode .content {
  color: #e2e8f0;
  background-color: #1e293b;
}

/* Focus state */
.container:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Dark mode focus state */
:global(.dark) .container:focus-within, .darkMode.container:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Placeholder */
.content:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  pointer-events: none;
  display: block;
  opacity: 0.8;
}

/* Dark mode placeholder */
:global(.dark) .content:empty:before, .darkMode .content:empty:before {
  color: #64748b;
}

/* Height variants */
.h48 {
  height: 12rem;
}

.h48 .content {
  min-height: 12rem;
}

.h96 {
  height: 24rem;
}

.h96 .content {
  min-height: 24rem;
}

/* Custom height support */
.customHeight {
  /* Height will be set inline */
}

.customHeight .content {
  /* Content height will adjust automatically */
  height: calc(100% - 50px);
}

/* Styled content */
.content h1,
.content h2,
.content h3 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.content h2 {
  font-size: 1.5em;
}

.content p {
  margin-bottom: 0.75em;
}

.content ul,
.content ol {
  margin-left: 1.5em;
  margin-bottom: 0.75em;
}

.content li {
  margin-bottom: 0.25em;
}

.content a {
  color: #2563eb;
  text-decoration: underline;
}

/* Dark mode for links */
:global(.dark) .content a, .darkMode .content a {
  color: #60a5fa;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .toolbar {
    padding: 6px;
  }

  .button {
    padding: 4px 8px;
    font-size: 12px;
    min-width: 28px;
  }

  .content {
    padding: 10px;
    font-size: 14px;
  }
}