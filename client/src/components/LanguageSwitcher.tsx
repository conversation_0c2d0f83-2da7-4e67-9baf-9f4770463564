import { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import FlagIcon from './icons/FlagIcon';

// Define language options with their details
const languages = [
  {
    code: 'vi',
    flagCode: 'vn',
    shortName: 'VI'
  },
  {
    code: 'en',
    flagCode: 'gb',
    shortName: 'EN'
  }
];

const LanguageSwitcher = () => {
  const { i18n } = useTranslation('common');
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get current language details
  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];

  // Log current language for debugging
  console.log('Current language:', i18n.language);

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div
      className="relative"
      ref={dropdownRef}
    >
      <button
        type="button"
        className="inline-flex items-center justify-center rounded-md px-2.5 py-1.5 text-sm font-medium text-slate-900 hover:text-blue-500 focus:outline-none transition-all duration-200 dark:text-white dark:hover:text-blue-400"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <span className="mr-1.5">
          <FlagIcon country={currentLanguage.flagCode as 'gb' | 'vn'} />
        </span>
        <span>{currentLanguage.shortName}</span>
      </button>

      {isOpen && (
        <div
          className="absolute right-0 mt-1 w-24 origin-top-right rounded-md border border-gray-200 focus:outline-none z-50 transform opacity-100 scale-100 transition-all duration-200 bg-white dark:bg-slate-800 dark:border-slate-700"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="language-menu-button"
        >
          <div className="py-1" role="none">
            {languages.map((language) => (
              <button
                key={language.code}
                className={`flex w-full items-center px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 dark:text-white dark:hover:bg-slate-700 ${
                  i18n.language === language.code
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                    : ''
                }`}
                onClick={() => changeLanguage(language.code)}
                role="menuitem"
              >
                <span className="mr-2">
                  <FlagIcon country={language.flagCode as 'gb' | 'vn'} />
                </span>
                <span>{language.shortName}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LanguageSwitcher;
