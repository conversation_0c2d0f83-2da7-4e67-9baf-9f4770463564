import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import styles from './TextEditor.module.css';

interface TextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

// A React 19 compatible rich text editor
const TextEditor: React.FC<TextEditorProps> = ({ value, onChange, placeholder, className }) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const { theme } = useTheme();
  const { t } = useTranslation(['common', 'editor']);
  const isDarkMode = theme === 'dark';

  // Initialize editor content
  useEffect(() => {
    if (editorRef.current && !isInitialized) {
      editorRef.current.innerHTML = value || '';
      setIsInitialized(true);
    }
  }, [value, isInitialized]);

  // Update content when value prop changes
  useEffect(() => {
    if (editorRef.current && isInitialized && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value || '';
    }
  }, [value, isInitialized]);

  // Handle editor input and emit changes
  const handleInput = () => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  };

  // Apply formatting to selected text
  const formatText = (command: string, value: string = '') => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
      editorRef.current.focus();
    }
  };

  // Parse the className to extract height classes
  const getHeightClass = () => {
    if (!className) return '';
    if (className.includes('h-48')) return styles.h48;
    if (className.includes('h-96')) return styles.h96;

    // Extract any custom height class (format: h-[number])
    const heightMatch = className.match(/h-(\d+)/);
    if (heightMatch && heightMatch[1]) {
      // If we have a custom height, create an inline style
      return styles.customHeight;
    }

    return '';
  };

  const heightClass = getHeightClass();

  // Calculate inline style for custom heights
  const getInlineStyle = () => {
    if (!className) return {};

    // Handle Tailwind height classes
    const heightMatch = className.match(/h-(\d+)/);
    if (heightMatch && heightMatch[1] && !className.includes('h-48') && !className.includes('h-96')) {
      const heightValue = parseInt(heightMatch[1]);
      return {
        height: `${heightValue / 4}rem`,
        minHeight: `${heightValue / 4}rem` // Add minHeight for better compatibility
      };
    }

    // Default height for h-96 class if it's not being properly applied by CSS
    if (className.includes('h-96')) {
      return {
        height: '24rem',
        minHeight: '24rem'
      };
    }

    return {};
  };

  return (
    <div ref={containerRef} className={`${styles.container} ${heightClass} ${isDarkMode ? styles.darkMode : ''}`} style={getInlineStyle()} data-dark-mode={isDarkMode}>
      <div className={styles.toolbar}>
        {/* Text formatting */}
        <button type="button" className={styles.button} onClick={() => formatText('bold')} title={t('editor.bold', 'Bold')}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>
            <path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>
          </svg>
        </button>
        <button type="button" className={styles.button} onClick={() => formatText('italic')} title={t('editor.italic', 'Italic')}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="19" y1="4" x2="10" y2="4"></line>
            <line x1="14" y1="20" x2="5" y2="20"></line>
            <line x1="15" y1="4" x2="9" y2="20"></line>
          </svg>
        </button>
        <button type="button" className={styles.button} onClick={() => formatText('underline')} title={t('editor.underline', 'Underline')}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"></path>
            <line x1="4" y1="21" x2="20" y2="21"></line>
          </svg>
        </button>

        {/* Headings */}
        <button type="button" className={styles.button} onClick={() => formatText('formatBlock', '<h1>')} title={t('editor.heading1', 'Heading 1')}>
          H1
        </button>
        <button type="button" className={styles.button} onClick={() => formatText('formatBlock', '<h2>')} title={t('editor.heading2', 'Heading 2')}>
          H2
        </button>

        {/* Lists */}
        <button type="button" className={styles.button} onClick={() => formatText('insertUnorderedList')} title={t('editor.bulletList', 'Bullet List')}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="8" y1="6" x2="21" y2="6"></line>
            <line x1="8" y1="12" x2="21" y2="12"></line>
            <line x1="8" y1="18" x2="21" y2="18"></line>
            <line x1="3" y1="6" x2="3.01" y2="6"></line>
            <line x1="3" y1="12" x2="3.01" y2="12"></line>
            <line x1="3" y1="18" x2="3.01" y2="18"></line>
          </svg>
        </button>
        <button type="button" className={styles.button} onClick={() => formatText('insertOrderedList')} title={t('editor.numberedList', 'Numbered List')}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="10" y1="6" x2="21" y2="6"></line>
            <line x1="10" y1="12" x2="21" y2="12"></line>
            <line x1="10" y1="18" x2="21" y2="18"></line>
            <path d="M4 6h1v4"></path>
            <path d="M4 10h2"></path>
            <path d="M6 18H4c0-1 2-2 2-3s-1-1.5-2-1"></path>
          </svg>
        </button>

        {/* Link */}
        <button
          type="button"
          className={styles.button}
          onClick={() => formatText('createLink', prompt(t('editor.enterLinkUrl', 'Enter link URL'), 'http://') || '')}
          title={t('editor.insertLink', 'Insert Link')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
          </svg>
        </button>

        {/* Paragraphs and alignment */}
        <button type="button" className={styles.button} onClick={() => formatText('formatBlock', '<p>')} title={t('editor.paragraph', 'Paragraph')}>
          ¶
        </button>
        <button type="button" className={styles.button} onClick={() => formatText('justifyLeft')} title={t('editor.alignLeft', 'Align Left')}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="17" y1="10" x2="3" y2="10"></line>
            <line x1="21" y1="6" x2="3" y2="6"></line>
            <line x1="21" y1="14" x2="3" y2="14"></line>
            <line x1="17" y1="18" x2="3" y2="18"></line>
          </svg>
        </button>
        <button type="button" className={styles.button} onClick={() => formatText('justifyCenter')} title={t('editor.alignCenter', 'Align Center')}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="18" y1="10" x2="6" y2="10"></line>
            <line x1="21" y1="6" x2="3" y2="6"></line>
            <line x1="21" y1="14" x2="3" y2="14"></line>
            <line x1="18" y1="18" x2="6" y2="18"></line>
          </svg>
        </button>
      </div>

      <div
        ref={editorRef}
        className={styles.content}
        contentEditable
        onInput={handleInput}
        data-placeholder={placeholder || t('editor.placeholder', 'Enter content here...')}
        suppressContentEditableWarning={true}
        aria-label={t('editor.contentArea', 'Content editing area')}
      />
    </div>
  );
};

export default TextEditor;