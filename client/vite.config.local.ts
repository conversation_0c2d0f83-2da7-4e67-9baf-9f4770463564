import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// Use localhost:5000 for local development
const apiTarget = 'http://localhost:5000'

console.log(`Using API target: ${apiTarget}`)

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: true,
    port: 3000,
    proxy: {
      '/api': {
        target: apiTarget,
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log(`Sending Request to ${apiTarget}:`, req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log(`Received Response from ${apiTarget}:`, req.url, proxyRes.statusCode);
          });
        }
      }
    }
  },
  define: {
    'process.env': {}
  }
}) 