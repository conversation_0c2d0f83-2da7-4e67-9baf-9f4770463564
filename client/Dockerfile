FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Accept build argument for API URL
ARG VITE_API_URL=https://server-nest-production.up.railway.app/api

# Set environment variable for build process
ENV VITE_API_URL=$VITE_API_URL

# Build the application with environment variable
RUN npm run build

# Set default port for runtime
ENV PORT=8080

# Expose port
EXPOSE $PORT

# Use npm start (which calls serve via npx)
CMD ["npm", "start"]