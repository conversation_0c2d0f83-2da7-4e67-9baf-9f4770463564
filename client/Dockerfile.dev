FROM node:20-alpine

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy the rest of the code
COPY . .

# Set environment variable to indicate we're in a container
ENV CONTAINER=true

# Expose the port
EXPOSE 3000

# Start the app in development mode with hot reloading
CMD ["npm", "run", "dev", "--", "--host", "--port", "3000"]