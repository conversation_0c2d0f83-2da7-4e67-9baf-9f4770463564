{"list": {"title": "Tests List", "createNew": "Create New Test", "noTests": "No tests available", "createFirst": "Create your first test", "showingYourTests": "Showing only your tests", "columns": {"title": "Title", "type": "Type", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "Category", "creator": "Creator", "published": "Published", "status": "Status", "actions": "Actions"}, "actions": {"view": "View", "edit": "Edit", "publish": "Publish", "unpublish": "Unpublish", "activate": "Activate", "deactivate": "Deactivate", "delete": "Delete", "take": "Take Test"}, "status": {"published": "Published", "draft": "Draft", "active": "Active", "inactive": "Inactive"}, "confirmDelete": "Are you sure you want to delete this test?"}, "create": {"title": "Create New Test", "success": "Test created successfully", "error": "Could not create test"}, "edit": {"title": "Edit Test", "success": "Test updated successfully", "error": "Could not update test"}, "detail": {"title": "Test Details", "generalInfo": "General Information", "creatorInfo": "Creator & Time", "description": "Description", "instructions": "Instructions", "structure": "Test Structure", "noStructure": "Test has no structure yet", "section": "Section", "questionCount": "Question count", "noDescription": "No description available", "noInstructions": "No instructions available", "createdBy": "Created by", "createdAt": "Created at", "updatedAt": "Last updated", "unknown": "Unknown"}, "take": {"title": "Take Test", "timeRemaining": "Time remaining", "submit": "Submit", "submitTest": "Submit Test", "confirmSubmit": "Are you sure you want to submit?", "submitConfirmation": "Are you sure you want to submit your test? This action cannot be undone.", "testContent": "Test Content", "answerSheet": "Answer Sheet", "progressSummary": "Progress Summary", "totalQuestions": "Total Questions", "answered": "Answered", "multipleChoice": "Multiple Choice Questions", "fillInTheBlank": "Fill-in-the-Blank Questions", "flag": "Flag", "unflag": "Unflag", "flagged": "Flagged", "question": "Question", "yourAnswer": "Your answer...", "notAnswered": "Not answered", "submitSuccess": "Test submitted successfully", "submitError": "Could not submit test"}, "result": {"title": "Test Results", "summary": "Test Summary", "score": "Score", "totalScore": "Total Score", "passedQuestions": "Correct Answers", "totalQuestions": "Total Questions", "timeTaken": "Time Taken", "reviewAnswers": "Answer Summary", "testContent": "Test Content", "correctAnswer": "Correct Answer", "yourAnswer": "Your Answer", "yourScore": "Your Score", "correct": "Correct", "incorrect": "Incorrect Answers", "notAnswered": "No answers provided for this test", "notAvailable": "Not available", "history": "Test Result History", "historyDescription": "View your previous attempts for this test.", "allHistoryDescription": "View all your test results.", "date": "Date", "viewHistory": "View History", "otherResults": "Other Results", "allTestResults": "All Test Results", "testName": "Test Name", "unknownTest": "Unknown Test", "noResults": "You have not taken any tests yet.", "timeFormat": "{{minutes}}m {{seconds}}s"}, "fields": {"title": "Title", "description": "Description", "content": "Content", "type": "Test Type", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "Category", "timeLimit": "Time Limit", "minutes": "minutes", "maxScore": "Maximum Score", "points": "points", "instructions": "Instructions", "answerSheet": "Answer Sheet", "multipleChoiceQuestions": "Multiple Choice Questions", "fillInBlankQuestions": "Fill-in-the-Blank Questions", "addQuestions": "Add Questions", "addQuestion": "Add Question", "questionNumber": "Question #", "correctOption": "Correct Option", "acceptableAnswers": "Acceptable Answers", "acceptableAnswer": "Acceptable Answer", "addAlternateAnswer": "Add Alternate Answer", "caseSensitive": "Case Sensitive", "remove": "Remove", "sortByNumber": "Sort by number"}, "types": {"reading": "Reading", "listening": "Listening", "writing": "Writing", "speaking": "Speaking", "fullTest": "Full Test"}, "difficulties": {"easy": "Easy", "medium": "Medium", "hard": "Hard"}, "validation": {"titleRequired": "Title is required", "categoryRequired": "Category is required", "timeLimitRequired": "Time limit is required", "maxScoreRequired": "Maximum score is required", "contentRequired": "Content is required", "correctAnswerRequired": "Correct answer is required", "invalidQuestionNumbers": "Please enter valid question numbers (greater than 0) for all questions", "duplicateQuestionNumbers": "Duplicate question numbers found"}, "import": {"multipleChoiceTitle": "Import Multiple Choice Answers", "fillInBlankTitle": "I<PERSON>rt Fill in the Blank Answers", "multipleChoiceFormat": "Format: One answer per line, e.g., '1. A' or '2. B'", "fillInBlankFormat": "Format: One answer per line, e.g., '1. Answer' or '2. Answer, Alt Answer'", "parseButton": "Parse Answers", "parseErrors": "Error parsing answers", "noValidAnswers": "No valid answers found in the input", "allDuplicates": "All imported questions already exist", "multipleChoicePlaceholder": "1. A\n2. B\n3. C\n4. D", "fillInBlankPlaceholder": "1. Answer 1\n2. Answer 2, Alt Answer\n3. Answer 3"}, "createdOn": "Created on {{date}}"}