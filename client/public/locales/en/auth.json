{"login": {"title": "<PERSON><PERSON>", "email": "Email", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "noAccount": "Don't have an account?", "signUp": "Sign up now", "loginButton": "<PERSON><PERSON>", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed"}, "register": {"title": "Register", "name": "Full name", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm password", "role": "Role", "student": "Student", "teacher": "Teacher", "hasAccount": "Already have an account?", "signIn": "Sign in", "registerButton": "Register", "registerSuccess": "Registration successful", "registerError": "Registration failed"}, "forgotPassword": {"title": "Forgot Password", "email": "Email", "resetButton": "Reset Password", "backToLogin": "Back to login", "resetSuccess": "Password reset request has been sent", "resetError": "Could not reset password"}, "resetPassword": {"title": "Reset Password", "newPassword": "New password", "confirmPassword": "Confirm new password", "resetButton": "Reset Password", "resetSuccess": "Password has been reset successfully", "resetError": "Could not reset password"}, "logout": {"logoutSuccess": "Logout successful"}, "validation": {"emailRequired": "Email is required", "emailInvalid": "<PERSON><PERSON> is invalid", "passwordRequired": "Password is required", "passwordLength": "Password must be at least 6 characters", "passwordMismatch": "Passwords do not match", "nameRequired": "Full name is required"}}