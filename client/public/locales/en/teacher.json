{"groups": {"title": "Student Groups", "createNew": "Create New Group", "createGroup": "Create Group", "editGroup": "Edit Group", "name": "Name", "level": "Level", "description": "Description", "students": "Students", "tests": "Tests", "status": "Status", "createdAt": "Created Date", "isActive": "Active", "group": "Group", "generateInvitationLink": "Generate Invitation Link", "invitationLink": "Invitation Link", "shareThisLink": "Share this link with your students", "expirationDays": "Expiration Period", "oneDay": "1 Day", "threeDays": "3 Days", "oneWeek": "1 Week", "twoWeeks": "2 Weeks", "oneMonth": "1 Month", "limitNumberOfUses": "Limit number of uses", "maxUses": "Maximum Uses", "enterMaxUses": "Enter maximum number of uses", "generateLink": "Generate Link", "inviteLinkGenerated": "Invitation link generated successfully", "linkCopied": "Link copied to clipboard", "inviteDetails": "Invitation Details", "created": "Created", "expires": "Expires", "currentUses": "Current Uses", "searchPlaceholder": "Search by name or description", "showInactive": "Show inactive groups", "noGroups": "No groups found", "createFirst": "Create your first group", "deleteSuccess": "Group deleted successfully", "confirmDelete": "Are you sure you want to delete this group?", "createSuccess": "Group created successfully", "updateSuccess": "Group updated successfully", "groupNotFound": "Group not found", "backToGroups": "Back to groups", "studentsInGroup": "Students in Group", "studentsAdded": "Students added successfully", "studentRemoved": "Student removed successfully", "confirmRemoveStudent": "Are you sure you want to remove this student from the group?", "noStudentsSelected": "No students selected", "addStudents": "Add Students", "searchStudents": "Search students by name or email", "noStudentsFound": "No students found", "noStudentsAvailable": "No students available to add", "addSelected": "Add Selected", "noStudentsInGroup": "No students in this group", "email": "Email", "joinedAt": "Joined At", "assignTest": "Assign Test", "selectTest": "Select Test", "dueDate": "Due Date", "testAssigned": "Test assigned successfully", "testRemoved": "Test removed successfully", "confirmRemoveTest": "Are you sure you want to remove this test from the group?", "noTestSelected": "No test selected", "assignedTests": "Assigned Tests", "noTestsAssigned": "No tests assigned to this group", "testTitle": "Test Title", "type": "Type", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assignedAt": "Assigned At", "viewResults": "View Results", "viewAnalysis": "View Analysis", "results": "Results", "studentCount": "Student Count", "averageScore": "Average Score", "completionRate": "Completion Rate", "studentPerformance": "Student Performance", "testsCompleted": "Tests Completed", "backToGroup": "Back to Group", "backToResults": "Back to Results", "studentDetails": "Student Details", "testsAssigned": "Tests Assigned", "comparisonToGroup": "Comparison to Group", "testResults": "Test Results", "completed": "Completed", "pending": "Pending", "score": "Score", "noTestResults": "No test results available", "failedToLoadStudentResults": "Failed to load student results", "noDeadline": "No deadline", "placeholderCompletionRate": "50%"}, "students": {"studentProfile": "Student Profile", "groupMemberships": "Group Memberships", "noGroupMemberships": "This student is not a member of any groups", "addToGroup": "Add to Group", "selectGroup": "Select a group", "selectGroupFirst": "Please select a group first", "addToSelectedGroup": "Add to Selected Group", "removedFromGroup": "Student removed from group successfully", "addedToGroup": "Student added to group successfully", "confirmRemoveFromGroup": "Are you sure you want to remove this student from the group?", "removeFromGroup": "Remove Student from Group", "studentNotFound": "Student not found", "testResults": "Test Results", "testResultsComingSoon": "Detailed test results will be available soon", "joinedAt": "Joined At"}, "tests": {"listView": "List View", "calendarView": "Calendar View", "batchAssign": "<PERSON><PERSON>", "noTestsSelected": "No tests selected", "batchAssignSuccess": "{{count}} tests assigned successfully", "selectTests": "Select Tests", "selectMultipleTests": "Select multiple tests to assign", "batchAssignTests": "<PERSON><PERSON> Assign Tests", "cancel": "Cancel", "assign": "Assign", "setDueDate": "Set Due Date", "testStatistics": "Test Statistics", "assignmentCalendar": "Assignment Calendar", "viewByMonth": "View by Month", "today": "Today", "tests": "tests", "testType": {"reading": "Reading", "listening": "Listening", "writing": "Writing", "speaking": "Speaking"}}, "questionAnalysis": {"title": "Question Analysis", "question": "Question", "questionPrefix": "Question {{number}}", "type": "Type", "correctAnswer": "Correct Answer", "correct": "Correct", "incorrect": "Incorrect", "skipped": "Skipped", "incorrectAnswers": "Incorrect Answers", "emptyAnswer": "Empty Answer", "multipleChoice": "Multiple Choice", "fillInBlank": "Fill in the Blank", "multipleChoiceQuestions": "Multiple Choice Questions", "fillInBlankQuestions": "Fill in the Blank Questions", "mostProblematicQuestions": "Most Problematic Questions", "incorrectRate": "Incorrect Rate", "completedCount": "Completed Count", "completionRate": "Completion Rate", "dataNotFound": "Test or group data not found", "option": "Option", "percentSymbol": "%"}}