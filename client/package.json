{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:local": "vite --config vite.config.local.ts", "build": "vite build", "build:check": "tsc -b --noEmit", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start": "npx serve -s dist -l ${PORT:-8080}", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=src/__tests__/.*\\.test\\.tsx?$", "test:e2e": "jest --testPathPattern=src/__tests__/e2e/.*\\.e2e\\.test\\.js$", "test:all": "node src/__tests__/run-tests.js"}, "dependencies": {"@headlessui/react": "^2.2.2", "@radix-ui/react-icons": "^1.3.2", "@tanstack/react-query": "^5.69.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.0.1", "i18next-browser-languagedetector": "^8.0.5", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.483.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-quill": "^2.0.0", "react-router-dom": "^7.4.0", "react-toastify": "^11.0.5", "serve": "^14.2.4", "tailwind-merge": "^3.0.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/forms": "^0.5.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/morgan": "^1.9.9", "@types/puppeteer": "^5.4.7", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.17", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-puppeteer": "^11.0.0", "postcss": "^8.4.35", "puppeteer": "^24.4.0", "tailwind-scrollbar": "^3.0.0", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}