import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// Use server container name in Docker, or localhost for local dev
const isContainer = process.env.CONTAINER === 'true';
const apiTarget = isContainer ? 'http://server:5000' : 'http://localhost:5000';

console.log(`Using API target: ${apiTarget}`);
console.log(`Running in container: ${isContainer}`);

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: true,
    port: 3000,
    proxy: {
      '/api': {
        target: apiTarget,
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('🔴 Proxy error:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log(`🔄 Proxy Request: ${req.method} ${req.url} -> ${apiTarget}${req.url}`);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log(`✅ Proxy Response: ${req.url} Status: ${proxyRes.statusCode}`);
          });
        }
      }
    }
  },
  define: {
    'process.env': {}
  }
})
