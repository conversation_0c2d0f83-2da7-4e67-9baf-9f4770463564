#!/bin/bash

# Railway Deployment Setup Script
# This script helps you deploy the IELTS Toolkit to Railway

set -e

echo "🚂 Railway Deployment Setup for IELTS Toolkit"
echo "=============================================="

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "📦 Installing Railway CLI..."
    npm install -g @railway/cli
fi

echo "🔐 Please login to Railway..."
railway login

echo "📁 Creating new Railway project..."
railway init

echo "🗄️  Setting up PostgreSQL database..."
echo "Please go to your Railway dashboard and:"
echo "1. Click 'New Service' → 'Database' → 'PostgreSQL'"
echo "2. Note the DATABASE_URL that Railway generates"
echo ""
read -p "Press Enter when you've created the PostgreSQL service..."

echo "🔧 Setting up backend service..."
cd server-nest
railway init
echo "Please configure the following environment variables in Railway dashboard:"
echo "- DATABASE_URL (auto-populated from PostgreSQL service)"
echo "- JWT_SECRET (generate a secure random string)"
echo "- NODE_ENV=production"
echo "- PORT=8080"
echo ""
read -p "Press Enter when you've configured the backend environment variables..."

echo "🚀 Deploying backend..."
railway up

echo "🎨 Setting up frontend service..."
cd ../client
railway init
echo "Please configure the following environment variable in Railway dashboard:"
echo "- VITE_API_URL=https://server-nest.railway.internal/api"
echo ""
read -p "Press Enter when you've configured the frontend environment variables..."

echo "🚀 Deploying frontend..."
railway up

echo "🗃️  Running database migrations..."
cd ../server-nest
railway run npm run prisma:deploy

echo "✅ Deployment complete!"
echo ""
echo "🌐 Your application should now be available at:"
echo "Frontend: Check your Railway dashboard for the frontend URL"
echo "Backend: Check your Railway dashboard for the backend URL"
echo ""
echo "💰 Estimated monthly cost: $5-15"
echo "📊 Monitor your services in the Railway dashboard"
