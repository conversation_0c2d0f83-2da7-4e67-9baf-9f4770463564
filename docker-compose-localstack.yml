services:
  localstack:
    container_name: localstack-terraform
    image: localstack/localstack:latest
    ports:
      - "4566:4566"            # LocalStack Gateway
      - "4510-4559:4510-4559"  # external services port range
    environment:
      - DEBUG=1
      - DOCKER_HOST=unix:///var/run/docker.sock
      - HOSTNAME_EXTERNAL=localstack
      - SERVICES=s3,iam,sts
      - DEFAULT_REGION=us-east-1
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
    volumes:
      - "${TMPDIR:-/tmp}/localstack:/tmp/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
    networks:
      - localstack-network

  # Fake GCS server to emulate Google Cloud Storage
  fake-gcs-server:
    container_name: fake-gcs-server
    image: fsouza/fake-gcs-server
    ports:
      - "4443:4443"
    command: ["-scheme", "http", "-port", "4443", "-public-host", "localhost:4443"]
    volumes:
      - "${TMPDIR:-/tmp}/fake-gcs-server:/data"
    networks:
      - localstack-network

networks:
  localstack-network:
    driver: bridge
